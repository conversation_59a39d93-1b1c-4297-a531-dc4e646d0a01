import asyncio
import pytest

import embedding_processor.calculator as calc_module
from embedding_processor.calculator import EmbeddingCalculator
from utils.models import CodeChunk


class FakeManagerOk:
    def __init__(self):
        self.calls = []

    async def batch_embedding(self, texts, model_type=None):
        self.calls.append(len(texts))
        # 返回与输入顺序一致的简单向量（每个文本长度编码到向量中）
        return [[float(len(t))] for t in texts]


class FakeManagerFail:
    async def batch_embedding(self, texts, model_type=None):
        raise Exception("batch error")


@pytest.mark.asyncio
async def test_batch_calculate_embeddings_happy_path(monkeypatch):
    # 准备：固定批大小为10
    old_batch_size = calc_module.EMBEDDING_CONFIG["batch_size"]
    calc_module.EMBEDDING_CONFIG["batch_size"] = 10
    fake_manager = FakeManagerOk()
    monkeypatch.setattr(calc_module, "model_manager", fake_manager, raising=True)

    try:
        calc = EmbeddingCalculator()
        texts = [f"text-{i}" for i in range(23)]
        embs = await calc.batch_calculate_embeddings(texts)

        assert len(embs) == len(texts)
        # 验证分批调用次数与每批大小（10,10,3）
        assert fake_manager.calls == [10, 10, 3]
        # 顺序不变：向量值等于对应文本长度
        for i, t in enumerate(texts):
            assert embs[i] == [float(len(t))]
    finally:
        calc_module.EMBEDDING_CONFIG["batch_size"] = old_batch_size


@pytest.mark.asyncio
async def test_batch_calculate_embeddings_fallback_to_single(monkeypatch):
    # 让批量接口报错，触发逐条回退
    monkeypatch.setattr(calc_module, "model_manager", FakeManagerFail(), raising=True)

    calc = EmbeddingCalculator()

    # 将单条计算桩替换为固定返回，避免真实外部请求
    async def fake_calc(self, text: str):
        return [1.0, 2.0]

    monkeypatch.setattr(EmbeddingCalculator, "calculate_embedding", fake_calc, raising=True)

    texts = ["a", "bb", "ccc", "dddd"]
    embs = await calc.batch_calculate_embeddings(texts)
    assert len(embs) == len(texts)
    for e in embs:
        assert e == [1.0, 2.0]


@pytest.mark.asyncio
async def test_update_chunk_embeddings_alignment(monkeypatch):
    # 模拟批量接口返回的数量少于输入数量（只返回前3个）
    class FakeManagerShort:
        async def batch_embedding(self, texts, model_type=None):
            return [[0.5]] * 3

    monkeypatch.setattr(calc_module, "model_manager", FakeManagerShort(), raising=True)

    calc = EmbeddingCalculator()
    # 减小维度以加速测试中的零向量构造
    calc.embedding_dimension = 3

    chunks = [
        CodeChunk(
            id=f"c{i}",
            file_path="/p",
            relative_path="p",
            start_line=1,
            end_line=2,
            content=f"code-{i}",
            chunk_type="function",
            symbol_name="f",
            language="python",
        )
        for i in range(5)
    ]

    updated = await calc.update_chunk_embeddings(chunks)
    # 前3个为0.5，其余被对齐为零向量
    for i, ch in enumerate(updated):
        assert ch.embedding is not None
        if i < 3:
            assert ch.embedding == [0.5]
        else:
            assert ch.embedding == [0.0, 0.0, 0.0]


