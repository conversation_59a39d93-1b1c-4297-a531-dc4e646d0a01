"""
查询重写器测试
"""

import pytest
from unittest.mock import AsyncMock, patch

from retrieval_service.query_rewriter import QueryRewriter


@pytest.fixture
def rewriter():
    return QueryRewriter()


def test_extract_keywords_from_query(rewriter):
    query = "如何实现用户认证 authentication function"
    keywords = rewriter._extract_keywords_from_query(query)
    assert isinstance(keywords, list)
    assert "authentication" in keywords
    assert "function" in keywords
    assert "用户认证" in keywords
    assert "如何" not in keywords
    assert "实现" not in keywords


def test_rule_based_rewrite(rewriter):
    query = "用户登录函数 login function"
    patterns = rewriter._rule_based_rewrite(query)
    assert isinstance(patterns, list)
    assert len(patterns) > 0
    assert any("def" in p or "function" in p for p in patterns)


def test_rule_based_rewrite_class(rewriter):
    query = "用户管理类 UserManager class"
    patterns = rewriter._rule_based_rewrite(query)
    assert any("class" in p for p in patterns)


def test_rewrite_to_embedding_query(rewriter):
    query = "如何实现用户认证功能？"
    embedding_query = rewriter.rewrite_to_embedding_query(query)
    assert isinstance(embedding_query, str)
    assert "如何" not in embedding_query
    assert "用户认证" in embedding_query
    assert "功能" in embedding_query


def test_generate_fallback_patterns(rewriter):
    query = "getUserData function implementation"
    patterns = rewriter.generate_fallback_patterns(query)
    assert isinstance(patterns, list)
    assert len(patterns) > 0
    assert "getUserData" in patterns
    assert "function" in patterns


@pytest.mark.asyncio
@patch("retrieval_service.query_rewriter.model_manager")
async def test_rewrite_to_ripgrep_with_llm(mock_model_manager, rewriter):
    mock_model_manager.chat_completion = AsyncMock(
        return_value="""
def.*login
class.*User
login.*function
        """
    )
    repo_insight = "这是一个用户管理系统，包含登录和认证功能。"
    query = "用户登录功能"
    patterns = await rewriter.rewrite_to_ripgrep(query, repo_insight)
    assert isinstance(patterns, list)
    assert len(patterns) > 0
    mock_model_manager.chat_completion.assert_called_once()


@pytest.mark.asyncio
@patch("retrieval_service.query_rewriter.model_manager")
async def test_rewrite_to_ripgrep_fallback(mock_model_manager, rewriter):
    mock_model_manager.chat_completion = AsyncMock(side_effect=Exception("LLM调用失败"))
    repo_insight = "测试仓库"
    query = "用户登录功能"
    patterns = await rewriter.rewrite_to_ripgrep(query, repo_insight)
    assert isinstance(patterns, list)
    assert len(patterns) > 0


def test_parse_ripgrep_response(rewriter):
    response = """
1. def.*login.*function
2. class.*User.*Manager
3. login.*authentication
    """
    patterns = rewriter._parse_ripgrep_response(response)
    assert len(patterns) == 3
    assert "def.*login.*function" in patterns
    assert "class.*User.*Manager" in patterns
    assert "login.*authentication" in patterns
