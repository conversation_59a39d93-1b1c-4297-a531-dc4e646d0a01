import asyncio
import unittest

from symbol_processor.symbol_index import SymbolIndex
from symbol_processor.symbol_retriever import SymbolRetriever
from utils.models import (
    Parameter,
    Reference,
    ReferenceType,
    Symbol,
    SymbolQuery,
    SymbolType,
)


class TestSymbolRetriever(unittest.TestCase):
    def setUp(self):
        self.index = SymbolIndex()

        # 构造一些符号
        s_func_login = Symbol(
            id="s1",
            name="login",
            symbol_type=SymbolType.FUNCTION,
            file_path="/repo/auth/user.py",
            start_line=10,
            end_line=30,
            start_column=0,
            end_column=0,
            module_path="auth/user.py",
            namespace=None,
            parent_symbol=None,
            signature="def login(username, password) -> bool",
            parameters=[Parameter(name="username"), Parameter(name="password")],
            return_type="bool",
            language="python",
        )

        s_method_auth = Symbol(
            id="s2",
            name="authenticate",
            symbol_type=SymbolType.METHOD,
            file_path="/repo/auth/service.ts",
            start_line=5,
            end_line=50,
            start_column=0,
            end_column=0,
            module_path="auth/service.ts",
            namespace="AuthService",
            parent_symbol=None,
            signature="authenticate(user: string, pass: string): boolean",
            language="typescript",
        )

        s_class_user = Symbol(
            id="s3",
            name="UserService",
            symbol_type=SymbolType.CLASS,
            file_path="/repo/auth/service.ts",
            start_line=1,
            end_line=200,
            start_column=0,
            end_column=0,
            module_path="auth/service.ts",
            namespace=None,
            parent_symbol=None,
            signature=None,
            language="typescript",
        )

        # 关系：login 依赖 authenticate
        s_func_login.dependencies = ["s2"]

        # 引用：authenticate 被某处调用（示例）
        s_method_auth.references = [
            Reference(
                file_path="/repo/auth/web.ts",
                line_number=12,
                column_number=3,
                context="authService.authenticate(user, pass)",
                reference_type=ReferenceType.CALL,
            )
        ]

        # 维护父子关系
        s_method_auth.parent_symbol = "s3"

        # 加入索引
        for s in (s_func_login, s_method_auth, s_class_user):
            self.index.add_symbol(s)

        self.retriever = SymbolRetriever(self.index)

    def test_search_by_name(self):
        q = SymbolQuery(query="login", exact_match=True, max_results=10)
        results = asyncio.run(self.retriever.search_symbols(q))
        self.assertGreaterEqual(len(results), 1)
        self.assertEqual(results[0].symbol.name, "login")

    def test_fuzzy_search(self):
        q = SymbolQuery(query="logn", exact_match=False, max_results=10)
        results = asyncio.run(self.retriever.search_symbols(q))
        names = [r.symbol.name for r in results]
        self.assertIn("login", names)

    def test_filter_by_type_and_language(self):
        q = SymbolQuery(
            query="auth",
            symbol_types=[SymbolType.METHOD],
            languages=["typescript"],
            max_results=10,
        )
        results = asyncio.run(self.retriever.search_symbols(q))
        self.assertTrue(all(r.symbol.symbol_type == SymbolType.METHOD for r in results))
        self.assertTrue(all(r.symbol.language == "typescript" for r in results))

    def test_context_filter(self):
        # context_path 命中 module_path 片段
        q = SymbolQuery(
            query="authenticate", context_path="auth/service.ts", max_results=10
        )
        results = asyncio.run(self.retriever.search_symbols(q))
        self.assertTrue(any(r.symbol.name == "authenticate" for r in results))

    def test_dependencies(self):
        deps = asyncio.run(self.retriever.get_symbol_dependencies("s1"))
        ids = [d.id for d in deps]
        self.assertIn("s2", ids)

    def test_references(self):
        refs = asyncio.run(self.retriever.get_symbol_references("s2"))
        self.assertGreaterEqual(len(refs), 1)
        self.assertEqual(refs[0].reference_type.name.lower(), "call")

    def test_find_definition(self):
        sym = asyncio.run(
            self.retriever.find_symbol_definition("authenticate", context_path="auth")
        )
        self.assertIsNotNone(sym)
        self.assertEqual(sym.name, "authenticate")
