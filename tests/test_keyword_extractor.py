"""
关键词提取器测试
"""

import unittest

from insight_generator.keyword_extractor import KeywordExtractor
from utils.models import CodeChunk


class TestKeywordExtractor(unittest.TestCase):
    """关键词提取器测试类"""

    def setUp(self):
        """测试初始化"""
        self.extractor = KeywordExtractor()

        # 创建测试代码分片
        self.python_chunk = CodeChunk(
            id="test_chunk_1",
            file_path="/test/example.py",
            relative_path="example.py",
            start_line=1,
            end_line=5,
            content="""def calculate_user_score(user_data, config_settings):
    total_score = 0
    for item in user_data:
        score = item.get('score', 0)
        total_score += score
    return total_score""",
            chunk_type="function",
            symbol_name="calculate_user_score",
            language="python",
        )

        self.javascript_chunk = CodeChunk(
            id="test_chunk_2",
            file_path="/test/utils.js",
            relative_path="utils.js",
            start_line=10,
            end_line=15,
            content="""function validateUserInput(inputData) {
    if (!inputData || typeof inputData !== 'object') {
        return false;
    }
    return inputData.hasOwnProperty('username');
}""",
            chunk_type="function",
            symbol_name="validateUserInput",
            language="javascript",
        )

    def test_extract_keywords_python(self):
        """测试Python代码关键词提取"""
        keywords = self.extractor.extract_keywords(self.python_chunk)

        # 验证关键词提取结果
        self.assertIsInstance(keywords, list)
        self.assertGreater(len(keywords), 0)

        # 验证包含符号名称相关的关键词
        keyword_str = " ".join(keywords).lower()
        self.assertIn("calculate", keyword_str)
        self.assertIn("user", keyword_str)
        self.assertIn("score", keyword_str)

        # 验证不包含Python关键字
        self.assertNotIn("def", keywords)
        self.assertNotIn("for", keywords)
        self.assertNotIn("return", keywords)

    def test_extract_keywords_javascript(self):
        """测试JavaScript代码关键词提取"""
        keywords = self.extractor.extract_keywords(self.javascript_chunk)

        # 验证关键词提取结果
        self.assertIsInstance(keywords, list)
        self.assertGreater(len(keywords), 0)

        # 验证包含符号名称相关的关键词
        keyword_str = " ".join(keywords).lower()
        self.assertIn("validate", keyword_str)
        self.assertIn("user", keyword_str)
        self.assertIn("input", keyword_str)

        # 验证不包含JavaScript关键字
        self.assertNotIn("function", keywords)
        self.assertNotIn("if", keywords)
        self.assertNotIn("return", keywords)

    def test_extract_file_path_keywords(self):
        """测试文件路径关键词提取"""
        keywords = self.extractor.extract_file_path_keywords(
            "src/utils/user_manager.py"
        )

        # 验证结果
        self.assertIsInstance(keywords, list)
        self.assertGreater(len(keywords), 0)

        # 验证包含路径相关的关键词
        self.assertIn("src", keywords)
        self.assertIn("utils", keywords)
        self.assertIn("user", keywords)
        self.assertIn("manager", keywords)

    def test_split_identifier(self):
        """测试标识符分割"""
        # 测试驼峰命名
        camel_keywords = self.extractor._split_identifier("getUserName")
        self.assertIn("get", camel_keywords)
        self.assertIn("user", camel_keywords)
        self.assertIn("name", camel_keywords)

        # 测试下划线命名
        snake_keywords = self.extractor._split_identifier("user_profile_data")
        self.assertIn("user", snake_keywords)
        self.assertIn("profile", snake_keywords)
        self.assertIn("data", snake_keywords)

    def test_filter_keywords(self):
        """测试关键词过滤"""
        raw_keywords = {"def", "user", "calculate", "for", "score", "a", "123"}

        filtered = self.extractor._filter_keywords(raw_keywords, "python")

        # 验证过滤结果
        self.assertNotIn("def", filtered)  # Python关键字被过滤
        self.assertNotIn("for", filtered)  # Python关键字被过滤
        self.assertNotIn("a", filtered)  # 太短的词被过滤
        self.assertNotIn("123", filtered)  # 纯数字被过滤

        self.assertIn("user", filtered)
        self.assertIn("calculate", filtered)
        self.assertIn("score", filtered)

    def test_sort_keywords_by_importance(self):
        """测试关键词重要性排序"""
        keywords = ["helper", "calculate", "user", "data"]

        # 使用包含'calculate'和'user'的分片测试
        sorted_keywords = self.extractor._sort_keywords_by_importance(
            keywords, self.python_chunk
        )

        # 验证排序结果
        self.assertEqual(len(sorted_keywords), len(keywords))

        # 符号名称中的关键词应该排在前面
        self.assertIn("calculate", sorted_keywords[:2])
        self.assertIn("user", sorted_keywords[:2])

    def test_update_chunk_keywords(self):
        """测试批量更新分片关键词"""
        chunks = [self.python_chunk, self.javascript_chunk]

        updated_chunks = self.extractor.update_chunk_keywords(chunks)

        # 验证更新结果
        self.assertEqual(len(updated_chunks), 2)

        for chunk in updated_chunks:
            self.assertIsNotNone(chunk.keywords)
            self.assertIsInstance(chunk.keywords, list)
            self.assertGreater(len(chunk.keywords), 0)


if __name__ == "__main__":
    unittest.main()
