import asyncio
import httpx
import time
import sys
import platform
import pytest
import pytest_asyncio

# ------------ 系统优化建议（Windows） ------------
def tune_windows_tcp():
    if sys.platform.startswith("win"):
        print("\n💡 [优化建议] Windows TCP 参数可以调整以提升并发：")
        print("  1. 增加可用端口数量：")
        print("     netsh int ipv4 set dynamicport tcp start=10000 num=55000")
        print("  2. 缩短 TIME_WAIT 时间：")
        print('     reg add "HKLM\\SYSTEM\\CurrentControlSet\\Services\\Tcpip\\Parameters" /v TcpTimedWaitDelay /t REG_DWORD /d 30 /f')
        print("  3. 修改后需要重启系统生效\n")

# ------------ 测试夹具（fixtures） ------------
@pytest.fixture(params=[20, 40, 80])
def max_concurrent(request):
    """参数化并发度，避免硬编码循环，同时让用例更快完成。"""
    return request.param


@pytest_asyncio.fixture
async def client():
    """使用 httpx.MockTransport 模拟服务端延迟，避免外网依赖与 TLS 开销。"""
    limits = httpx.Limits(
        max_connections=200,
        max_keepalive_connections=50,
    )

    async def _handler(request: httpx.Request) -> httpx.Response:
        # 模拟 50ms 处理延迟
        await asyncio.sleep(0.05)
        return httpx.Response(200, json={"ok": True})

    transport = httpx.MockTransport(_handler)
    async with httpx.AsyncClient(timeout=5, limits=limits, transport=transport) as c:
        yield c


# ------------ 异步请求测试函数 ------------
@pytest.mark.asyncio
async def test_concurrent_requests(max_concurrent, client):
    """测试指定并发数下的请求成功率"""
    semaphore = asyncio.Semaphore(max_concurrent)

    async def make_request():
        async with semaphore:
            try:
                # 使用 MockTransport，不真实发起网络请求
                response = await client.get("http://example.local/delay/0.05")
                return response.status_code == 200
            except Exception:
                return False

    # 降低单次请求数量以缩短测试时间
    tasks = [make_request() for _ in range(60)]
    start_time = time.time()
    results = await asyncio.gather(*tasks)
    end_time = time.time()

    success_rate = sum(results) / len(results)
    duration = end_time - start_time

    print(f"并发数: {max_concurrent:2d} | 成功率: {success_rate:.2%} | 耗时: {duration:.1f}s")
    return success_rate

# ------------ 并发搜索主函数 ------------
async def find_optimal_concurrency():
    """找到最优并发数"""
    print(f"系统平台: {platform.system()} {platform.version()}")
    tune_windows_tcp()

    # 全局复用一个 AsyncClient（带连接池）
    limits = httpx.Limits(
        max_connections=200,          # 最大连接数
        max_keepalive_connections=50  # 最大保持活跃连接（HTTP Keep-Alive）
    )
    async with httpx.AsyncClient(timeout=15, limits=limits) as client:
        print("正在测试不同并发数的性能...\n")
        for concurrent in range(20, 101, 10):
            success_rate = await test_concurrent_requests(concurrent, client)
            if success_rate < 0.95:
                print(f"\n建议的最大并发数: {concurrent - 5}")
                break
            await asyncio.sleep(0.5)

# ------------ 程序入口 ------------
if __name__ == "__main__":
    if sys.platform.startswith("win"):
        # Windows 下可切换事件循环策略，Selector 对 httpx 有较好兼容性
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    asyncio.run(find_optimal_concurrency())
