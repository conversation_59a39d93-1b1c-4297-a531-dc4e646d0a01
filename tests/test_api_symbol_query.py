from fastapi.testclient import TestClient

from symbol_processor.symbol_index import SymbolIndex
from utils.models import Parameter, Reference, ReferenceType, Symbol, SymbolType
from web_service.api import CodebaseRetrievalAPI


def make_symbol_index():
    index = SymbolIndex()
    s_func_login = Symbol(
        id="s1",
        name="login",
        symbol_type=SymbolType.FUNCTION,
        file_path="/repo/auth/user.py",
        start_line=10,
        end_line=30,
        start_column=0,
        end_column=0,
        module_path="auth/user.py",
        signature="def login(username, password) -> bool",
        parameters=[Parameter(name="username"), Parameter(name="password")],
        return_type="bool",
        language="python",
    )

    s_class_user = Symbol(
        id="s3",
        name="UserService",
        symbol_type=SymbolType.CLASS,
        file_path="/repo/auth/service.ts",
        start_line=1,
        end_line=200,
        start_column=0,
        end_column=0,
        module_path="auth/service.ts",
        signature=None,
        language="typescript",
    )

    s_method_auth = Symbol(
        id="s2",
        name="authenticate",
        symbol_type=SymbolType.METHOD,
        file_path="/repo/auth/service.ts",
        start_line=5,
        end_line=50,
        start_column=0,
        end_column=0,
        module_path="auth/service.ts",
        namespace="AuthService",
        parent_symbol="s3",
        signature="authenticate(user: string, pass: string): boolean",
        language="typescript",
        references=[
            Reference(
                file_path="/repo/auth/web.ts",
                line_number=12,
                column_number=3,
                context="authService.authenticate(user, pass)",
                reference_type=ReferenceType.CALL,
            )
        ],
    )

    for s in (s_func_login, s_class_user, s_method_auth):
        index.add_symbol(s)
    return index


def make_client_with_symbols():
    api = CodebaseRetrievalAPI(symbol_index=make_symbol_index())
    return TestClient(api.app)


def test_symbol_query_basic():
    client = make_client_with_symbols()
    resp = client.post("/symbol_query", json={"query": "login", "max_results": 10})
    assert resp.status_code == 200
    data = resp.json()
    names = [r["symbol"]["name"] for r in data["results"]]
    assert "login" in names


def test_symbol_query_filters():
    client = make_client_with_symbols()
    resp = client.post(
        "/symbol_query",
        json={
            "query": "auth",
            "symbol_types": ["method"],
            "languages": ["typescript"],
            "max_results": 10,
        },
    )
    assert resp.status_code == 200
    data = resp.json()
    assert all(r["symbol"]["type"] == "method" for r in data["results"])  # 仅方法
    assert all(r["symbol"]["language"] == "typescript" for r in data["results"])  # 仅TS


def test_symbol_query_context_path():
    client = make_client_with_symbols()
    resp = client.post(
        "/symbol_query",
        json={
            "query": "authenticate",
            "context_path": "auth/service.ts",
            "max_results": 10,
        },
    )
    assert resp.status_code == 200
    data = resp.json()
    assert any(r["symbol"]["name"] == "authenticate" for r in data["results"])


def test_symbol_query_languages_wildcard():
    client = make_client_with_symbols()
    # '*' 表示全局语言搜索，不过滤
    resp = client.post(
        "/symbol_query",
        json={"query": "login", "languages": "*", "max_results": 10},
    )
    assert resp.status_code == 200
    data = resp.json()
    names = [r["symbol"]["name"] for r in data["results"]]
    assert "login" in names


def test_symbol_query_types_wildcard():
    client = make_client_with_symbols()
    # '*' 表示全局类型搜索，不过滤
    resp = client.post(
        "/symbol_query",
        json={"query": "auth", "symbol_types": ["*"], "max_results": 10},
    )
    assert resp.status_code == 200
    data = resp.json()
    # 至少应包含 authenticate（method）或 UserService（class）中的一个
    type_set = {r["symbol"]["type"] for r in data["results"]}
    assert "method" in type_set or "class" in type_set
