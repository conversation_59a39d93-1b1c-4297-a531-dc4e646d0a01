"""
仓库索引管理器
管理代码分片的索引数据和持久化存储
"""

import gzip
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Optional

from config import INDEX_CONFIG
from symbol_processor.symbol_index import SymbolIndex
from utils.helpers import cosine_similarity, ensure_directory
from utils.interfaces import IRepoIndex
from utils.models import CodeChunk

logger = logging.getLogger(__name__)


class RepoIndex(IRepoIndex):
    """仓库索引管理器实现"""

    def __init__(self, cache_dir: str = None):
        self.chunks: dict[str, CodeChunk] = {}  # chunk_id -> CodeChunk
        self.file_chunks: dict[str, set[str]] = {}  # file_path -> set of chunk_ids
        self.language_chunks: dict[str, set[str]] = {}  # language -> set of chunk_ids
        self.type_chunks: dict[str, set[str]] = {}  # chunk_type -> set of chunk_ids
        self.keyword_chunks: dict[str, set[str]] = {}  # keyword -> set of chunk_ids

        self.repo_path: Optional[str] = None
        self.repo_insight: Optional[str] = None
        self.created_at: Optional[datetime] = None
        self.updated_at: Optional[datetime] = None
        self.file_checksums: dict[str, str] = {}  # file_path -> crc32
        self.file_insights: dict[str, str] = {}  # file_path -> file level insight

        # 初始化符号索引
        self.symbol_index = SymbolIndex()
        self.cache_dir = cache_dir

        # 配置
        self.enable_persistence = INDEX_CONFIG["enable_persistence"]
        self.compression_enabled = INDEX_CONFIG["compression_enabled"]

    def add_chunk(self, chunk: CodeChunk):
        """添加代码分片到索引"""
        # 添加到主索引
        self.chunks[chunk.id] = chunk

        # 更新文件索引
        if chunk.relative_path not in self.file_chunks:
            self.file_chunks[chunk.relative_path] = set()
        self.file_chunks[chunk.relative_path].add(chunk.id)

        # 更新语言索引
        if chunk.language not in self.language_chunks:
            self.language_chunks[chunk.language] = set()
        self.language_chunks[chunk.language].add(chunk.id)

        # 更新类型索引
        if chunk.chunk_type not in self.type_chunks:
            self.type_chunks[chunk.chunk_type] = set()
        self.type_chunks[chunk.chunk_type].add(chunk.id)

        # 更新关键词索引
        if chunk.keywords:
            for keyword in chunk.keywords:
                if keyword not in self.keyword_chunks:
                    self.keyword_chunks[keyword] = set()
                self.keyword_chunks[keyword].add(chunk.id)

        # 更新时间戳
        self.updated_at = datetime.now()

    def remove_chunks_by_file(self, file_path: str):
        """删除指定文件的所有分片"""
        if file_path not in self.file_chunks:
            logger.warning(f"警告: 文件 {file_path} 不在索引中，跳过删除")
            return

        chunk_ids_to_remove = self.file_chunks[file_path].copy()

        for chunk_id in chunk_ids_to_remove:
            self.remove_chunk(chunk_id)

        # 删除文件索引（安全删除）
        if file_path in self.file_chunks:
            del self.file_chunks[file_path]

        # 删除文件校验和
        if file_path in self.file_checksums:
            del self.file_checksums[file_path]

        # 删除文件级洞察
        if file_path in self.file_insights:
            del self.file_insights[file_path]

        # 删除符号索引中的相关符号
        if self.symbol_index:
            self.symbol_index.remove_symbols_by_file(file_path)

    def remove_chunk(self, chunk_id: str):
        """删除指定的代码分片"""
        if chunk_id not in self.chunks:
            return

        chunk = self.chunks[chunk_id]

        # 从主索引删除
        del self.chunks[chunk_id]

        # 从文件索引删除
        if chunk.relative_path in self.file_chunks:
            self.file_chunks[chunk.relative_path].discard(chunk_id)
            if not self.file_chunks[chunk.relative_path]:
                del self.file_chunks[chunk.relative_path]

        # 从语言索引删除
        if chunk.language in self.language_chunks:
            self.language_chunks[chunk.language].discard(chunk_id)
            if not self.language_chunks[chunk.language]:
                del self.language_chunks[chunk.language]

        # 从类型索引删除
        if chunk.chunk_type in self.type_chunks:
            self.type_chunks[chunk.chunk_type].discard(chunk_id)
            if not self.type_chunks[chunk.chunk_type]:
                del self.type_chunks[chunk.chunk_type]

        # 从关键词索引删除
        if chunk.keywords:
            for keyword in chunk.keywords:
                if keyword in self.keyword_chunks:
                    self.keyword_chunks[keyword].discard(chunk_id)
                    if not self.keyword_chunks[keyword]:
                        del self.keyword_chunks[keyword]

    def get_chunk(self, chunk_id: str) -> Optional[CodeChunk]:
        """获取指定的代码分片"""
        return self.chunks.get(chunk_id)

    def get_chunks_by_file(self, file_path: str) -> list[CodeChunk]:
        """获取指定文件的所有分片"""
        if file_path not in self.file_chunks:
            return []

        return [
            self.chunks[chunk_id]
            for chunk_id in self.file_chunks[file_path]
            if chunk_id in self.chunks
        ]

    def get_chunks_by_language(self, language: str) -> list[CodeChunk]:
        """获取指定语言的所有分片"""
        if language not in self.language_chunks:
            return []

        return [
            self.chunks[chunk_id]
            for chunk_id in self.language_chunks[language]
            if chunk_id in self.chunks
        ]

    def get_chunks_by_type(self, chunk_type: str) -> list[CodeChunk]:
        """获取指定类型的所有分片"""
        if chunk_type not in self.type_chunks:
            return []

        return [
            self.chunks[chunk_id]
            for chunk_id in self.type_chunks[chunk_type]
            if chunk_id in self.chunks
        ]

    def search_by_keywords(self, keywords: list[str]) -> list[CodeChunk]:
        """根据关键词搜索分片"""
        matching_chunk_ids = set()

        for keyword in keywords:
            if keyword in self.keyword_chunks:
                if not matching_chunk_ids:
                    matching_chunk_ids = self.keyword_chunks[keyword].copy()
                else:
                    matching_chunk_ids &= self.keyword_chunks[keyword]

        return [
            self.chunks[chunk_id]
            for chunk_id in matching_chunk_ids
            if chunk_id in self.chunks
        ]

    def find_similar_chunks(
        self, query_embedding: list[float], top_k: int = 10
    ) -> list[tuple]:
        """基于嵌入向量找到相似的分片"""
        similarities = []

        for chunk in self.chunks.values():
            if chunk.embedding:
                similarity = cosine_similarity(query_embedding, chunk.embedding)
                similarities.append((chunk, similarity))

        # 按相似度排序
        similarities.sort(key=lambda x: x[1], reverse=True)

        return similarities[:top_k]

    def get_all_chunks(self) -> list[CodeChunk]:
        """获取所有代码分片"""
        return list(self.chunks.values())

    def get_repo_insight(self) -> str:
        """获取仓库整体洞察"""
        if self.repo_insight:
            return self.repo_insight

        # 如果没有预生成的洞察，生成基础统计信息
        return self._generate_basic_insight()

    def set_repo_insight(self, insight: str):
        """设置仓库整体洞察"""
        self.repo_insight = insight
        self.updated_at = datetime.now()

    def set_file_insight(self, file_path: str, insight: str):
        """设置某个文件的文件级洞察"""
        self.file_insights[file_path] = insight or ""
        self.updated_at = datetime.now()

    def get_file_insight(self, file_path: str) -> str:
        """获取文件级洞察"""
        return self.file_insights.get(file_path, "")

    def _generate_basic_insight(self) -> str:
        """生成基础的仓库洞察"""
        total_chunks = len(self.chunks)
        total_files = len(self.file_chunks)
        languages = list(self.language_chunks.keys())

        insight_parts = [
            f"这个代码仓库包含{total_files}个文件，共{total_chunks}个代码片段。"
        ]

        if languages:
            insight_parts.append(
                f"支持的编程语言包括：{', '.join(sorted(languages))}。"
            )

        return " ".join(insight_parts)

    def update_file_checksum(self, file_path: str, checksum: str):
        """更新文件校验和"""
        self.file_checksums[file_path] = checksum

    def get_file_checksum(self, file_path: str) -> Optional[str]:
        """获取文件校验和"""
        return self.file_checksums.get(file_path)

    def get_stats(self) -> dict:
        """获取索引统计信息"""
        return {
            "total_chunks": len(self.chunks),
            "total_files": len(self.file_chunks),
            "languages": {
                lang: len(chunk_ids) for lang, chunk_ids in self.language_chunks.items()
            },
            "chunk_types": {
                ctype: len(chunk_ids) for ctype, chunk_ids in self.type_chunks.items()
            },
            "total_keywords": len(self.keyword_chunks),
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "repo_path": self.repo_path,
        }

    def save_metadata(self, index_dir: str):
        """保存索引元数据"""
        if not self.enable_persistence:
            return

        try:
            index_path = Path(index_dir)
            ensure_directory(index_path)

            metadata = {
                "repo_path": self.repo_path,
                "repo_insight": self.repo_insight,
                "created_at": self.created_at.isoformat() if self.created_at else None,
                "updated_at": self.updated_at.isoformat() if self.updated_at else None,
                "file_checksums": self.file_checksums,
                "file_insights": self.file_insights,
                "language_chunks": {
                    lang: list(chunk_ids)
                    for lang, chunk_ids in self.language_chunks.items()
                },
                "type_chunks": {
                    ctype: list(chunk_ids)
                    for ctype, chunk_ids in self.type_chunks.items()
                },
                "keyword_chunks": {
                    keyword: list(chunk_ids)
                    for keyword, chunk_ids in self.keyword_chunks.items()
                },
                "version": "2.1",  # 增加版本号以支持符号索引
            }

            metadata_file = index_path / "metadata.json"
            if self.compression_enabled:
                with gzip.open(str(metadata_file) + ".gz", "wt", encoding="utf-8") as f:
                    json.dump(metadata, f, ensure_ascii=False, indent=2)
            else:
                with open(metadata_file, "w", encoding="utf-8") as f:
                    json.dump(metadata, f, ensure_ascii=False, indent=2)

            # 保存符号索引
            self._save_symbol_index(index_path)

        except Exception as e:
            logger.error(f"保存元数据失败: {e}")
            raise

    def _save_symbol_index(self, index_path: Path):
        """保存符号索引"""
        try:
            symbol_index_file = index_path / "symbol_index.json"
            if self.compression_enabled:
                self.symbol_index.save_index(str(symbol_index_file) + ".gz")
            else:
                self.symbol_index.save_index(str(symbol_index_file))
            logger.debug("符号索引已保存")
        except Exception as e:
            logger.warning(f"保存符号索引失败: {e}")

    def save_file_index(self, index_dir: str, file_path: str):
        """保存单个文件的索引"""
        if not self.enable_persistence:
            return

        try:
            index_path = Path(index_dir)
            files_dir = index_path / "files"
            ensure_directory(files_dir)

            # 获取该文件的分片
            chunk_ids = self.file_chunks.get(file_path, set())
            
            # 创建文件索引数据（即使没有分片也要保存空索引）
            safe_filename = self._get_safe_filename(file_path)
            file_index_path = files_dir / f"{safe_filename}.json"

            file_chunks_data = {
                "file_path": file_path,
                "chunk_ids": list(chunk_ids),
                "chunks": {
                    chunk_id: self._serialize_chunk(self.chunks[chunk_id])
                    for chunk_id in chunk_ids
                    if chunk_id in self.chunks
                },
                # 添加文件处理状态标记，避免重复处理
                "processed": True,
                "checksum": self.file_checksums.get(file_path, ""),
            }

            # 保存文件索引
            if self.compression_enabled:
                with gzip.open(
                    str(file_index_path) + ".gz", "wt", encoding="utf-8"
                ) as f:
                    json.dump(file_chunks_data, f, ensure_ascii=False, indent=2)
                # 删除未压缩版本
                if file_index_path.exists():
                    file_index_path.unlink()
            else:
                with open(file_index_path, "w", encoding="utf-8") as f:
                    json.dump(file_chunks_data, f, ensure_ascii=False, indent=2)
                # 删除压缩版本
                compressed_path = Path(str(file_index_path) + ".gz")
                if compressed_path.exists():
                    compressed_path.unlink()

        except Exception as e:
            logger.error(f"保存文件索引失败 {file_path}: {e}")
            raise

    def remove_file_index(self, index_dir: str, file_path: str):
        """删除单个文件的索引"""
        try:
            index_path = Path(index_dir)
            files_dir = index_path / "files"
            self._remove_file_index_file(files_dir, file_path)
        except Exception as e:
            logger.error(f"删除文件索引失败 {file_path}: {e}")

    def _remove_file_index_file(self, files_dir: Path, file_path: str):
        """删除文件索引文件"""
        if not files_dir.exists():
            return

        safe_filename = self._get_safe_filename(file_path)
        file_index_path = files_dir / f"{safe_filename}.json"
        compressed_path = Path(str(file_index_path) + ".gz")

        if file_index_path.exists():
            file_index_path.unlink()
        if compressed_path.exists():
            compressed_path.unlink()

    def _get_safe_filename(self, file_path: str) -> str:
        """将文件路径转换为安全的文件名"""
        import hashlib

        return hashlib.md5(file_path.encode("utf-8")).hexdigest()

    def load_index(self, index_dir: str) -> bool:
        """从目录加载索引"""
        try:
            index_path = Path(index_dir)
            if not index_path.exists():
                return False

            # 加载元数据
            if not self._load_metadata(index_path):
                return False

            # 加载所有文件索引
            self._load_all_file_indices(index_path)

            # 加载符号索引
            self._load_symbol_index(index_path)

            logger.info(f"索引已从 {index_dir} 加载，包含 {len(self.chunks)} 个分片")
            return True

        except Exception as e:
            logger.error(f"加载索引失败: {e}")
            return False

    def _load_symbol_index(self, index_path: Path):
        """加载符号索引"""
        try:
            symbol_index_file = index_path / "symbol_index.json"
            compressed_file = Path(str(symbol_index_file) + ".gz")

            if compressed_file.exists():
                success = self.symbol_index.load_index(str(compressed_file))
            elif symbol_index_file.exists():
                success = self.symbol_index.load_index(str(symbol_index_file))
            else:
                logger.debug("符号索引文件不存在，将创建新的符号索引")
                return

            if success:
                logger.debug("符号索引已加载")
            else:
                logger.warning("符号索引加载失败")
        except Exception as e:
            logger.warning(f"加载符号索引失败: {e}")

    def _load_metadata(self, index_path: Path) -> bool:
        """加载索引元数据"""
        metadata_file = index_path / "metadata.json"
        compressed_metadata = Path(str(metadata_file) + ".gz")

        try:
            if compressed_metadata.exists():
                with gzip.open(compressed_metadata, "rt", encoding="utf-8") as f:
                    metadata = json.load(f)
            elif metadata_file.exists():
                with open(metadata_file, encoding="utf-8") as f:
                    metadata = json.load(f)
            else:
                return False

            # 恢复元数据
            self.repo_path = metadata.get("repo_path")
            self.repo_insight = metadata.get("repo_insight")
            self.file_checksums = metadata.get("file_checksums", {})
            self.file_insights = metadata.get("file_insights", {})

            self.language_chunks = {
                lang: set(chunk_ids)
                for lang, chunk_ids in metadata.get("language_chunks", {}).items()
            }

            self.type_chunks = {
                ctype: set(chunk_ids)
                for ctype, chunk_ids in metadata.get("type_chunks", {}).items()
            }

            self.keyword_chunks = {
                keyword: set(chunk_ids)
                for keyword, chunk_ids in metadata.get("keyword_chunks", {}).items()
            }

            # 恢复时间戳
            if metadata.get("created_at"):
                self.created_at = datetime.fromisoformat(metadata["created_at"])
            if metadata.get("updated_at"):
                self.updated_at = datetime.fromisoformat(metadata["updated_at"])

            return True

        except Exception as e:
            logger.error(f"加载元数据失败: {e}")
            return False

    def _load_all_file_indices(self, index_path: Path):
        """加载所有文件索引"""
        files_dir = index_path / "files"
        if not files_dir.exists():
            return

        # 清空现有数据
        self.chunks.clear()
        self.file_chunks.clear()

        # 遍历所有文件索引
        for file_index_path in files_dir.glob("*.json*"):
            try:
                if file_index_path.suffix == ".gz":
                    with gzip.open(file_index_path, "rt", encoding="utf-8") as f:
                        file_data = json.load(f)
                else:
                    with open(file_index_path, encoding="utf-8") as f:
                        file_data = json.load(f)

                file_path = file_data["file_path"]
                chunk_ids = set(file_data["chunk_ids"])
                chunks_data = file_data["chunks"]

                # 恢复分片数据
                for chunk_id, chunk_data in chunks_data.items():
                    self.chunks[chunk_id] = self._deserialize_chunk(chunk_data)

                # 恢复文件索引
                self.file_chunks[file_path] = chunk_ids

            except Exception as e:
                logger.error(f"加载文件索引失败 {file_index_path}: {e}")
                continue

    def _serialize_chunk(self, chunk: CodeChunk) -> dict:
        """序列化代码分片"""
        return {
            "id": chunk.id,
            "file_path": chunk.file_path,
            "relative_path": chunk.relative_path,
            "start_line": chunk.start_line,
            "end_line": chunk.end_line,
            "content": chunk.content,
            "chunk_type": chunk.chunk_type,
            "symbol_name": chunk.symbol_name,
            "language": chunk.language,
            "insight": chunk.insight,
            "keywords": chunk.keywords,
            "embedding": chunk.embedding,
        }

    def _deserialize_chunk(self, chunk_data: dict) -> CodeChunk:
        """反序列化代码分片"""
        return CodeChunk(
            id=chunk_data["id"],
            file_path=chunk_data["file_path"],
            relative_path=chunk_data["relative_path"],
            start_line=chunk_data["start_line"],
            end_line=chunk_data["end_line"],
            content=chunk_data["content"],
            chunk_type=chunk_data["chunk_type"],
            symbol_name=chunk_data["symbol_name"],
            language=chunk_data["language"],
            insight=chunk_data.get("insight"),
            keywords=chunk_data.get("keywords"),
            embedding=chunk_data.get("embedding"),
        )
