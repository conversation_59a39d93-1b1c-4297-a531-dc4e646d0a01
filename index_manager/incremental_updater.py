"""
增量更新机制
检测文件变更并增量更新索引
"""

from __future__ import annotations

import asyncio
import fnmatch
import logging
import os
from pathlib import Path

from codebase_processor.chunker import CodeChunker
from codebase_processor.loader import CodebaseLoader
from codebase_processor.parser import TreeSitterParser
from config import INDEX_CONFIG, ignore_config
from embedding_processor.calculator import EmbeddingCalculator
from index_manager.repo_index import RepoIndex
from insight_generator.code_insight import CodeInsightGenerator
from insight_generator.keyword_extractor import KeywordExtractor
from symbol_processor import SymbolExtractor
from symbol_processor.symbol_index import SymbolIndex
from utils.file_oper import read_file
from utils.helpers import calculate_file_crc32, get_file_language, get_relative_path
from utils.models import CodeChunk, FileInfo

logger = logging.getLogger(__name__)


class IncrementalUpdater:
    """增量更新器"""

    def __init__(self, repo_index: RepoIndex, symbol_index: SymbolIndex | None = None):
        self.repo_index = repo_index
        self.symbol_index = symbol_index
        self.loader = CodebaseLoader()
        self.parser = TreeSitterParser()
        self.chunker = CodeChunker()
        self.insight_generator = CodeInsightGenerator()
        self.keyword_extractor = KeywordExtractor()
        self.embedding_calculator = EmbeddingCalculator()
        self.index_dir: str | None = None
        self.symbol_extractor = SymbolExtractor()

    def set_index_dir(self, index_dir: str):
        """设置索引目录，用于增量保存"""
        self.index_dir = index_dir

    def _should_ignore_path(self, path: str, repo_path: str) -> bool:
        """检查路径是否应该被忽略"""
        # 获取相对路径
        try:
            relative_path = get_relative_path(path, repo_path)
        except Exception:
            relative_path = os.path.relpath(path, repo_path)

        # 检查每个忽略模式
        for pattern in ignore_config.all_ignore_patterns:
            # 使用fnmatch进行模式匹配
            if fnmatch.fnmatch(relative_path, pattern) or fnmatch.fnmatch(
                os.path.basename(path), pattern
            ):
                return True

            # 检查路径的任何部分是否匹配模式
            path_parts = relative_path.split(os.sep)
            for part in path_parts:
                if fnmatch.fnmatch(part, pattern.replace("**/", "").replace("/**", "")):
                    return True

        return False

    def detect_file_changes(
        self, repo_path: str
    ) -> tuple[list[str], list[str], list[str]]:
        """检测文件变更

        Returns:
            Tuple[added_files, modified_files, deleted_files]
        """
        # 获取当前文件列表（已经通过loader过滤了忽略的文件）
        current_files = self.loader.load_files(repo_path)
        current_file_map = {info.relative_path: info for info in current_files}

        # 获取索引中的文件校验和
        indexed_checksums = self.repo_index.file_checksums

        added_files = []
        modified_files = []
        deleted_files = []

        # 检查新增和修改的文件
        for relative_path, file_info in current_file_map.items():
            if relative_path not in indexed_checksums:
                # 新文件
                added_files.append(relative_path)
            elif indexed_checksums[relative_path] != file_info.crc32:
                # 修改的文件
                modified_files.append(relative_path)

        # 检查删除的文件
        for relative_path in indexed_checksums:
            if relative_path not in current_file_map:
                deleted_files.append(relative_path)

        return added_files, modified_files, deleted_files

    async def update_index(self, repo_path: str) -> dict[str, int]:
        """增量更新索引

        Returns:
            更新统计信息
        """
        logger.debug("开始检测文件变更...")
        added_files, modified_files, deleted_files = self.detect_file_changes(repo_path)

        stats = {
            "added_files": len(added_files),
            "modified_files": len(modified_files),
            "deleted_files": len(deleted_files),
            "added_chunks": 0,
            "modified_chunks": 0,
            "deleted_chunks": 0,
        }

        logger.debug(
            f"检测到变更: 新增{len(added_files)}个文件, 修改{len(modified_files)}个文件, 删除{len(deleted_files)}个文件"
        )

        # 处理删除的文件
        if deleted_files:
            stats["deleted_chunks"] = await self._handle_deleted_files(deleted_files)

        # 并行处理修改和新增的文件（如果有）
        tasks = []
        task_keys = []  # 与 tasks 对应的统计键名

        if modified_files:
            tasks.append(
                asyncio.create_task(
                    self._handle_modified_files(repo_path, modified_files)
                )
            )
            task_keys.append("modified_chunks")

        if added_files:
            tasks.append(
                asyncio.create_task(
                    self._handle_added_files(repo_path, added_files)
                )
            )
            task_keys.append("added_chunks")

        if tasks:
            results = await asyncio.gather(*tasks, return_exceptions=True)

            for key, result in zip(task_keys, results):
                if isinstance(result, Exception):
                    logger.warning(f"处理{key}任务失败: {result}")
                    continue
                stats[key] = result

        # 如果有任何变更，重新生成仓库洞察并保存元数据
        if any(
            stats[key] > 0
            for key in ["added_chunks", "modified_chunks", "deleted_chunks"]
        ):
            await self._update_repo_insight()
            # 保存更新后的元数据
            if self.index_dir:
                self.repo_index.save_metadata(self.index_dir)

        logger.debug(
            f"增量更新完成: 新增{stats['added_chunks']}个分片, 修改{stats['modified_chunks']}个分片, 删除{stats['deleted_chunks']}个分片"
        )

        return stats

    async def _handle_deleted_files(self, deleted_files: list[str]) -> int:
        """处理删除的文件"""
        deleted_chunks_count = 0

        for file_path in deleted_files:
            # 获取该文件的所有分片
            chunks = self.repo_index.get_chunks_by_file(file_path)
            deleted_chunks_count += len(chunks)

            # 从索引中删除该文件的所有分片
            self.repo_index.remove_chunks_by_file(file_path)

            # 同步删除符号索引
            if self.symbol_index:
                self.symbol_index.remove_symbols_by_file(file_path)

            # 删除该文件的索引文件
            if self.index_dir:
                self.repo_index.remove_file_index(self.index_dir, file_path)

            logger.info(f"删除文件 {file_path} 的 {len(chunks)} 个分片")

        return deleted_chunks_count

    async def _handle_modified_files(
        self, repo_path: str, modified_files: list[str]
    ) -> int:
        """并行处理修改的文件"""
        if not modified_files:
            return 0

        logger.info(f"并行处理 {len(modified_files)} 个修改文件...")

        # 先删除所有旧分片
        old_chunks_count = 0
        for relative_path in modified_files:
            logger.info(f"处理修改文件: {relative_path}")
            old_chunks = self.repo_index.get_chunks_by_file(relative_path)
            old_chunks_count += len(old_chunks)
            self.repo_index.remove_chunks_by_file(relative_path)
            # 删除旧的符号
            if self.symbol_index:
                self.symbol_index.remove_symbols_by_file(relative_path)

        # 并行处理文件
        max_concurrent = INDEX_CONFIG.get("max_concurrent_files", 5)
        semaphore = asyncio.Semaphore(max_concurrent)

        async def process_modified_file(relative_path):
            async with semaphore:
                file_path = str(Path(repo_path) / relative_path)
                try:
                    # 为单个文件处理添加超时保护
                    new_chunks = await asyncio.wait_for(
                        self._process_single_file(file_path, repo_path),
                        timeout=300  # 5分钟超时
                    )

                    # 立即保存该文件的索引
                    if self.index_dir:
                        self.repo_index.save_file_index(self.index_dir, relative_path)

                    return relative_path, len(new_chunks), None
                except asyncio.TimeoutError:
                    logger.warning(f"处理修改文件超时: {relative_path}")
                    return relative_path, 0, Exception(f"处理超时: {relative_path}")
                except Exception as e:
                    return relative_path, 0, e

        # 创建并执行任务
        tasks = [
            process_modified_file(relative_path) for relative_path in modified_files
        ]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理结果
        modified_chunks_count = 0
        for i, result in enumerate(results):
            relative_path = modified_files[i]
            
            # 处理可能的异常结果
            if isinstance(result, Exception):
                logger.warning(f"处理修改文件失败 {relative_path}: {result}")
                continue
                
            # 正常结果
            file_path, chunk_count, error = result
            if error:
                logger.warning(f"处理修改文件失败 {file_path}: {error}")
            else:
                modified_chunks_count += chunk_count
                logger.info(f"更新文件 {file_path}: 新增 {chunk_count} 个分片")

        logger.info(
            f"修改文件处理完成: 删除 {old_chunks_count} 个分片, 新增 {modified_chunks_count} 个分片"
        )
        return modified_chunks_count

    async def _handle_added_files(self, repo_path: str, added_files: list[str]) -> int:
        """并行处理新增的文件"""
        if not added_files:
            return 0

        logger.info(f"并行处理 {len(added_files)} 个新增文件...")

        # 并行处理文件
        max_concurrent = INDEX_CONFIG.get("max_concurrent_files", 5)
        semaphore = asyncio.Semaphore(max_concurrent)

        async def process_added_file(relative_path):
            async with semaphore:
                file_path = str(Path(repo_path) / relative_path)
                try:
                    # 为单个文件处理添加超时保护
                    new_chunks = await asyncio.wait_for(
                        self._process_single_file(file_path, repo_path),
                        timeout=300  # 5分钟超时
                    )

                    # 立即保存该文件的索引
                    if self.index_dir:
                        self.repo_index.save_file_index(self.index_dir, relative_path)

                    return relative_path, len(new_chunks), None
                except asyncio.TimeoutError:
                    logger.warning(f"处理新增文件超时: {relative_path}")
                    return relative_path, 0, Exception(f"处理超时: {relative_path}")
                except Exception as e:
                    return relative_path, 0, e

        # 创建并执行任务
        tasks = [process_added_file(relative_path) for relative_path in added_files]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理结果
        added_chunks_count = 0
        for i, result in enumerate(results):
            relative_path = added_files[i]
            
            # 处理可能的异常结果
            if isinstance(result, Exception):
                logger.warning(f"处理新增文件失败 {relative_path}: {result}")
                continue
                
            # 正常结果
            file_path, chunk_count, error = result
            if error:
                logger.warning(f"处理新增文件失败 {file_path}: {error}")
            else:
                added_chunks_count += chunk_count
                logger.info(f"新增文件 {file_path}: 创建 {chunk_count} 个分片")

        logger.info(f"新增文件处理完成: 创建 {added_chunks_count} 个分片")
        return added_chunks_count

    async def _process_single_file(
        self, file_path: str, repo_path: str
    ) -> list[CodeChunk]:
        """完整处理单个文件，返回生成的分片"""
        # 加载文件信息
        file_info = self._create_file_info(file_path, repo_path)
        if not file_info:
            return []

        try:
            # 1. 解析文件
            parse_tree = self.parser.parse_file(file_info)
            if not parse_tree:
                return []

            # 提取符号并更新符号索引
            if self.symbol_index and file_info.language != "markdown":
                try:
                    symbols = self.symbol_extractor.extract_symbols(
                        parse_tree, file_info
                    )
                    for sym in symbols:
                        self.symbol_index.add_symbol(sym)
                except Exception as e:
                    print(f"符号提取失败 {file_info.relative_path}: {e}")

            # 2. 分片
            if file_info.language == "markdown":
                chunks = self.chunker.chunk_markdown(file_info)
            else:
                chunks = self.chunker.chunk_by_symbols(parse_tree, file_info)

            if not chunks:
                # 即使没有分片，也要更新文件校验和并保存空索引，避免重复处理
                logger.debug(f"文件 {file_info.relative_path} 无法分片或为空文件，保存空索引")
                
                # 更新文件校验和
                self.repo_index.update_file_checksum(
                    file_info.relative_path, file_info.crc32
                )
                
                # 保存空索引（重要：避免定时刷新时重复处理）
                if self.index_dir:
                    self.repo_index.save_file_index(self.index_dir, file_info.relative_path)
                
                return []

            # 3. 生成洞察
            # chunks = await self.insight_generator.update_chunk_insights(chunks) # For cmp test

            # 4. 提取关键词
            chunks = self.keyword_extractor.update_chunk_keywords(chunks)

            # 5/6. 并行执行：生成文件级洞察（LLM）与计算嵌入向量
            insight_task = asyncio.create_task(
                self.insight_generator.generate_file_insight(
                    file_info.relative_path, chunks
                )
            )
            embedding_task = asyncio.create_task(
                self.embedding_calculator.update_chunk_embeddings(chunks)
            )

            file_insight, emb_result = await asyncio.gather(
                insight_task, embedding_task, return_exceptions=True
            )

            # 处理洞察结果
            if isinstance(file_insight, Exception):
                # 保守处理，洞察失败不影响索引构建
                logger.debug(
                    f"文件级洞察失败 {file_info.relative_path}: {file_insight}"
                )
            elif file_insight:
                self.repo_index.set_file_insight(file_info.relative_path, file_insight)

            # 处理嵌入结果（必须回填到 chunks）
            if isinstance(emb_result, Exception):
                logger.warning(
                    f"计算嵌入失败 {file_info.relative_path}: {emb_result}，将使用零向量降级"
                )
                # 保留原 chunks（其中 calculate_embedding 已在内部做零向量降级）
            else:
                chunks = emb_result

            # 7. 添加到索引
            for chunk in chunks:
                self.repo_index.add_chunk(chunk)

            # 8. 更新文件校验和
            self.repo_index.update_file_checksum(
                file_info.relative_path, file_info.crc32
            )

            return chunks

        except Exception as e:
            logger.error(f"完整处理文件失败 {file_info.relative_path}: {e}")
            return []

    def _create_file_info(self, file_path: str, repo_path: str) -> FileInfo:
        """创建文件信息对象"""
        content = read_file(file_path)
        language = get_file_language(file_path)
        if not language:
            return None

        relative_path = get_relative_path(file_path, repo_path)
        file_size = len(content.encode("utf-8"))
        crc32 = calculate_file_crc32(file_path)

        return FileInfo(
            file_path=file_path,
            relative_path=relative_path,
            language=language,
            content=content,
            size=file_size,
            crc32=crc32,
        )

    async def _update_repo_insight(self):
        """更新仓库整体洞察"""
        try:
            all_chunks = self.repo_index.get_all_chunks()
            repo_insight = self.insight_generator.generate_repo_insight(all_chunks)
            self.repo_index.set_repo_insight(repo_insight)
            logger.info("仓库洞察已更新")
        except Exception as e:
            logger.error(f"更新仓库洞察失败: {e}")

    def is_incremental_update_needed(self, repo_path: str) -> bool:
        """检查是否需要增量更新"""
        added_files, modified_files, deleted_files = self.detect_file_changes(repo_path)
        return len(added_files) > 0 or len(modified_files) > 0 or len(deleted_files) > 0

    def get_change_summary(self, repo_path: str) -> dict:
        """获取变更摘要"""
        added_files, modified_files, deleted_files = self.detect_file_changes(repo_path)

        return {
            "has_changes": len(added_files) > 0
            or len(modified_files) > 0
            or len(deleted_files) > 0,
            "added_files": added_files,
            "modified_files": modified_files,
            "deleted_files": deleted_files,
            "total_changes": len(added_files)
            + len(modified_files)
            + len(deleted_files),
        }
