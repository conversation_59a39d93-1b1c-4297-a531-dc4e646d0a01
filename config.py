import asyncio
from enum import Enum
from typing import Any

import openai
import httpx
import sys
import time
from pydantic import BaseModel, Field, ConfigDict


class HTTPClientPool:
    """httpx.AsyncClient 连接池（按事件循环隔离的全局复用）
    - 启用 HTTP/2
    - 连接池上限与 keep-alive 复用
    - 按事件循环隔离，避免跨事件循环复用导致的不稳定
    """

    _clients: dict[tuple[str, int, int], httpx.AsyncClient] = {}

    @classmethod
    def _current_loop_id(cls) -> int:
        try:
            loop = asyncio.get_running_loop()
            return id(loop)
        except RuntimeError:
            # 无运行中的事件循环（如在纯同步上下文中）
            return 0

    @classmethod
    def get_client(cls, base_url: str, timeout_seconds: int) -> httpx.AsyncClient:
        # 以 (基础URL, 超时, 事件循环ID) 作为键，避免跨事件循环复用
        loop_id = cls._current_loop_id()
        key = (base_url, int(timeout_seconds), loop_id)
        if key in cls._clients:
            return cls._clients[key]

        limits = httpx.Limits(
            max_connections=200,
            max_keepalive_connections=50,
            keepalive_expiry=30.0,
        )

        client = httpx.AsyncClient(
            timeout=timeout_seconds,
            http2=True,
            limits=limits,
            follow_redirects=True,
            base_url=base_url,
        )
        cls._clients[key] = client
        return client

    @classmethod
    async def aclose_all(cls) -> None:
        """关闭所有已创建的 AsyncClient 实例，释放连接池资源。"""
        clients = list(cls._clients.values())
        cls._clients.clear()
        for c in clients:
            try:
                await c.aclose()
            except Exception:
                pass


class IgnoreConfig(BaseModel):
    """文件和目录忽略配置"""

    # 隐藏目录
    hidden_directories: list[str] = [
        ".*",
        ".*/**",
        "**/.*",
    ]

    # 开发和构建相关目录
    ignore_directories: list[str] = [
        "**/__pycache__/**",
        "**/.pytest_cache/**",
        "**/venv/**",
        "**/node_modules/**",
        "**/dist/**",
        "**/build/**",
        "**/cache/**",
        "**/logs/**",
        "**/temp/**",
        "**/codebase_cache/**",
        "**/oh_modules/**",
    ]

    # 测试相关
    ignore_test_patterns: list[str] = [
        # "**/test/**",
        # "**/tests/**",
        # "**/test_*.*",
        # "**/*_test.*",
    ]

    # 版本控制和IDE
    ignore_vcs_ide: list[str] = [
        "*.git*",
        "*.vscode*",
        "*.venv*",
        "*.egg-info*",
    ]

    # 日志和覆盖率
    ignore_logs_coverage: list[str] = [
        "**/.coverage/**",
        "**/.coverage.*",
        "*.log",
        "*.log.*",
        "*logs",
    ]

    # 缓存和临时文件
    ignore_cache_temp: list[str] = [
        "*__pycache__*",
        "*.pytest_cache*",
        "*.cache",
        "*.pyc",
    ]

    # 媒体和数据文件
    ignore_media_data: list[str] = [
        "*.png",
        "*.jpeg",
        "*.jpg",
        "*.svg",
        "*.json",
        "*.json5",
        "*.sample",
    ]

    @property
    def all_ignore_patterns(self) -> list[str]:
        """获取所有忽略模式的合并列表"""
        return (
            self.hidden_directories
            + self.ignore_directories
            + self.ignore_test_patterns
            + self.ignore_vcs_ide
            + self.ignore_logs_coverage
            + self.ignore_cache_temp
            + self.ignore_media_data
        )


class ModelType(str, Enum):
    """模型类型枚举"""
    BACKUP_SUFFIX = "_backup"
    NORMAL = "normal"
    NORMAL_BACKUP = "normal_backup"
    FLASH = "flash"
    FLASH_BACKUP = "flash_backup"
    EMBEDDING = "embedding"
    EMBEDDING_BACKUP = "embedding_backup"


class ModelConfig(BaseModel):
    """模型配置类"""

    model_name: str = Field(..., description="模型名称")
    base_url: str = Field(..., description="API基础URL")
    api_key: str = Field(..., description="API密钥")
    max_context_token: int = Field(default=256_000, description="最大输入token数")
    max_tokens: int = Field(default=1024, description="最大输出token数")
    timeout: int = Field(default=30, description="超时时间(秒)")
    extra_body: dict[str, Any] = Field(default_factory=dict, description="额外请求参数")

    # Pydantic v2 配置
    model_config = ConfigDict(frozen=True)


class LLMClient:
    """LLM客户端封装类"""

    def __init__(self, config: ModelConfig):
        self.config = config
        # 复用全局HTTP连接池，启用HTTP/2与连接复用，降低TLS握手与建连开销
        http_client = HTTPClientPool.get_client(self.config.base_url, self.config.timeout)
        self._client = openai.AsyncOpenAI(
            api_key=config.api_key,
            base_url=config.base_url,
            timeout=config.timeout,
            # 关闭 SDK 内置重试，交由上层快速重试与备援接管，降低尾延迟
            max_retries=0,
            http_client=http_client,
        )

    async def chat_completion(self, prompt: str, **kwargs) -> str:
        """聊天补全"""
        for i in range(2):
            try:
                response = await self._client.chat.completions.create(
                    model=self.config.model_name,
                    messages=[{"role": "user", "content": prompt}],
                    max_tokens=min(
                        self.config.max_tokens,
                        kwargs.get("max_tokens", self.config.max_tokens),
                    ),
                    extra_body=self.config.extra_body,
                    # **kwargs
                )
                if response.choices[0].message.content is None:
                    raise Exception(f"LLM返回空响应: {str(response)}")
                return response.choices[0].message.content
            except httpx.HTTPStatusError as e:
                # 对于明确的 HTTP 状态错误（如 404/403），立即上抛触发上层备用模型，不做本地重试
                status_code = getattr(e.response, "status_code", None)
                if status_code in (403, 404):
                    raise
                # 其他状态码按原策略进行快速重试
                if i == 2:
                    raise Exception(f"LLM调用失败: {str(e)}") from e
                else:
                    continue
            except Exception as e:
                if i == 2:
                    raise Exception(f"LLM调用失败: {str(e)}") from e
                else:
                    continue

    async def embedding(self, text: str) -> list:
        """文本嵌入"""
        for i in range(2):
            try:
                response = await self._client.embeddings.create(
                    model=self.config.model_name,
                    input=text,
                    timeout=getattr(self.config, 'timeout', 10.0),  # 使用配置的超时时间，默认5秒
                )
                if response.data is None or len(response.data) == 0:
                    raise Exception(f"嵌入调用失败: {str(response)}")
                return response.data[0].embedding
            except httpx.HTTPStatusError as e:
                status_code = getattr(e.response, "status_code", None)
                if status_code in (403, 404):
                    raise
                if i == 2:
                    raise Exception(f"嵌入调用失败: {str(e)}") from e
                else:
                    continue
            except Exception as e:
                if i == 2:
                    raise Exception(f"嵌入调用失败: {str(e)}") from e
                else:
                    continue

    async def batch_embedding(self, texts: list[str]) -> list[list[float]]:
        """批量文本嵌入（单次POST，服务端返回与输入顺序一致）"""
        if not texts:
            return []
        for i in range(2):
            try:
                response = await self._client.embeddings.create(
                    model=self.config.model_name,
                    input=texts,
                    timeout=getattr(self.config, 'timeout', 10.0),  # 添加超时设置
                )
                if response.data is None or len(response.data) == 0:
                    raise Exception(f"批量嵌入调用失败: {str(response)}")
                return [item.embedding for item in response.data]
            except httpx.HTTPStatusError as e:
                status_code = getattr(e.response, "status_code", None)
                if status_code in (403, 404):
                    raise
                if i == 2:
                    raise Exception(f"批量嵌入调用失败: {str(e)}") from e
                else:
                    continue
            except Exception as e:
                if i == 2:
                    raise Exception(f"批量嵌入调用失败: {str(e)}") from e
                else:
                    continue


class ModelManager:
    """模型管理器"""

    def __init__(self):
        self._configs: dict[ModelType, ModelConfig] = {
            ModelType.NORMAL: ModelConfig(
                model_name="doubao-seed-1-6-250615",
                base_url="https://ark.cn-beijing.volces.com/api/v3",
                api_key="ba1a55eb-f6ad-4bd0-88fb-4e3ab5f69722",
                max_tokens=32_000,  # 输出token上限
                extra_body={"thinking": {"type": "disabled"}},
            ),
            # ModelType.NORMAL: ModelConfig(
            #     model_name="moonshotai/kimi-k2-instruct-0905",
            #     base_url="https://api-key.info/v1",
            #     api_key="sk-tD88pugtVmUpZSxLotX0L2CDMQIngvVuvbj9c3PsxzpF0jfX",
            #     max_tokens=32_000,  # 输出token上限
            # ),
            ModelType.NORMAL_BACKUP: ModelConfig(
                model_name="doubao-seed-1-6-250615",
                base_url="https://ark.cn-beijing.volces.com/api/v3",
                api_key="ba1a55eb-f6ad-4bd0-88fb-4e3ab5f69722",
                max_tokens=32_000,  # 输出token上限
                extra_body={"thinking": {"type": "disabled"}},
            ),

            ModelType.FLASH: ModelConfig(
                model_name="doubao-seed-1-6-250615",
                base_url="https://ark.cn-beijing.volces.com/api/v3",
                api_key="ba1a55eb-f6ad-4bd0-88fb-4e3ab5f69722",
                max_tokens=32_000,  # 输出token上限
                extra_body={"thinking": {"type": "disabled"}},
            ),
            ModelType.FLASH_BACKUP: ModelConfig(
                model_name="doubao-seed-1-6-250615",
                base_url="https://ark.cn-beijing.volces.com/api/v3",
                api_key="db011622-3694-490d-81a7-65e4ac64373f", # for demo
                max_tokens=32_000,  # 输出token上限
                extra_body={"thinking": {"type": "disabled"}},
            ),
            # ModelType.FLASH: ModelConfig(
            #     model_name="qwen-3-coder-480b",
            #     base_url="https://api-key.info/v1",
            #     api_key="sk-tD88pugtVmUpZSxLotX0L2CDMQIngvVuvbj9c3PsxzpF0jfX",
            #     max_tokens=32_000,  # 输出token上限
            # ),
            # ModelType.FLASH_BACKUP: ModelConfig(
            #     model_name="moonshotai/kimi-k2-instruct-0905",
            #     base_url="https://api-key.info/v1",
            #     api_key="sk-tD88pugtVmUpZSxLotX0L2CDMQIngvVuvbj9c3PsxzpF0jfX",
            #     max_tokens=32_000,  # 输出token上限
            # ),

            ModelType.EMBEDDING: ModelConfig(
                model_name="Qwen/Qwen3-Embedding-8B",
                base_url="https://api.siliconflow.cn/v1",
                api_key="sk-ravccrfhdceccblziftcgyzodzjbftdzrozbofbxrhayptnd",
                max_tokens=32_000,
            ),
            ModelType.EMBEDDING_BACKUP: ModelConfig(
                model_name="Qwen/Qwen3-Embedding-8B",
                base_url="https://api.siliconflow.cn/v1",
                api_key="sk-estsvbjfjifgluhxahrrmtnjdhfcodzdlkehtxyonzyizbmx", # for demo
                max_tokens=32_000,
            ),
        }
        # 缓存客户端以实现连接池与TLS会话复用
        self._clients: dict[ModelType, LLMClient] = {}

    def _get_backup_type(self, model_type: ModelType) -> ModelType:
        mapping = {
            ModelType.NORMAL: ModelType.NORMAL_BACKUP,
            ModelType.FLASH: ModelType.FLASH_BACKUP,
            ModelType.EMBEDDING: ModelType.EMBEDDING_BACKUP,
        }
        return mapping.get(model_type, model_type)

    def get_client(self, model_type: ModelType) -> LLMClient:
        """获取模型客户端"""
        if model_type not in self._clients:
            self._clients[model_type] = LLMClient(self._configs[model_type])
        return self._clients[model_type]

    def get_config(self, model_type: ModelType) -> ModelConfig:
        """获取模型配置"""
        return self._configs[model_type]

    async def chat_completion(
        self, prompt: str, model_type: ModelType = ModelType.NORMAL, **kwargs
    ) -> str:
        """聊天补全"""
        # print(f"entry chat_completion: {model_type}")
        client = self.get_client(model_type)
        try:
            return await client.chat_completion(prompt, **kwargs)
        except Exception:
            backup_type = self._get_backup_type(model_type)
            client = self.get_client(backup_type)
            return await client.chat_completion(prompt, **kwargs)

    async def embedding(
        self, text: str, model_type: ModelType = ModelType.EMBEDDING
    ) -> list:
        """文本嵌入"""
        # print(f"entry embedding: {model_type}")
        client = self.get_client(model_type)
        try:
            return await client.embedding(text)
        except Exception:
            backup_type = self._get_backup_type(model_type)
            client = self.get_client(backup_type)
            return await client.embedding(text)

    async def batch_embedding(
        self, texts: list[str], model_type: ModelType = ModelType.EMBEDDING
    ) -> list[list[float]]:
        """批量文本嵌入（单次POST）"""
        client = self.get_client(model_type)
        try:
            return await client.batch_embedding(texts)
        except Exception:
            backup_type = self._get_backup_type(model_type)
            client = self.get_client(backup_type)
            return await client.batch_embedding(texts)

    def list_models(self) -> list:
        """列出所有可用模型"""
        return [model_type.value for model_type in ModelType]


# 全局模型管理器实例
model_manager = ModelManager()

# 全局忽略配置实例
ignore_config = IgnoreConfig()


# 便捷函数
async def llm_call(prompt: str, model_type: str = "normal", **kwargs) -> str:
    """便捷的LLM调用函数"""
    return await model_manager.chat_completion(prompt, ModelType(model_type), **kwargs)


async def embedding_call(text: str, model_type: str = "embedding") -> list:
    """便捷的嵌入调用函数"""
    return await model_manager.embedding(text, ModelType(model_type))


def _run_async_in_new_loop(coro):
    """在新的事件循环中运行协程"""
    try:
        loop = asyncio.get_event_loop()
        if loop.is_running():
            # 如果当前线程已有运行的事件循环，创建新线程
            def run_in_thread():
                new_loop = asyncio.new_event_loop()
                asyncio.set_event_loop(new_loop)
                try:
                    return new_loop.run_until_complete(coro)
                finally:
                    new_loop.close()

            import concurrent.futures

            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(run_in_thread)
                return future.result()
        else:
            # 如果当前线程没有运行的事件循环，直接运行
            return loop.run_until_complete(coro)
    except Exception as e:
        raise Exception(f"异步执行失败: {str(e)}") from e


# 同步包装器（如果需要）
def sync_llm_call(prompt: str, model_type: str = "normal", **kwargs) -> str:
    """同步LLM调用函数"""
    return _run_async_in_new_loop(llm_call(prompt, model_type, **kwargs))


def sync_embedding_call(text: str, model_type: str = "embedding") -> list:
    """同步嵌入调用函数"""
    return _run_async_in_new_loop(embedding_call(text, model_type))


# 向后兼容的接口
def __llm_call(prompt: str, type: str = "normal") -> str:
    """向后兼容的LLM调用函数"""
    return sync_llm_call(prompt, type)


def __embedding_call(prompt: str, type: str = "embedding") -> list:
    """向后兼容的嵌入调用函数"""
    return sync_embedding_call(prompt, type)


# ============= Codebase Retriever 配置 =============

# 支持的编程语言配置
SUPPORTED_LANGUAGES = {
    "python": [".py"],
    "javascript": [".js"],
    "typescript": [".ts"],
    "java": [".java"],
    "c": [".c", ".h"],
    "cpp": [".cpp", ".cc", ".cxx", ".hpp", ".hxx"],
    "rust": [".rs"],
    "arkts": [".ets"],
    "markdown": [".md", ".markdown"],
}

# Tree-sitter配置
TREE_SITTER_CONFIG = {
    "chunk_min_lines": 3,
    "chunk_max_lines": 120,  # 增加最大行数，避免过度分割
    "function_max_lines": 200,  # 增加单个函数的最大行数，保持函数完整性
    "prefer_function_granularity": True,  # 优先按函数粒度分片
    "symbol_types": [
        "function_definition",
        "class_definition",
        "method_definition",
        "interface_declaration",
    ],
    "context_lines": 1,  # 分片前后包含的上下文行数
}

# 检索配置
RETRIEVAL_CONFIG = {
    "ripgrep_timeout": 10,
    "max_ripgrep_results": 100,
    "embedding_top_k": 50,
    "llm_filter_threshold": 0.5,
    "max_query_rewrite_attempts": 3,
}

# 服务配置
SERVICE_CONFIG = {
    "host": "127.0.0.1",
    "port": 5001,
    "workers": 1,
    "index_cache_dir": ".codebase_index",
    "index_file_name": "repo_index.json",
}

# 洞察生成配置
INSIGHT_CONFIG = {
    "batch_size": 10,  # 增加批量处理的分片数量以提高并发
    "max_insight_length": 400,  # 分片洞察的最大长度
    "max_file_insight_length": 800,  # 文件级洞察的最大长度（字数上限）
    "max_concurrent_requests": 20,  # 最大并发请求数
    "insight_prompt_template": """你是资深代码审阅助手。基于给定代码，输出功能描述（≤300字）。
必须严格遵循：
- 不要复述任何文件名/路径；
- 不要输出代码片段、反引号或 Markdown；不要用“这段代码/该函数/本文件”等虚词，直接描述做了什么。
【文件信息】（无需复述）
路径: {file_path}
类型: {chunk_type}
符号: {symbol_name}
语言: {language}
【文件内容】
```{language}
{code_content}
```
只输出功能描述：
""",
    "file_insight_prompt_template": """你是资深代码审阅助手，请基于提供的文件信息与代码内容，输出该文件的功能总结。
必须严格遵循：
1) 语言：中文；长度：不超过300字；只输出最终总结。
2) 不要复述文件名/路径。
3) 不要输出代码片段、反引号或 Markdown 结构。
4) 关键函数/类/方法/接口等可以点名，但不要硬造细节。
5) 允许结合常识做保守判断，但不要杜撰不存在的模块/行为。

【文件信息】（无需复述）
路径: {file_path}
语言: {language}

【文件内容】
```{language}
{code_content}
```

只输出文件功能总结，以中文句号结尾：
""",
}

# 关键词提取配置
KEYWORD_CONFIG = {
    "min_keyword_length": 2,
    "max_keywords_per_chunk": 20,
    "include_file_path_keywords": True,
    "keyword_weight": {
        "function_name": 1.0,
        "class_name": 1.0,
        "variable_name": 0.8,
        "file_path": 0.6,
    },
}

# 嵌入计算配置
EMBEDDING_CONFIG = {
    "batch_size": 10,  # 增加批量计算嵌入的数量
    "max_text_length": 32_000 * 2,  # 单个文本的最大长度
    "embedding_dimension": 4096,  # 嵌入向量维度（根据模型调整）
    "max_concurrent_requests": 25,  # 最大并发请求数
    "similarity_threshold": 0.25,  # 最低相似度阈值
}

# 索引管理配置
INDEX_CONFIG = {
    "enable_persistence": True,
    "compression_enabled": True,
    "max_concurrent_files": 20,  # 最大并发处理文件数
}

# ============= Symbol Retriever 配置 =============

# 符号提取配置
SYMBOL_CONFIG = {
    "extract_private_symbols": True,  # 是否提取私有符号
    "extract_local_variables": False,  # 是否提取局部变量
    "extract_imports": True,  # 是否提取导入信息
    "extract_comments": True,  # 是否提取注释信息
    "max_symbol_depth": 10,  # 最大符号嵌套深度
    "supported_languages": [  # 支持的语言列表
        "python",
        "javascript",
        "typescript",
        "java",
        "c",
        "cpp",
        "rust",
        "arkts",
    ],
    "symbol_types_by_language": {  # 每种语言支持的符号类型
        "python": ["function", "method", "class", "variable", "constant", "import"],
        "javascript": ["function", "method", "class", "variable", "constant", "import"],
        "typescript": [
            "function",
            "method",
            "class",
            "interface",
            "type_alias",
            "variable",
            "constant",
            "import",
        ],
        "java": ["method", "class", "interface", "field", "enum", "import"],
        "c": ["function", "variable", "constant"],
        "cpp": ["function", "method", "class", "variable", "constant", "namespace"],
        "rust": ["function", "method", "class", "enum", "constant", "module"],
        "arkts": ["function", "method", "class", "interface", "variable", "constant"],
    },
}

# 符号索引配置
SYMBOL_INDEX_CONFIG = {
    "enable_fuzzy_search": True,  # 启用模糊搜索
    "fuzzy_threshold": 0.8,  # 模糊匹配阈值
    "max_search_results": 100,  # 最大搜索结果数
    "enable_symbol_cache": True,  # 启用符号缓存
    "cache_size_limit": 10000,  # 缓存大小限制
    "index_update_batch_size": 1000,  # 批量更新大小
    "enable_reference_tracking": True,  # 启用引用追踪
    "max_reference_depth": 5,  # 最大引用深度
    "symbol_index_file": "symbol_index.json",  # 符号索引文件名
}

# 符号检索配置
SYMBOL_RETRIEVAL_CONFIG = {
    "default_max_results": 50,  # 默认最大结果数
    "enable_context_search": True,  # 启用上下文搜索
    "context_weight": 0.3,  # 上下文权重
    "name_match_weight": 0.7,  # 名称匹配权重
    "type_match_bonus": 0.1,  # 类型匹配加分
    "file_proximity_bonus": 0.05,  # 文件邻近性加分
    "search_timeout": 10.0,  # 搜索超时时间（秒）
    "enable_dependency_analysis": True,  # 启用依赖分析
    "max_dependency_depth": 3,  # 最大依赖深度
}
