<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>代码分块测试结果可视化</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            background: #f8fafc;
            min-height: 100vh;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            box-shadow: 0 0 0 1px rgba(0,0,0,0.05);
        }

        /* 顶部导航栏 */
        .navbar {
            background: white;
            border-bottom: 1px solid #e2e8f0;
            padding: 1rem 2rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: sticky;
            top: 0;
            z-index: 100;
            backdrop-filter: blur(10px);
        }

        .navbar-brand {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.25rem;
            font-weight: 600;
            color: #1e293b;
        }

        .navbar-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            font-size: 0.875rem;
            font-weight: 500;
            text-decoration: none;
            border: none;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #f1f5f9;
            color: #475569;
            border: 1px solid #e2e8f0;
        }

        .btn-secondary:hover {
            background: #e2e8f0;
        }

        /* 主内容区域 */
        .main-content {
            padding: 2rem;
        }

        /* 欢迎界面 */
        .welcome-screen {
            text-align: center;
            padding: 4rem 2rem;
            max-width: 600px;
            margin: 0 auto;
        }

        .welcome-screen h1 {
            font-size: 2.5rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 1rem;
        }

        .welcome-screen p {
            font-size: 1.125rem;
            color: #64748b;
            margin-bottom: 2rem;
        }

        .file-upload-area {
            border: 2px dashed #cbd5e1;
            border-radius: 1rem;
            padding: 3rem 2rem;
            margin: 2rem 0;
            transition: all 0.2s;
            cursor: pointer;
        }

        .file-upload-area:hover {
            border-color: #3b82f6;
            background: #f8fafc;
        }

        .file-upload-area.dragover {
            border-color: #3b82f6;
            background: #eff6ff;
        }

        .upload-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .upload-text {
            font-size: 1.125rem;
            font-weight: 500;
            color: #475569;
            margin-bottom: 0.5rem;
        }

        .upload-subtext {
            color: #64748b;
            font-size: 0.875rem;
        }

        /* 统计卡片 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 3rem;
        }

        .stat-card {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 0.75rem;
            padding: 1.5rem;
            text-align: center;
            transition: all 0.2s;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .stat-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #64748b;
            font-size: 0.875rem;
            font-weight: 500;
        }

        /* 分块类型统计 */
        .section {
            margin-bottom: 3rem;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .chunk-types-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
        }

        .chunk-type-card {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 0.75rem;
            padding: 1.5rem;
            text-align: center;
            transition: all 0.2s;
        }

        .chunk-type-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .chunk-type-count {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .chunk-type-label {
            font-size: 0.875rem;
            color: #64748b;
            font-weight: 500;
        }

        /* 分块卡片 */
        .chunk-card {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 0.75rem;
            margin-bottom: 1.5rem;
            overflow: hidden;
            transition: all 0.2s;
        }

        .chunk-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .chunk-header {
            background: #f8fafc;
            padding: 1.5rem;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .chunk-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: #1e293b;
        }

        .chunk-meta {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .chunk-type-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .chunk-type-badge.function { background: #dbeafe; color: #1e40af; }
        .chunk-type-badge.class { background: #fef3c7; color: #92400e; }
        .chunk-type-badge.method { background: #d1fae5; color: #065f46; }
        .chunk-type-badge.interface { background: #e0e7ff; color: #3730a3; }
        .chunk-type-badge.other { background: #f3f4f6; color: #374151; }

        .chunk-body {
            padding: 1.5rem;
        }

        .chunk-info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .info-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem;
            background: #f8fafc;
            border-radius: 0.5rem;
            border: 1px solid #e2e8f0;
        }

        .info-icon {
            font-size: 1.25rem;
            flex-shrink: 0;
        }

        .info-content {
            flex: 1;
            min-width: 0;
        }

        .info-label {
            font-size: 0.75rem;
            color: #64748b;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .info-value {
            font-size: 0.875rem;
            color: #1e293b;
            font-weight: 600;
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .chunk-file-path {
            display: flex;
            align-items: flex-start;
            gap: 0.75rem;
            padding: 1rem;
            background: #f8fafc;
            border-radius: 0.5rem;
            border: 1px solid #e2e8f0;
            margin-bottom: 1rem;
        }

        .file-path-value {
            white-space: pre-wrap !important;
            word-break: break-all !important;
            font-size: 0.8rem !important;
            color: #475569 !important;
            line-height: 1.4 !important;
        }

        .code-preview {
            background: #1e293b;
            color: #e2e8f0;
            padding: 0;
            border-radius: 0.5rem;
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            font-size: 0.875rem;
            line-height: 1.5;
            overflow-x: auto;
            white-space: pre-wrap;
            border: 1px solid #334155;
            max-height: 400px;
            overflow-y: auto;
            position: relative;
        }
        
        .code-preview > div {
            padding: 0.25rem 0.5rem;
        }
        
        .code-preview > div:first-child {
            padding-top: 0.5rem;
        }
        
        .code-preview > div:last-child {
            padding-bottom: 0.5rem;
        }

        .code-preview.expandable {
            cursor: pointer;
            transition: max-height 0.3s ease;
        }

        .code-preview.expandable:hover {
            border-color: #60a5fa;
        }

        .code-preview.expanded {
            max-height: none;
        }

        .expand-indicator {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            background: rgba(0, 0, 0, 0.7);
            color: #e2e8f0;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            opacity: 0.8;
            transition: opacity 0.2s;
            z-index: 10;
        }

        .expand-indicator:hover {
            opacity: 1;
        }

        .code-preview .line-number {
            color: #64748b;
            margin-right: 1rem;
            user-select: none;
            display: inline-block;
            width: 3rem;
            text-align: right;
            font-size: 0.75rem;
            vertical-align: top;
        }

        /* 文件信息 */
        .file-info-card {
            background: #eff6ff;
            border: 1px solid #bfdbfe;
            border-radius: 0.75rem;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .file-info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .file-detail {
            background: white;
            padding: 1rem;
            border-radius: 0.5rem;
            border: 1px solid #dbeafe;
        }

        .file-detail-label {
            font-size: 0.75rem;
            color: #3b82f6;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-bottom: 0.5rem;
        }

        .file-detail-value {
            color: #1e293b;
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            font-size: 0.875rem;
            word-break: break-all;
        }

        /* 代码查看器 */
        .code-viewer {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 0.75rem;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .code-content {
            background: #1e293b;
            color: #e2e8f0;
            padding: 0;
            border-radius: 0.5rem;
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            font-size: 0.875rem;
            line-height: 1.6;
            overflow-x: auto;
            max-height: 500px;
            overflow-y: auto;
            border: 1px solid #334155;
        }
        
        .code-content > div {
            padding: 0.25rem 0.5rem;
        }
        
        .code-content > div:first-child {
            padding-top: 0.5rem;
        }
        
        .code-content > div:last-child {
            padding-bottom: 0.5rem;
        }

        .line-number {
            color: #64748b;
            margin-right: 1rem;
            user-select: none;
            display: inline-block;
            width: 3rem;
            text-align: right;
            font-size: 0.75rem;
            vertical-align: top;
        }

        /* 状态提示 */
        .loading, .error {
            text-align: center;
            padding: 3rem;
            display: none;
        }

        .loading.show, .error.show {
            display: block;
        }

        .loading {
            color: #64748b;
        }

        .error {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
            border-radius: 0.75rem;
            margin: 1rem 0;
        }

        .timestamp {
            text-align: center;
            padding: 1rem;
            color: #64748b;
            font-size: 0.875rem;
            font-style: italic;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .navbar {
                padding: 1rem;
                flex-direction: column;
                gap: 1rem;
            }

            .main-content {
                padding: 1rem;
            }

            .welcome-screen {
                padding: 2rem 1rem;
            }

            .welcome-screen h1 {
                font-size: 2rem;
            }

            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                gap: 1rem;
            }

            .chunk-header {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }

            .chunk-meta {
                justify-content: center;
            }

            .chunk-info-grid {
                grid-template-columns: 1fr;
            }
        }

        /* 隐藏文件输入 */
        #jsonFileInput {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 顶部导航栏 -->
        <nav class="navbar">
            <div class="navbar-brand">
                <span>🔍</span>
                <span>代码分块分析器</span>
            </div>
            <div class="navbar-actions">
                <input type="file" id="jsonFileInput" accept=".json">
                <button onclick="document.getElementById('jsonFileInput').click()" class="btn btn-primary">
                    📁 选择文件
                </button>
                <button onclick="loadSampleData()" class="btn btn-secondary">
                    🚀 示例数据
                </button>
            </div>
        </nav>

        <div class="main-content">
            <!-- 欢迎界面 -->
            <div class="welcome-screen" id="welcomeScreen">
                <h1>代码分块分析器</h1>
                <p>强大的代码分块测试结果可视化工具</p>
                
                <div class="file-upload-area" id="dropZone">
                    <div class="upload-icon">📄</div>
                    <div class="upload-text">拖拽JSON文件到这里</div>
                    <div class="upload-subtext">支持 test_results.json 格式</div>
                </div>
            </div>

            <!-- 加载状态 -->
            <div class="loading" id="loading">
                <h3>🔄 正在加载文件...</h3>
                <p>请稍候，正在解析JSON数据</p>
            </div>

            <!-- 错误提示 -->
            <div class="error" id="error">
                <h4>❌ 加载失败</h4>
                <p id="errorMessage"></p>
            </div>

            <!-- 主要内容区域 -->
            <div id="mainContent" style="display: none;">
                <!-- 统计概览 -->
                <div class="stats-grid" id="statsGrid">
                    <!-- 动态生成统计卡片 -->
                </div>

                <!-- 分块类型统计 -->
                <div class="section">
                    <h2 class="section-title">
                        <span>📊</span>
                        <span>分块类型分布</span>
                    </h2>
                    <div class="chunk-types-grid" id="chunkTypes">
                        <!-- 动态生成分块类型 -->
                    </div>
                </div>

                <!-- 文件信息 -->
                <div class="file-info-card" id="fileInfo">
                    <!-- 动态生成文件信息 -->
                </div>

                <!-- 分块详情 -->
                <div class="section">
                    <h2 class="section-title">
                        <span>🔍</span>
                        <span>分块详情</span>
                    </h2>
                    <div id="chunksContainer">
                        <!-- 动态生成分块卡片 -->
                    </div>
                </div>

                <!-- 代码查看器 -->
                <div class="code-viewer">
                    <h2 class="section-title">
                        <span>💻</span>
                        <span>源代码查看器</span>
                    </h2>
                    <div class="code-content" id="rawCodeContent">
                        <!-- 原始代码内容 -->
                    </div>
                </div>

                <!-- 时间戳 -->
                <div class="timestamp" id="timestamp">
                    <!-- 动态生成时间戳 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // 文件加载功能
        function loadJsonFile(file) {
            const loading = document.getElementById('loading');
            const error = document.getElementById('error');
            
            // 显示加载状态
            loading.classList.add('show');
            error.classList.remove('show');
            
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const jsonData = JSON.parse(e.target.result);
                    
                    // 处理新的综合格式或旧的数组格式
                    if (jsonData.test_results) {
                        // 新格式：包含test_summary和test_results的综合文件
                        testData = jsonData.test_results;
                    } else if (Array.isArray(jsonData)) {
                        // 旧格式：直接是数组
                        testData = jsonData;
                    } else {
                        // 单个对象
                        testData = [jsonData];
                    }
                    
                    // 隐藏加载状态
                    loading.classList.remove('show');
                    
                    // 隐藏欢迎界面，显示主要内容
                    document.getElementById('welcomeScreen').style.display = 'none';
                    document.getElementById('mainContent').style.display = 'block';
                    
                    // 重新渲染所有内容
                    renderAll();
                } catch (err) {
                    // 显示错误
                    loading.classList.remove('show');
                    error.classList.add('show');
                    document.getElementById('errorMessage').textContent = `JSON解析失败: ${err.message}`;
                }
            };
            
            reader.onerror = function() {
                loading.classList.remove('show');
                error.classList.add('show');
                document.getElementById('errorMessage').textContent = '文件读取失败';
            };
            
            reader.readAsText(file);
        }

        // 文件输入事件监听
        document.getElementById('jsonFileInput').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                loadJsonFile(file);
            }
        });

        // 拖拽功能
        const dropZone = document.getElementById('dropZone');
        
        // 阻止默认拖拽行为
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, preventDefaults, false);
            document.body.addEventListener(eventName, preventDefaults, false);
        });

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        // 拖拽进入和离开
        ['dragenter', 'dragover'].forEach(eventName => {
            dropZone.addEventListener(eventName, highlight, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, unhighlight, false);
        });

        function highlight(e) {
            dropZone.classList.add('dragover');
        }

        function unhighlight(e) {
            dropZone.classList.remove('dragover');
        }

        // 处理文件拖拽
        dropZone.addEventListener('drop', handleDrop, false);

        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;
            
            if (files.length > 0) {
                const file = files[0];
                if (file.type === 'application/json' || file.name.endsWith('.json')) {
                    loadJsonFile(file);
                } else {
                    showError('请选择JSON文件');
                }
            }
        }

        // 点击拖拽区域选择文件
        dropZone.addEventListener('click', function() {
            document.getElementById('jsonFileInput').click();
        });

        // 显示错误信息
        function showError(message) {
            const error = document.getElementById('error');
            error.classList.add('show');
            document.getElementById('errorMessage').textContent = message;
        }

        // 加载示例数据
        function loadSampleData() {
            const sampleData = [
                {
                    "total_chunks": 2,
                    "function_chunks": 0,
                    "class_chunks": 0,
                    "method_chunks": 0,
                    "interface_chunks": 0,
                    "other_chunks": 2,
                    "markdown_chunks": 0,
                    "avg_chunk_lines": 70.5,
                    "max_chunk_lines": 120,
                    "min_chunk_lines": 21,
                    "chunks_detail": [
                        {
                            "id": "797863b62e97d36b01f5aa817811c389",
                            "symbol_name": "chunk_1",
                            "chunk_type": "other",
                            "start_line": 1,
                            "end_line": 120,
                            "line_count": 120,
                            "content_preview": "import { showMessage } from '../componets/common/promptShow'\nimport ReadShow from '../componets/common/ReadShow'\nimport { NavItem } from '../componets/dataList/type'\nimport { updateAppData } from '../...",
                            "file_path": "/Users/<USER>/projects/hmos_projects/oh-example-composer/legado-Harmony/entry/src/main/ets/pages/Index.ets"
                        },
                        {
                            "id": "35e349cab1e79f245510c6ceca666ed5",
                            "symbol_name": "chunk_2",
                            "chunk_type": "other",
                            "start_line": 121,
                            "end_line": 141,
                            "line_count": 21,
                            "content_preview": "            }\n          }.tabBar(\n            this.TabBuilder(item.index,item.title,item.selectImg,item.unselected)\n          )\n        })\n      }\n      .barHeight(this.ShowBar?null:0)\n      .scrollab...",
                            "file_path": "/Users/<USER>/projects/hmos_projects/oh-example-composer/legado-Harmony/entry/src/main/ets/pages/Index.ets"
                        }
                    ],
                    "sample_name": "code_Index.ets",
                    "file_info": {
                        "path": "/Users/<USER>/projects/hmos_projects/oh-example-composer/legado-Harmony/entry/src/main/ets/pages/Index.ets",
                        "relative_path": "Index.ets",
                        "language": "arkts",
                        "total_lines": 141,
                        "size": 4746
                    },
                    "parse_tree": {
                        "type": "simple_parse",
                        "symbols": [],
                        "lines": [
                            "import { showMessage } from '../componets/common/promptShow'",
                            "import ReadShow from '../componets/common/ReadShow'",
                            "import { NavItem } from '../componets/dataList/type'",
                            "import { updateAppData } from '../storage/appData'",
                            "import BookShelf from './view/BookShelf'",
                            "import Find from './view/Find/Find'",
                            "import MyCenter from './view/myCenter/MyCenter'",
                            "import Subscription from './view/Subscription'",
                            "import {testGetColorFunction} from '@ohos/colorLibrary'",
                            "import { BusinessError, emitter } from '@kit.BasicServicesKit'",
                            "import { JSON } from '@kit.ArkTS'",
                            "import { image } from '@kit.ImageKit'",
                            "import { ThemeStorageKey } from '../common/constants/Theme'",
                            "import { ThemeItem } from '../common/model/Theme'",
                            "",
                            "@Entry",
                            "@Component",
                            "struct Main {",
                            "  @State showReadShow:boolean = true",
                            "  @State currentTabIndex: number = 0",
                            "  @State isShow: boolean = true",
                            "  @StorageLink('bottomRectHeight') bottomRectHeight: number = 0",
                            "  @StorageLink('topRectHeight') topRectHeight: number = 0",
                            "  @StorageProp('APP_INDEX_SCROLLABLE') APP_INDEX_SCROLLABLE: boolean = true",
                            "  @State ShowBar: boolean = true",
                            "  tabController: TabsController = new TabsController()",
                            "",
                            "  // 主题颜色",
                            "  @StorageProp(ThemeStorageKey.THEME) theme: ThemeItem = {} as ThemeItem",
                            "",
                            "  onPageShow(): void {",
                            "    // initBookListData()",
                            "    setTimeout(()=>{",
                            "      this.showReadShow = false",
                            "      this.isShow = false",
                            "    }, 5000)",
                            "",
                            "    getContext(this).eventHub.on('SubscriptionBatchEdit', (params: boolean) => {",
                            "      this.ShowBar = params",
                            "      this.APP_INDEX_SCROLLABLE = !this.APP_INDEX_SCROLLABLE",
                            "      console.log('SubscriptionBatchEdit:' + params)",
                            "    })",
                            "  }",
                            "",
                            "  screenShot() {",
                            "    // 切换回首页",
                            "    let index = this.currentTabIndex",
                            "    this.tabController.changeIndex(0)",
                            "    this.getUIContext().getComponentSnapshot().get('main', ((err: Error, pixmap: image.PixelMap) => {",
                            "      if (err) {",
                            "        console.log(\"error: \" + JSON.stringify(err))",
                            "        return",
                            "      }",
                            "      AppStorage.setOrCreate('screen_shot', pixmap)",
                            "    }))",
                            "    // 恢复当前页",
                            "    this.tabController.changeIndex(index)",
                            "  }",
                            "",
                            "  aboutToAppear(): void {",
                            "    // 注册监听截图事件 用于我的页面下主题设置预览",
                            "    emitter.on('screen_shot', () => {",
                            "      this.screenShot()",
                            "    })",
                            "    //测试颜色模块",
                            "    testGetColorFunction();",
                            "  }",
                            "",
                            "  @Builder TabBuilder(index = 0, name: string,icon_select: Resource,unselected: Resource) {",
                            "    Column(){",
                            "      Flex({",
                            "        direction:FlexDirection.Column,",
                            "        alignItems:ItemAlign.Center,",
                            "        justifyContent:FlexAlign.Center",
                            "      }){",
                            "        Image(this.currentTabIndex === index? icon_select:unselected).height(25)",
                            "          .draggable(false)",
                            "          .fillColor(this.currentTabIndex === index ? this.theme.mainColor : '#000000')",
                            "        Text(name).fontSize(11).margin(5).fontColor(this.currentTabIndex === index ? this.theme.mainColor : '#000000')",
                            "      }",
                            "    }.gesture(",
                            "      LongPressGesture({ repeat: index === 0,duration:1000 })",
                            "        .onAction((event: GestureEvent) => {",
                            "          console.log('onAction' + event.repeat)",
                            "          if (event.repeat) {",
                            "            updateAppData(!this.APP_INDEX_SCROLLABLE)",
                            "            showMessage(this.APP_INDEX_SCROLLABLE?'标签栏锁已开启':'标签栏已解锁')}}))",
                            "    .width('100%')",
                            "    .margin({top:4})",
                            "    .height(\"100%\")",
                            "  }",
                            "",
                            "  nav : NavItem[] = [",
                            "    new NavItem(0,$r(\"app.media.HOME_theme\"),$r('app.media.HOME_line') ,'书架'),",
                            "    new NavItem(1,$r('app.media.FIND_theme'), $r('app.media.FIND_line') ,'发现'),",
                            "    new NavItem(2,$r('app.media.SUB_theme'),$r('app.media.SUB_line'), '订阅源'),",
                            "    new NavItem(3, $r('app.media.MINE_theme'),$r('app.media.MINE_line'), '我的')",
                            "  ]",
                            "",
                            "  build() {",
                            "    Column() {",
                            "      Tabs({",
                            "        barPosition:BarPosition.End,",
                            "        controller: this.tabController",
                            "      }){",
                            "        ForEach(this.nav,(item:NavItem) => {",
                            "          TabContent() {",
                            "            Column() {",
                            "              if (item.index === 0){",
                            "                BookShelf()",
                            "              } else if (item.index === 1){",
                            "                Find()",
                            "              } else if (item.index === 2){",
                            "                Subscription({",
                            "                  currentTabIndex:this.currentTabIndex",
                            "                })",
                            "              }",
                            "              if (item.index === 3){",
                            "                MyCenter()",
                            "              }",
                            "            }",
                            "          }.tabBar(",
                            "            this.TabBuilder(item.index,item.title,item.selectImg,item.unselected)",
                            "          )",
                            "        })",
                            "      }",
                            "      .barHeight(this.ShowBar?null:0)",
                            "      .scrollable(!this.APP_INDEX_SCROLLABLE)",
                            "      .padding({bottom:this.bottomRectHeight})",
                            "      .onChange((index) => {",
                            "        this.currentTabIndex = index",
                            "      })",
                            "      if (this.showReadShow){",
                            "        ReadShow({showReadShow:this.showReadShow}).offset({y:-150 - this.bottomRectHeight})",
                            "      }",
                            "    }",
                            "    .width(\"100%\")",
                            "    .height(\"100%\")",
                            "    .id('main')",
                            "  }",
                            "}"
                        ]
                    },
                    "timestamp": "2025-09-10T10:31:59.162926"
                }
            ];
            
            testData = sampleData;
            document.getElementById('error').classList.remove('show');
            
            // 隐藏欢迎界面，显示主要内容
            document.getElementById('welcomeScreen').style.display = 'none';
            document.getElementById('mainContent').style.display = 'block';
            
            renderAll();
        }

        // 渲染统计卡片
        function renderStats() {
            if (!testData || testData.length === 0) return;
            const data = testData[0];
            const statsGrid = document.getElementById('statsGrid');
            
            const stats = [
                { label: '总分块数', value: data.total_chunks },
                { label: '平均行数', value: data.avg_chunk_lines },
                { label: '最大行数', value: data.max_chunk_lines },
                { label: '最小行数', value: data.min_chunk_lines }
            ];

            statsGrid.innerHTML = stats.map(stat => `
                <div class="stat-card">
                    <div class="stat-value">${stat.value}</div>
                    <div class="stat-label">${stat.label}</div>
                </div>
            `).join('');
        }

        // 渲染分块类型
        function renderChunkTypes() {
            if (!testData || testData.length === 0) return;
            const data = testData[0];
            const chunkTypes = document.getElementById('chunkTypes');
            
            const types = [
                { label: '函数', count: data.function_chunks },
                { label: '类', count: data.class_chunks },
                { label: '方法', count: data.method_chunks },
                { label: '接口', count: data.interface_chunks },
                { label: '其他', count: data.other_chunks },
                { label: 'Markdown', count: data.markdown_chunks }
            ];

            chunkTypes.innerHTML = types.map(type => `
                <div class="chunk-type-card">
                    <div class="chunk-type-count">${type.count}</div>
                    <div class="chunk-type-label">${type.label}</div>
                </div>
            `).join('');
        }

        // 渲染文件信息
        function renderFileInfo() {
            if (!testData || testData.length === 0) return;
            const data = testData[0];
            const fileInfo = document.getElementById('fileInfo');
            
            fileInfo.innerHTML = `
                <h2 style="color: #1e40af; margin-bottom: 1rem; font-size: 1.25rem; font-weight: 600;">📁 文件信息</h2>
                <div class="file-info-grid">
                    <div class="file-detail">
                        <div class="file-detail-label">文件名</div>
                        <div class="file-detail-value">${data.file_info.relative_path}</div>
                    </div>
                    <div class="file-detail">
                        <div class="file-detail-label">语言</div>
                        <div class="file-detail-value">${data.file_info.language}</div>
                    </div>
                    <div class="file-detail">
                        <div class="file-detail-label">总行数</div>
                        <div class="file-detail-value">${data.file_info.total_lines}</div>
                    </div>
                    <div class="file-detail">
                        <div class="file-detail-label">文件大小</div>
                        <div class="file-detail-value">${data.file_info.size} bytes</div>
                    </div>
                </div>
            `;
        }

        // 渲染分块详情
        function renderChunks() {
            if (!testData || testData.length === 0) return;
            const data = testData[0];
            const chunksContainer = document.getElementById('chunksContainer');
            
            chunksContainer.innerHTML = data.chunks_detail.map((chunk, index) => `
                <div class="chunk-card">
                    <div class="chunk-header">
                        <div class="chunk-title">${chunk.symbol_name}</div>
                        <div class="chunk-meta">
                            <span class="chunk-type-badge ${chunk.chunk_type}">${chunk.chunk_type}</span>
                            <span>${chunk.line_count}行</span>
                        </div>
                    </div>
                    <div class="chunk-body">
                        <div class="chunk-info-grid">
                            <div class="info-item">
                                <div class="info-icon">📍</div>
                                <div class="info-content">
                                    <div class="info-label">行号范围</div>
                                    <div class="info-value">${chunk.start_line}-${chunk.end_line}</div>
                                </div>
                            </div>
                            <div class="info-item">
                                <div class="info-icon">📏</div>
                                <div class="info-content">
                                    <div class="info-label">行数</div>
                                    <div class="info-value">${chunk.line_count}行</div>
                                </div>
                            </div>
                            <div class="info-item">
                                <div class="info-icon">📁</div>
                                <div class="info-content">
                                    <div class="info-label">文件名</div>
                                    <div class="info-value" title="${chunk.file_path}">${chunk.file_path.split('/').pop()}</div>
                                </div>
                            </div>
                            <div class="info-item">
                                <div class="info-icon">🆔</div>
                                <div class="info-content">
                                    <div class="info-label">ID</div>
                                    <div class="info-value">${chunk.id.substring(0, 8)}...</div>
                                </div>
                            </div>
                        </div>
                        <div class="chunk-file-path">
                            <div class="info-icon">🗂️</div>
                            <div class="info-content">
                                <div class="info-label">完整路径</div>
                                <div class="info-value file-path-value" title="${chunk.file_path}">${chunk.file_path}</div>
                            </div>
                        </div>
                        <div class="code-preview expandable" onclick="toggleCodePreview(this)">
                            <div class="expand-indicator">点击展开/收起</div>
                            ${addLineNumbers(chunk.content_preview, chunk.start_line)}
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // 渲染原始代码
        function renderRawCode() {
            if (!testData || testData.length === 0) return;
            const data = testData[0];
            const rawCodeContent = document.getElementById('rawCodeContent');
            
            const codeHtml = data.parse_tree.lines.map((line, index) => {
                const lineNum = String(index + 1).padStart(3, '0');
                // 保持空格的完整性，将空格转换为&nbsp;，空行显示&nbsp;
                const escapedLine = line === '' ? '&nbsp;' : escapeHtml(line).replace(/ /g, '&nbsp;');
                return `<div><span class="line-number">${lineNum}</span>${escapedLine}</div>`;
            }).join('');
            
            rawCodeContent.innerHTML = codeHtml;
        }


        // 渲染时间戳
        function renderTimestamp() {
            if (!testData || testData.length === 0) return;
            const data = testData[0];
            const timestamp = document.getElementById('timestamp');
            
            const date = new Date(data.timestamp);
            const formattedDate = date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            
            timestamp.innerHTML = `测试时间: ${formattedDate}`;
        }


        // HTML转义函数
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 添加行号函数
        function addLineNumbers(code, startLine) {
            const lines = code.split('\n');
            return lines.map((line, index) => {
                const lineNum = startLine + index;
                // 保持空格的完整性，将空格转换为&nbsp;，空行显示&nbsp;
                const escapedLine = line === '' ? '&nbsp;' : escapeHtml(line).replace(/ /g, '&nbsp;');
                return `<div><span class="line-number">${String(lineNum).padStart(3, '0')}</span>${escapedLine}</div>`;
            }).join('');
        }

        // 切换代码预览展开/收起
        function toggleCodePreview(element) {
            element.classList.toggle('expanded');
            const indicator = element.querySelector('.expand-indicator');
            if (element.classList.contains('expanded')) {
                indicator.textContent = '点击收起';
            } else {
                indicator.textContent = '点击展开/收起';
            }
        }

        // 渲染所有内容
        function renderAll() {
            renderStats();
            renderChunkTypes();
            renderFileInfo();
            renderChunks();
            renderRawCode();
            renderTimestamp();
        }


        // 初始化页面
        function init() {
            // 页面加载时不自动渲染，等待用户加载文件
            console.log('页面已加载，请点击"加载JSON文件"按钮选择文件');
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
