#!/usr/bin/env python3
"""
更新版 test_chunker.py - 使用迁移到chunker.py的新功能
集成链式symbol_name格式、智能chunk合并、通用装饰器处理等高级功能
"""

import json
import os
import re
import sys
import traceback
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
from codebase_processor.chunker import CodeChunker
from config import TREE_SITTER_CONFIG
from utils.helpers import get_file_language, get_relative_path
from utils.models import CodeChunk, FileInfo


class UpdatedChunkerTester:
    """更新版测试器，使用chunker.py的增强功能"""

    def __init__(self):
        self.chunker = CodeChunker()
        # 启用智能合并功能
        self.chunker.enable_smart_merge = True
        self.test_results = []

    def create_file_info(self, file_path: str) -> Optional[FileInfo]:
        """从文件路径创建FileInfo对象"""
        try:
            if not os.path.exists(file_path):
                print(f"❌ 文件不存在: {file_path}")
                return None

            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()

            repo_path = Path(file_path).parent
            relative_path = get_relative_path(file_path, str(repo_path))
            language = get_file_language(file_path) or "unknown"
            size = len(content.encode("utf-8"))
            crc32 = format(abs(hash(content)) & 0xFFFFFFFF, "08x")

            return FileInfo(
                file_path=file_path,
                relative_path=relative_path,
                language=language,
                content=content,
                size=size,
                crc32=crc32,
            )
        except Exception as e:
            print(f"❌ 创建文件信息失败: {str(e)}")
            return None

    def create_comprehensive_parse_tree(self, file_info: FileInfo) -> Dict[str, Any]:
        """创建全面的解析树 - 使用chunker.py的增强解析功能"""
        print(f"🔍 开始全面解析 {file_info.language} 文件...")

        # 使用chunker.py的增强解析功能
        parse_tree = self.chunker.create_enhanced_parse_tree(file_info)

        # 转换为兼容格式（保持原有接口）
        return {
            "type": "simple_parse",
            "symbols": parse_tree["symbols"],
            "lines": parse_tree["lines"],
        }

    def process_single_file(self, file_path: str) -> Optional[Dict[str, Any]]:
        """处理单个文件 - 使用增强的chunker.py功能"""
        print(f"\n🔍 处理文件: {file_path}")

        try:
            # 创建文件信息
            file_info = self.create_file_info(file_path)
            if not file_info:
                return None

            print(f"📄 文件: {file_info.relative_path}")
            print(f"📝 语言: {file_info.language}")
            print(f"📏 大小: {file_info.size} 字节")
            print(f"🔢 行数: {len(file_info.content.splitlines())}")

            # 根据文件类型执行分片
            if file_info.language == "markdown":
                chunks = self.chunker.chunk_markdown(file_info)
                test_name = f"markdown_{os.path.basename(file_path)}"
            else:
                # 创建全面的解析树 - 现在使用增强的chunker.py功能
                parse_tree = self.create_comprehensive_parse_tree(file_info)
                # 使用chunker.py的增强功能进行分片
                chunks = self.chunker.chunk_by_symbols(parse_tree, file_info)
                test_name = f"code_{os.path.basename(file_path)}"

            # 分析结果 - 现在chunks已经包含了链式symbol_name和智能合并
            result = self.analyze_chunks(chunks, file_info)
            result["sample_name"] = test_name
            result["file_info"] = {
                "path": file_info.file_path,
                "relative_path": file_info.relative_path,
                "language": file_info.language,
                "total_lines": len(file_info.content.splitlines()),
                "size": file_info.size,
            }
            # 添加parse_tree以兼容可视化器
            result["parse_tree"] = {
                "type": "simple_parse",
                "symbols": [],  # 简化版本，避免循环引用
                "lines": file_info.content.splitlines(),
            }
            result["timestamp"] = datetime.now().isoformat()

            self.test_results.append(result)

            print(f"✅ 分片结果:")
            print(f"   📊 总分片数: {len(chunks)}")
            print(f"   🔧 函数分片: {result['function_chunks']}")
            print(f"   🏗️ 类分片: {result['class_chunks']}")
            print(f"   ⚙️ 方法分片: {result['method_chunks']}")
            print(f"   📦 其他分片: {result['other_chunks']}")
            print(f"   📑 Markdown分片: {result['markdown_chunks']}")
            print(f"   📊 Import分片: {result['import_chunks']}")
            print(f"   📊 Export分片: {result['export_chunks']}")
            print(f"   📏 平均行数: {result['avg_chunk_lines']:.1f}")
            print(f"   📐 最大行数: {result['max_chunk_lines']}")
            print(f"   📏 最小行数: {result['min_chunk_lines']}")

            # 显示链式symbol_name示例
            chain_symbols = [
                chunk["symbol_name"]
                for chunk in result["chunks_detail"]
                if "." in chunk["symbol_name"]
            ]
            if chain_symbols:
                print(f"   🔗 链式symbol_name示例: {chain_symbols[:3]}")

            return result

        except Exception as e:
            print(f"❌ 文件处理失败: {str(e)}")
            traceback.print_exc()
            return None

    def analyze_chunks(
        self, chunks: List[CodeChunk], file_info: FileInfo
    ) -> Dict[str, Any]:
        """分析分片结果 - 现在使用chunker.py内置的智能合并"""
        if not chunks:
            return {
                "total_chunks": 0,
                "function_chunks": 0,
                "class_chunks": 0,
                "method_chunks": 0,
                "interface_chunks": 0,
                "other_chunks": 0,
                "markdown_chunks": 0,
                "import_chunks": 0,
                "export_chunks": 0,
                "decorator_chunks": 0,
                "avg_chunk_lines": 0,
                "max_chunk_lines": 0,
                "min_chunk_lines": 0,
                "chunks_detail": [],
            }

        # 统计不同类型的分片
        chunk_types = {
            "function": 0,
            "class": 0,
            "method": 0,
            "interface": 0,
            "other": 0,
            "markdown_section": 0,
            "import": 0,
            "export": 0,
            "import_section": 0,
            "decorator_section": 0,
            "comment_section": 0,
            "constant_declaration": 0,
            "variable_declaration": 0,
        }

        chunk_lines = []
        chunks_detail = []

        # 获取原始文件内容，用于提取原始代码片段
        original_lines = file_info.content.split("\n")

        for chunk in chunks:
            chunk_type = chunk.chunk_type
            if chunk_type in chunk_types:
                chunk_types[chunk_type] += 1
            else:
                chunk_types["other"] += 1

            lines_count = chunk.end_line - chunk.start_line + 1
            chunk_lines.append(lines_count)

            # 提取原始内容（保持原始格式和行号对齐）
            original_content = self._extract_original_content(
                original_lines, chunk.start_line, chunk.end_line
            )

            chunks_detail.append(
                {
                    "id": chunk.id,
                    "symbol_name": chunk.symbol_name,  # 现在包含链式格式
                    "chunk_type": chunk.chunk_type,
                    "start_line": chunk.start_line,
                    "end_line": chunk.end_line,
                    "line_count": lines_count,
                    "content_preview": original_content,  # 保留完整的原始内容
                    "file_path": chunk.file_path,
                }
            )

        return {
            "total_chunks": len(chunks),
            "function_chunks": chunk_types["function"],
            "class_chunks": chunk_types["class"],
            "method_chunks": chunk_types["method"],
            "interface_chunks": chunk_types["interface"],
            "other_chunks": chunk_types["other"],
            "markdown_chunks": chunk_types["markdown_section"],
            "import_chunks": chunk_types["import"] + chunk_types["import_section"],
            "export_chunks": chunk_types["export"],
            "decorator_chunks": chunk_types["decorator_section"],
            "avg_chunk_lines": sum(chunk_lines) / len(chunk_lines)
            if chunk_lines
            else 0,
            "max_chunk_lines": max(chunk_lines) if chunk_lines else 0,
            "min_chunk_lines": min(chunk_lines) if chunk_lines else 0,
            "chunks_detail": chunks_detail,
        }

    def _extract_original_content(
        self, original_lines: List[str], start_line: int, end_line: int
    ) -> str:
        """提取原始内容，保持原始格式和行号对齐"""
        # 转换为0-based索引
        start_idx = start_line - 1
        end_idx = end_line - 1

        # 确保索引在有效范围内
        start_idx = max(0, start_idx)
        end_idx = min(len(original_lines) - 1, end_idx)

        # 提取指定行范围的内容
        extracted_lines = original_lines[start_idx : end_idx + 1]

        # 保持原始格式，包括空行和空白字符
        return "\n".join(extracted_lines)

    def save_results(self, output_dir: str = "test_chunker_updated_results") -> str:
        """保存测试结果 - 生成符合现有可视化器格式的JSON"""
        ensure_dir(output_dir)

        # 生成符合现有可视化器格式的JSON
        if len(self.test_results) == 1:
            # 单个文件测试 - 直接使用结果格式
            output_data = self.test_results[0]
        else:
            # 多个文件测试 - 包装成数组格式
            output_data = self.test_results

        result_file = os.path.join(output_dir, "test_results.json")
        with open(result_file, "w", encoding="utf-8") as f:
            json.dump(output_data, f, ensure_ascii=False, indent=2)

        print(f"\n📊 更新版分片结果已保存到: {output_dir}")
        print(f"📄 JSON文件: {result_file}")
        print(f"💡 提示: 可以使用 test_results_visualizer.html 查看可视化结果")

        return output_dir


def ensure_dir(directory: str):
    """确保目录存在"""
    Path(directory).mkdir(parents=True, exist_ok=True)


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(
        description="更新版 test_chunker.py - 使用chunker.py的增强功能",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 测试单个文件
  python test_chunker_updated.py --file /path/to/file.py
  
  # 测试ArkTS文件
  python test_chunker_updated.py --file sample_arkts.ets
  
  # 测试整个目录
  python test_chunker_updated.py --dir /path/to/project
  
  # 指定文件扩展名
  python test_chunker_updated.py --dir /path/to/project --extensions .ets .ts .js
        """,
    )

    parser.add_argument("--file", "-f", type=str, help="测试单个文件")
    parser.add_argument("--dir", "-d", type=str, help="测试目录中的文件")
    parser.add_argument("--files", "-fl", nargs="+", help="测试多个文件")
    parser.add_argument(
        "--max-files", "-m", type=int, default=10, help="最大测试文件数量 (默认: 10)"
    )
    parser.add_argument(
        "--extensions",
        "-e",
        nargs="+",
        default=[".py", ".js", ".ts", ".java", ".md", ".arkts", ".ets"],
        help="文件扩展名过滤器 (默认: .py .js .ts .java .md .arkts .ets)",
    )
    parser.add_argument(
        "--output",
        "-o",
        type=str,
        default="test_chunker_updated_results",
        help="输出目录 (默认: test_chunker_updated_results)",
    )

    args = parser.parse_args()

    print("🚀 更新版 test_chunker.py - 使用chunker.py增强功能")
    print(f"🔧 配置: {TREE_SITTER_CONFIG}")
    print("📦 使用分片器: V2递归分片器 (增强版，含链式symbol_name和智能合并)")

    # 创建测试器
    tester = UpdatedChunkerTester()

    # 根据参数执行测试
    if args.file:
        # 测试单个文件
        result = tester.process_single_file(args.file)
        if not result:
            print("❌ 文件处理失败")
            return 1
    elif args.dir:
        # 测试目录
        print(f"📁 目录处理功能待实现")
        return 1
    elif args.files:
        # 测试文件列表
        print(f"📋 测试文件列表 ({len(args.files)} 个文件)")
        success_count = 0
        for i, file_path in enumerate(args.files, 1):
            print(f"\n[{i}/{len(args.files)}]", end=" ")
            result = tester.process_single_file(file_path)
            if result:
                success_count += 1

        print(f"\n✅ 成功处理 {success_count}/{len(args.files)} 个文件")
        if success_count == 0:
            print("❌ 所有文件处理失败")
            return 1
    else:
        print("❌ 请指定要测试的文件或目录")
        parser.print_help()
        return 1

    # 保存结果
    if tester.test_results:
        output_dir = tester.save_results(args.output)
        print(f"\n🎉 更新版分片测试完成! 结果保存在: {output_dir}")
        print(f"🌐 请使用 test_results_visualizer.html 查看可视化结果")
    else:
        print("❌ 没有生成测试结果")
        return 1

    return 0


if __name__ == "__main__":
    sys.exit(main())
