"""
Tree-sitter解析器
使用tree-sitter解析不同编程语言的代码
"""

import logging
import traceback
from typing import Any, Optional

from config import TREE_SITTER_CONFIG
from utils.interfaces import ITreeSitterParser
from utils.models import FileInfo

# 导入ArkTS解析器
from .arkts_parser import arkts_parser

logger = logging.getLogger(__name__)

try:
    import tree_sitter_languages
    from tree_sitter import Parser

    TREE_SITTER_AVAILABLE = True
except ImportError as e:
    TREE_SITTER_AVAILABLE = False
    logger.warning(f"tree-sitter相关库未安装 ({e})，将使用简单的文本解析")


class TreeSitterParser(ITreeSitterParser):
    """Tree-sitter解析器实现"""

    def __init__(self):
        self.parsers = {}
        self.languages = {}

        if TREE_SITTER_AVAILABLE:
            self._init_languages()

        # 支持的符号类型
        self.symbol_types = set(TREE_SITTER_CONFIG["symbol_types"])

    def _init_languages(self):
        """初始化各种编程语言的解析器"""
        # ArkTS使用专门的解析器
        if arkts_parser.is_available():
            self.parsers["arkts"] = arkts_parser
            logger.info("ArkTS解析器已加载")

        # 使用tree_sitter_languages包中的语言
        supported_languages = [
            "python",
            "javascript",
            "typescript",
            "java",
            "c",
            "cpp",
            "rust",
        ]

        for lang in supported_languages:
            try:
                language = tree_sitter_languages.get_language(lang)
                parser = Parser()
                parser.set_language(language)
                self.parsers[lang] = parser
                self.languages[lang] = language
                logger.debug(f"{lang}语言解析器已加载")
            except Exception as e:
                logger.warning(f"无法加载{lang}语言解析器: {e}")

    def parse_file(self, file_info: FileInfo) -> Optional[object]:
        """解析文件并返回解析树"""
        # 特殊处理ArkTS
        if file_info.language == "arkts":
            if arkts_parser.is_available():
                try:
                    tree = arkts_parser.parse(file_info.content)
                    if tree:
                        return {
                            "type": "arkts_parse",
                            "tree": tree,
                            "symbols": arkts_parser.extract_symbols(tree),
                            "lines": file_info.content.split("\n"),
                        }
                except Exception as e:
                    logger.warning(f"ArkTS解析失败 {file_info.file_path}: {e}")

            # ArkTS降级到简单解析
            return self._simple_parse(file_info)

        # 其他语言的处理
        if not TREE_SITTER_AVAILABLE or file_info.language not in self.parsers:
            # 降级到简单文本解析
            return self._simple_parse(file_info)

        try:
            parser = self.parsers[file_info.language]
            tree = parser.parse(bytes(file_info.content, "utf-8"))

            # 为tree-sitter解析结果添加符号信息
            symbols = self._extract_tree_sitter_symbols(tree, file_info.language)

            return {
                "type": "tree_sitter_parse",
                "tree": tree,
                "symbols": symbols,
                "lines": file_info.content.split("\n"),
            }
        except Exception as e:
            traceback.print_exc()
            logger.warning(f"解析文件失败 {file_info.file_path}: {e}")
            return self._simple_parse(file_info)

    def _simple_parse(self, file_info: FileInfo) -> dict[str, Any]:
        logger.debug(f"开始简单解析 {file_info.file_path}")
        """简单的文本解析，作为tree-sitter的降级方案"""
        lines = file_info.content.split("\n")

        # 根据语言类型进行简单的符号识别
        symbols = []

        if file_info.language == "python":
            symbols = self._parse_python_simple(lines)
        elif file_info.language in ["javascript", "typescript"]:
            symbols = self._parse_js_simple(lines)
        elif file_info.language == "java":
            symbols = self._parse_java_simple(lines)
        elif file_info.language in ["c", "cpp"]:
            symbols = self._parse_c_simple(lines)
        elif file_info.language == "rust":
            symbols = self._parse_rust_simple(lines)
        elif file_info.language == "arkts":
            symbols = self._parse_arkts_simple(lines)
        else:
            # 默认按函数模式解析
            symbols = self._parse_generic_simple(lines)

        return {"type": "simple_parse", "symbols": symbols, "lines": lines}

    def _scan_block_curly(self, lines: list[str], start_i: int) -> int:
        """从 start_i 开始向后扫描，找到以花括号包围的代码块的结束行（1-based）。
        适用于 JS/TS/Java/C/CPP/Rust 等。
        若无法找到匹配的 '}', 退化为从 start_i 到文件末尾。
        """
        # 找到起始 '{' 的位置（可能不在同一行）
        i = start_i
        depth = 0
        seen_open = False
        while i < len(lines):
            line = lines[i]
            for ch in line:
                if ch == "{":
                    depth += 1
                    seen_open = True
                elif ch == "}":
                    if depth > 0:
                        depth -= 1
                # 当已见到过开括号并且回到 0，块结束
                if seen_open and depth == 0:
                    return i + 1  # 1-based
            i += 1
        # 没有匹配成功，返回到文件末尾
        return len(lines)

    def _find_python_block_end(self, lines: list[str], start_i: int) -> int:
        """根据缩进推断 Python 块结束行（1-based）。
        start_i 为 0-based 的起始行索引（def/class 所在行）。
        规则：遇到第一个非空、非注释且缩进量 <= 起始缩进的行则视为块结束的前一行。
        若无法判定，则延伸到文件末尾。
        """
        import re as _re

        def indent_of(s: str) -> int:
            return len(s) - len(s.lstrip(" "))

        start_line = lines[start_i]
        base_indent = indent_of(start_line)
        i = start_i + 1
        end = len(lines)
        while i < len(lines):
            s = lines[i]
            stripped = s.strip()
            # 跳过空行与仅注释行
            if stripped == "" or stripped.startswith("#"):
                i += 1
                continue
            # 跳过多行字符串的情况（粗略处理）
            if _re.match(r"^[ruRU]*['\"]{3}", stripped):
                # 向后寻找结束的三引号
                j = i + 1
                while j < len(lines) and not _re.search(r"['\"]{3}$", lines[j].strip()):
                    j += 1
                i = j + 1
                continue
            # 如果缩进回退到 <= base_indent，块结束
            if indent_of(s) <= base_indent:
                end = i
                break
            i += 1
        if end == len(lines):
            # 到文件末尾
            return len(lines)
        else:
            # 结束为上一行
            return max(start_i + 1, end)

    def _parse_python_simple(self, lines: list[str]) -> list[dict[str, Any]]:
        """简单解析Python代码"""
        symbols = []

        for i, line in enumerate(lines):
            stripped = line.strip()

            # 类定义
            if stripped.startswith("class ") and ":" in stripped:
                class_name = (
                    stripped.split("class ")[1].split("(")[0].split(":")[0].strip()
                )
                symbols.append(
                    {
                        "type": "class_definition",
                        "name": class_name,
                        "start_line": i + 1,
                        "end_line": self._find_python_block_end(lines, i),
                        "line": line,
                    }
                )

            # 函数定义
            elif stripped.startswith("def ") and ":" in stripped:
                func_name = stripped.split("def ")[1].split("(")[0].strip()
                symbols.append(
                    {
                        "type": "function_definition",
                        "name": func_name,
                        "start_line": i + 1,
                        "end_line": self._find_python_block_end(lines, i),
                        "line": line,
                    }
                )

        return symbols

    def _parse_js_simple(self, lines: list[str]) -> list[dict[str, Any]]:
        """简单解析JavaScript/TypeScript代码"""
        symbols = []

        for i, line in enumerate(lines):
            stripped = line.strip()

            # 函数定义
            if "function " in stripped and "(" in stripped:
                try:
                    func_part = stripped.split("function ")[1]
                    func_name = func_part.split("(")[0].strip()
                    symbols.append(
                        {
                            "type": "function_definition",
                            "name": func_name,
                            "start_line": i + 1,
                            "end_line": self._scan_block_curly(lines, i),
                            "line": line,
                        }
                    )
                except IndexError:
                    continue

            # 类定义
            elif stripped.startswith("class ") and "{" in stripped:
                try:
                    class_name = (
                        stripped.split("class ")[1].split(" ")[0].split("{")[0].strip()
                    )
                    symbols.append(
                        {
                            "type": "class_definition",
                            "name": class_name,
                            "start_line": i + 1,
                            "line": line,
                        }
                    )
                except IndexError:
                    continue

            # 方法定义（箭头函数等）
            elif "=>" in stripped and "(" in stripped:
                try:
                    # 简单处理箭头函数
                    if "=" in stripped:
                        func_name = stripped.split("=")[0].strip()
                        symbols.append(
                            {
                                "type": "function_definition",
                                "name": func_name,
                                "start_line": i + 1,
                                "end_line": self._scan_block_curly(lines, i),
                                "line": line,
                            }
                        )
                except IndexError:
                    continue

        return symbols

    def _parse_java_simple(self, lines: list[str]) -> list[dict[str, Any]]:
        """简单解析Java代码"""
        symbols = []

        for i, line in enumerate(lines):
            stripped = line.strip()

            # 类定义
            if "class " in stripped and "{" in stripped:
                try:
                    parts = stripped.split("class ")
                    if len(parts) > 1:
                        class_name = parts[1].split(" ")[0].split("{")[0].strip()
                        symbols.append(
                            {
                                "type": "class_definition",
                                "name": class_name,
                                "start_line": i + 1,
                                "end_line": self._scan_block_curly(lines, i),
                                "line": line,
                            }
                        )
                except IndexError:
                    continue

            # 方法定义
            elif (
                (
                    "public " in stripped
                    or "private " in stripped
                    or "protected " in stripped
                )
                and "(" in stripped
                and "{" in stripped
            ):
                try:
                    # 提取方法名
                    parts = stripped.split("(")[0].split()
                    if len(parts) >= 2:
                        method_name = parts[-1]
                        symbols.append(
                            {
                                "type": "method_definition",
                                "name": method_name,
                                "start_line": i + 1,
                                "end_line": self._scan_block_curly(lines, i),
                                "line": line,
                            }
                        )
                except IndexError:
                    continue

        return symbols

    def _parse_c_simple(self, lines: list[str]) -> list[dict[str, Any]]:
        """简单解析C/C++代码"""
        symbols = []

        for i, line in enumerate(lines):
            stripped = line.strip()

            # 函数定义（简单模式）
            if (
                "(" in stripped
                and ")" in stripped
                and "{" in stripped
                and not stripped.startswith("//")
            ):
                try:
                    # 提取函数名
                    func_part = stripped.split("(")[0]
                    if " " in func_part:
                        func_name = func_part.split()[-1]
                        symbols.append(
                            {
                                "type": "function_definition",
                                "name": func_name,
                                "start_line": i + 1,
                                "end_line": self._scan_block_curly(lines, i),
                                "line": line,
                            }
                        )
                except IndexError:
                    continue

        return symbols

    def _parse_rust_simple(self, lines: list[str]) -> list[dict[str, Any]]:
        """简单解析Rust代码"""
        symbols = []

        for i, line in enumerate(lines):
            stripped = line.strip()

            # 函数定义
            if stripped.startswith("fn ") and "(" in stripped:
                try:
                    func_name = stripped.split("fn ")[1].split("(")[0].strip()
                    symbols.append(
                        {
                            "type": "function_definition",
                            "name": func_name,
                            "start_line": i + 1,
                            "end_line": self._scan_block_curly(lines, i),
                            "line": line,
                        }
                    )
                except IndexError:
                    continue

            # 结构体定义
            elif stripped.startswith("struct ") and "{" in stripped:
                try:
                    struct_name = (
                        stripped.split("struct ")[1].split(" ")[0].split("{")[0].strip()
                    )
                    symbols.append(
                        {
                            "type": "class_definition",  # 将struct视为class
                            "name": struct_name,
                            "start_line": i + 1,
                            "end_line": self._scan_block_curly(lines, i),
                            "line": line,
                        }
                    )
                except IndexError:
                    continue

        return symbols

    def _parse_arkts_simple(self, lines: list[str]) -> list[dict[str, Any]]:
        """简单解析ArkTS代码"""
        # 使用ArkTS解析器的简单解析功能
        content = "\n".join(lines)
        return arkts_parser.parse_simple(content)

    def _parse_generic_simple(self, lines: list[str]) -> list[dict[str, Any]]:
        """通用简单解析"""
        symbols = []

        for i, line in enumerate(lines):
            stripped = line.strip()

            # 寻找可能的函数定义模式
            if (
                "(" in stripped
                and ")" in stripped
                and not stripped.startswith("#")
                and not stripped.startswith("//")
            ):
                symbols.append(
                    {
                        "type": "function_definition",
                        "name": f"function_at_line_{i + 1}",
                        "start_line": i + 1,
                        "line": line,
                    }
                )

        return symbols

    def _extract_tree_sitter_symbols(self, tree, language: str) -> list[dict[str, Any]]:
        """从tree-sitter解析树中提取符号信息"""
        if not tree or not tree.root_node:
            return []

        symbols = []

        # 定义不同语言的符号类型映射
        symbol_type_maps = {
            "python": {
                "function_definition": "function_definition",
                "class_definition": "class_definition",
                "async_function_definition": "function_definition",
            },
            "javascript": {
                "function_declaration": "function_definition",
                "function_expression": "function_definition",
                "arrow_function": "function_definition",
                "class_declaration": "class_definition",
                "method_definition": "method_definition",
            },
            "typescript": {
                "function_declaration": "function_definition",
                "function_expression": "function_definition",
                "arrow_function": "function_definition",
                "class_declaration": "class_definition",
                "method_definition": "method_definition",
                "interface_declaration": "interface_definition",
            },
            "java": {
                "method_declaration": "method_definition",
                "class_declaration": "class_definition",
                "interface_declaration": "interface_definition",
            },
            "c": {
                "function_definition": "function_definition",
                "struct_specifier": "class_definition",
            },
            "cpp": {
                "function_definition": "function_definition",
                "class_specifier": "class_definition",
                "struct_specifier": "class_definition",
            },
            "rust": {
                "function_item": "function_definition",
                "struct_item": "class_definition",
                "impl_item": "class_definition",
            },
        }

        def traverse_node(node, depth=0, parent_type=None):
            """遍历AST节点"""
            symbol_map = symbol_type_maps.get(language, {})

            if node.type in symbol_map:
                symbol_name = self._extract_symbol_name_from_node(node)
                if symbol_name:
                    symbol_type = symbol_map[node.type]

                    # 对于在类中的方法，更准确地标记类型
                    if (
                        parent_type == "class_definition"
                        and symbol_type == "function_definition"
                    ):
                        symbol_type = "method_definition"

                    symbol_info = {
                        "type": symbol_type,
                        "name": symbol_name,
                        "start_line": node.start_point[0] + 1,
                        "end_line": node.end_point[0] + 1,
                        "start_byte": node.start_byte,
                        "end_byte": node.end_byte,
                        "node_type": node.type,
                        "parent_type": parent_type,
                    }
                    symbols.append(symbol_info)

                    # 递归遍历子节点，传递当前节点类型作为父类型
                    for child in node.children:
                        traverse_node(child, depth + 1, symbol_type)
            else:
                # 递归遍历子节点
                for child in node.children:
                    traverse_node(child, depth + 1, parent_type)

        traverse_node(tree.root_node)

        # 后处理：验证和修正符号边界
        symbols = self._validate_symbol_boundaries(symbols)

        return symbols

    def _validate_symbol_boundaries(
        self, symbols: list[dict[str, Any]]
    ) -> list[dict[str, Any]]:
        """验证和修正符号边界

        注意：上一条符号可能被丢弃（例如被调整后宽度为负），因此不能仅凭 i>0 就访问
        `validated_symbols[-1]`，需要先确认已存在有效的前一符号。
        """
        if not symbols:
            return symbols

        # 按开始行排序
        sorted_symbols = sorted(symbols, key=lambda x: x["start_line"])
        validated_symbols: list[dict[str, Any]] = []

        for i, symbol in enumerate(sorted_symbols):
            # 检查是否有重叠
            fixed_symbol = symbol.copy()

            # 检查与前一个已验证符号是否重叠
            if validated_symbols:
                prev_symbol = validated_symbols[-1]
                if symbol["start_line"] <= prev_symbol["end_line"]:
                    # 如果重叠，调整当前符号的开始行
                    fixed_symbol["start_line"] = prev_symbol["end_line"] + 1

            # 检查与下一个符号是否重叠
            if i + 1 < len(sorted_symbols):
                next_symbol = sorted_symbols[i + 1]
                if fixed_symbol["end_line"] >= next_symbol["start_line"]:
                    # 如果重叠，调整当前符号的结束行
                    fixed_symbol["end_line"] = next_symbol["start_line"] - 1

            # 确保符号至少有一行
            if fixed_symbol["end_line"] >= fixed_symbol["start_line"]:
                validated_symbols.append(fixed_symbol)

        return validated_symbols

    def _extract_symbol_name_from_node(self, node) -> str:
        """从AST节点中提取符号名称"""

        # 尝试找到标识符子节点
        def find_identifier(node, depth=0):
            """递归查找标识符节点"""
            if depth > 3:  # 限制递归深度
                return None

            if node.type in ["identifier", "property_identifier", "type_identifier"]:
                return node.text.decode("utf-8")

            # 在子节点中查找
            for child in node.children:
                result = find_identifier(child, depth + 1)
                if result:
                    return result
            return None

        name = find_identifier(node)
        if name:
            return name

        # 如果找不到标识符，返回默认名称
        return f"unnamed_{node.type}"

    def get_supported_languages(self) -> list[str]:
        """获取支持的编程语言列表"""
        if TREE_SITTER_AVAILABLE:
            return list(self.parsers.keys())
        else:
            # 即使没有tree-sitter，也支持简单解析
            return [
                "python",
                "javascript",
                "typescript",
                "java",
                "c",
                "cpp",
                "rust",
                "arkts",
            ]

    def extract_symbols(self, parse_result: Any) -> list[dict[str, Any]]:
        """从解析结果中提取符号信息"""
        if isinstance(parse_result, dict):
            if parse_result.get("type") == "simple_parse":
                return parse_result["symbols"]
            elif parse_result.get("type") == "arkts_parse":
                return parse_result["symbols"]
            elif parse_result.get("type") == "tree_sitter_parse":
                return parse_result["symbols"]

        # 如果是其他格式的解析结果，返回空列表
        return []
