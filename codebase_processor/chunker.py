"""
基于AST的递归代码分片器 V2
实现RAG数据预处理的AST + 递归chunk切分方案

核心特性：
1. 基于AST语法树 → 确保chunk边界与语言结构对齐
2. 递归拆分 → 对过大节点进行递归拆分
3. 上下文保留 → 携带父级必要上下文信息
4. 链式symbol_name格式支持
5. 智能chunk合并防止碎片化
6. 通用装饰器处理
"""

import re
from dataclasses import dataclass
from enum import Enum
from typing import Any, Dict, List, Optional, Tuple

from config import TREE_SITTER_CONFIG
from utils.helpers import clean_code_content, generate_chunk_id
from utils.interfaces import ICodeChunker
from utils.models import ChunkType, CodeChunk, FileInfo


class ASTNodeType(Enum):
    """AST节点类型枚举"""

    # 顶层声明
    MODULE = "module"
    CLASS = "class"
    INTERFACE = "interface"
    STRUCT = "struct"
    ENUM = "enum"
    NAMESPACE = "namespace"

    # 函数相关
    FUNCTION = "function"
    METHOD = "method"
    CONSTRUCTOR = "constructor"
    DESTRUCTOR = "destructor"

    # 控制结构
    IF_STATEMENT = "if_statement"
    FOR_LOOP = "for_loop"
    WHILE_LOOP = "while_loop"
    SWITCH_STATEMENT = "switch_statement"
    TRY_CATCH = "try_catch"

    # 代码块
    BLOCK = "block"
    EXPRESSION = "expression"

    # 声明
    VARIABLE_DECLARATION = "variable_declaration"
    CONSTANT_DECLARATION = "constant_declaration"
    TYPE_DECLARATION = "type_declaration"

    # 其他
    COMMENT = "comment"
    IMPORT = "import"
    OTHER = "other"


@dataclass
class ChunkContext:
    """Chunk上下文信息"""

    parent_classes: List[str] = None  # 父级类名栈
    parent_functions: List[str] = None  # 父级函数名栈
    namespace: str = ""  # 命名空间
    imports: List[str] = None  # 相关导入
    dependencies: List[str] = None  # 依赖关系

    def __post_init__(self):
        if self.parent_classes is None:
            self.parent_classes = []
        if self.parent_functions is None:
            self.parent_functions = []
        if self.imports is None:
            self.imports = []
        if self.dependencies is None:
            self.dependencies = []


class CodeChunker(ICodeChunker):
    """基于AST的递归代码分片器 V2

    实现RAG数据预处理的AST + 递归chunk切分方案：
    1. 基于AST语法树确保chunk边界与语言结构对齐
    2. 递归拆分过大节点，保持语义完整性
    3. 上下文保留机制，确保chunk携带父级必要信息
    4. 链式symbol_name格式支持
    5. 智能chunk合并防止碎片化
    """

    def __init__(self):
        # 基础配置
        self.chunk_min_lines = TREE_SITTER_CONFIG["chunk_min_lines"]
        self.chunk_max_lines = TREE_SITTER_CONFIG["chunk_max_lines"]
        self.function_max_lines = TREE_SITTER_CONFIG.get("function_max_lines", 200)
        self.class_max_lines = TREE_SITTER_CONFIG.get("class_max_lines", 500)
        self.context_lines = TREE_SITTER_CONFIG["context_lines"]

        # 递归拆分配置
        self.max_recursion_depth = 10
        self.min_chunk_size = 5  # 最小chunk行数
        self.preserve_structure = True  # 是否保持结构完整性

        # 上下文保留配置
        self.include_parent_context = True
        self.max_context_depth = 3  # 最大上下文深度
        self.include_imports = True
        self.include_dependencies = True

        # 智能合并配置
        self.enable_smart_merge = True
        self.max_merge_lines = 10  # 合并后的最大行数
        self.min_chunk_lines = 3  # 考虑合并的最小行数

    def extract_module_name(self, file_info: FileInfo) -> str:
        """从文件路径提取模块名 - 支持链式symbol_name格式"""
        try:
            # 使用相对路径作为模块名基础
            relative_path = file_info.relative_path

            # 移除文件扩展名
            module_path = relative_path.rsplit(".", 1)[0]

            # 将路径分隔符替换为点（Python模块风格）
            module_name = module_path.replace("/", ".").replace("\\", ".")

            # 移除开头的点
            module_name = module_name.lstrip(".")

            return module_name
        except Exception as e:
            print(f"⚠️ 提取模块名失败: {str(e)}")
            return ""

    def create_enhanced_parse_tree(self, file_info: FileInfo) -> Dict[str, Any]:
        """创建增强的解析树 - 整合test_chunker_updated的解析逻辑
        
        提供全面的符号识别能力：
        1. 支持多语言解析（Python, ArkTS, JS/TS/Java, 通用）
        2. 智能符号检测和范围计算
        3. 缺失内容补充，确保代码全覆盖
        4. 生成标准化的解析树格式
        """
        lines = file_info.content.split("\n")
        symbols = []
        
        print(f"🔍 开始增强解析 {file_info.language} 文件...")
        
        # 提取模块名用于链式symbol_name
        module_name = self.extract_module_name(file_info)
        print(f"📦 模块名: {module_name}")
        
        # 根据语言类型使用专门的解析策略
        if file_info.language == "python":
            symbols = self._parse_python_symbols(lines)
        elif file_info.language in ["javascript", "typescript", "java"]:
            symbols = self._parse_js_ts_java_symbols(lines)
        elif file_info.language == "arkts":
            symbols = self._parse_arkts_symbols(lines)
        else:
            # 对于其他语言，使用通用但全面的解析
            symbols = self._parse_generic_symbols(lines)
        
        print(f"🔍 识别到 {len(symbols)} 个符号")
        
        # 检查遗漏并生成补充分片
        symbols = self._generate_missing_symbols(lines, symbols)
        
        print(f"🔍 最终生成 {len(symbols)} 个符号（包含补充）")
        
        # 转换为chunker.py标准格式
        return self._convert_to_chunker_format(symbols, lines, module_name)

    def _parse_python_symbols(self, lines: list[str]) -> list[dict]:
        """Python符号解析 - 迁移自test_chunker_updated"""
        symbols = []
        i = 0
        
        # 正则表达式模式
        import_pattern = re.compile(r"^\s*(?:from\s+(\w+)\s+)?import\s+(.+)$")
        class_pattern = re.compile(r"^\s*class\s+(\w+)")
        def_pattern = re.compile(r"^\s*def\s+(\w+)")
        
        while i < len(lines):
            line = lines[i]
            stripped = line.strip()
            
            # 跳过空行和注释
            if (
                not stripped
                or stripped.startswith("#")
                or stripped.startswith('"""')
                or stripped.startswith("'''")
            ):
                i += 1
                continue
            
            # 检测import语句
            import_match = import_pattern.match(line)
            if import_match:
                symbols.append({
                    "type": "import",
                    "name": f"import_{i + 1}",
                    "start_line": i + 1,
                    "end_line": i + 1,
                })
                i += 1
                continue
            
            # 检测类定义
            class_match = class_pattern.match(line)
            if class_match:
                class_name = class_match.group(1)
                class_start = i
                class_end = self._find_python_block_end(lines, i)
                
                symbols.append({
                    "type": "class_definition",
                    "name": class_name,
                    "start_line": class_start + 1,
                    "end_line": class_end + 1,
                })
                
                # 解析类内部的方法
                class_methods = self._parse_class_methods_python(lines, class_start, class_end)
                symbols.extend(class_methods)
                
                i = class_end + 1
                continue
            
            # 检测独立函数定义
            def_match = def_pattern.match(line)
            if def_match:
                func_name = def_match.group(1)
                func_start = i
                func_end = self._find_python_block_end(lines, i)
                
                symbols.append({
                    "type": "function_definition",
                    "name": func_name,
                    "start_line": func_start + 1,
                    "end_line": func_end + 1,
                })
                i = func_end + 1
                continue
            
            i += 1
        
        return symbols

    def _parse_class_methods_python(self, lines: list[str], class_start: int, class_end: int) -> list[dict]:
        """解析Python类内部的方法"""
        methods = []
        
        for i in range(class_start + 1, class_end):
            line = lines[i]
            stripped = line.strip()
            
            # 跳过空行和注释
            if not stripped or stripped.startswith("#"):
                continue
            
            # 检测方法定义（在类内部）
            method_match = re.match(r"^\s*def\s+(\w+)", line)
            if method_match:
                method_name = method_match.group(1)
                method_start = i
                method_end = self._find_python_block_end(lines, i)
                
                methods.append({
                    "type": "method_definition",
                    "name": method_name,
                    "start_line": method_start + 1,
                    "end_line": method_end + 1,
                })
                
                i = method_end
        
        return methods

    def _parse_arkts_symbols(self, lines: list[str]) -> list[dict]:
        """ArkTS符号解析 - 迁移自test_chunker_updated"""
        symbols = []
        i = 0
        
        # ArkTS特定的正则表达式模式
        import_pattern = re.compile(r"^\s*import\s+(.+)$")
        component_pattern = re.compile(r"^\s*@Component\s*$")
        entry_pattern = re.compile(r"^\s*@Entry\s*$")
        struct_pattern = re.compile(r"^\s*struct\s+(\w+)")
        function_pattern = re.compile(r"^\s*function\s+(\w+)")
        class_pattern = re.compile(r"^\s*(?:export\s+)?class\s+(\w+)")  # 普通class
        
        # 通用装饰器模式 - 匹配任何@DecoratorName或@DecoratorName(params)
        decorator_pattern = re.compile(r"^\s*@(\w+)(?:\([^)]*\))?\s*$")
        
        current_struct = None
        in_struct = False
        
        while i < len(lines):
            line = lines[i]
            stripped = line.strip()
            
            # 跳过空行和注释
            if not stripped or stripped.startswith("//") or stripped.startswith("/*"):
                i += 1
                continue
            
            # 检测import语句
            import_match = import_pattern.match(stripped)
            if import_match:
                symbols.append({
                    "type": "import",
                    "name": f"import_{i + 1}",
                    "start_line": i + 1,
                    "end_line": i + 1,
                })
                i += 1
                continue
            
            # 通用装饰器检测和处理
            decorator_match = decorator_pattern.match(stripped)
            if decorator_match:
                decorator_name = decorator_match.group(1)
                decorator_start = i
                
                # 收集连续的所有装饰器（可能有多个装饰器堆叠）
                decorators_end = i
                j = i + 1
                while j < min(i + 10, len(lines)):
                    next_line = lines[j].strip()
                    if decorator_pattern.match(next_line):
                        decorators_end = j
                        j += 1
                    else:
                        break
                
                # 查找装饰器修饰的声明（函数、类、struct等）
                target_found = False
                k = decorators_end + 1
                while k < min(decorators_end + 15, len(lines)):  # 在装饰器后15行内搜索
                    target_line = lines[k]
                    target_stripped = target_line.strip()
                    
                    # 跳过空行和注释
                    if (not target_stripped or target_stripped.startswith("//") or 
                        target_stripped.startswith("/*") or target_stripped.startswith("*")):
                        k += 1
                        continue
                    
                    # 检测各种可能被装饰的声明
                    
                    # 1. struct 定义
                    struct_match = re.match(r"^\s*struct\s+(\w+)", target_stripped)
                    if struct_match:
                        struct_name = struct_match.group(1)
                        struct_start = k
                        struct_end = self._find_brace_block_end(lines, k)
                        
                        symbols.append({
                            "type": "class_definition",
                            "name": struct_name,
                            "start_line": decorator_start + 1,  # 从第一个装饰器开始
                            "end_line": struct_end + 1,
                        })
                        
                        # 解析组件内部的方法
                        component_methods = self._parse_component_methods_arkts(lines, decorator_start, struct_end)
                        symbols.extend(component_methods)
                        
                        current_struct = struct_name
                        in_struct = True
                        i = struct_end + 1
                        target_found = True
                        break
                    
                    # 2. function 定义
                    function_match = re.match(r"^\s*function\s+(\w+)", target_stripped)
                    if function_match and not in_struct:
                        func_name = function_match.group(1)
                        func_start = k
                        func_end = self._find_brace_block_end(lines, k)
                        
                        symbols.append({
                            "type": "function_definition",
                            "name": func_name,
                            "start_line": decorator_start + 1,  # 从第一个装饰器开始
                            "end_line": func_end + 1,
                        })
                        
                        i = func_end + 1
                        target_found = True
                        break
                    
                    # 3. class 定义
                    class_match = re.match(r"^\s*(?:export\s+)?class\s+(\w+)", target_stripped)
                    if class_match:
                        class_name = class_match.group(1)
                        class_start = k
                        class_end = self._find_brace_block_end(lines, k)
                        
                        symbols.append({
                            "type": "class_definition",
                            "name": class_name,
                            "start_line": decorator_start + 1,  # 从第一个装饰器开始
                            "end_line": class_end + 1,
                        })
                        
                        # 解析类内部的方法
                        class_methods = self._parse_class_methods_arkts(lines, class_start, class_end)
                        symbols.extend(class_methods)
                        
                        current_struct = class_name
                        in_struct = True
                        i = class_end + 1
                        target_found = True
                        break
                    
                    # 如果遇到非装饰器、非声明的行，停止搜索
                    if target_stripped and not target_stripped.startswith("@"):
                        if not (target_stripped.startswith("//") or target_stripped.startswith("/*") or 
                                target_stripped.startswith("*") or target_stripped == ""):
                            break
                    
                    k += 1
                
                if not target_found:
                    # 没有找到目标声明，将装饰器作为独立的decorator_section处理
                    symbols.append({
                        "type": "decorator_section",
                        "name": f"decorator_section_{decorator_start + 1}",
                        "start_line": decorator_start + 1,
                        "end_line": decorators_end + 1,
                    })
                    i = decorators_end + 1
                
                # Reset struct state
                current_struct = None
                in_struct = False
                continue
            
            # 检测独立函数
            function_match = function_pattern.match(stripped)
            if function_match and not in_struct:
                func_name = function_match.group(1)
                func_start = i
                func_end = self._find_brace_block_end(lines, i)
                
                symbols.append({
                    "type": "function_definition",
                    "name": func_name,
                    "start_line": func_start + 1,
                    "end_line": func_end + 1,
                })
                i = func_end + 1
                continue
            
            # 检测独立的方法（不在struct内部）
            method_match = re.match(r"^\s*(\w+)\s*\([^)]*\)\s*:\s*(\w+)", stripped)
            if method_match and not in_struct:
                method_name = method_match.group(1)
                method_start = i
                method_end = self._find_brace_block_end(lines, i)
                
                symbols.append({
                    "type": "function_definition",
                    "name": method_name,
                    "start_line": method_start + 1,
                    "end_line": method_end + 1,
                })
                i = method_end + 1
                continue
            
            i += 1
        
        return symbols

    def _parse_class_methods_arkts(self, lines: list[str], class_start: int, class_end: int) -> list[dict]:
        """解析ArkTS类内部的方法（普通class）"""
        methods = []
        
        for i in range(class_start + 1, class_end):
            line = lines[i]
            stripped = line.strip()
            
            # 跳过空行和注释
            if not stripped or stripped.startswith("//"):
                continue
            
            # 检测方法定义（在类内部）
            method_match = re.match(r"^\s*(\w+)\s*\([^)]*\)\s*:\s*(\w+)", stripped)
            if method_match:
                method_name = method_match.group(1)
                method_start = i
                method_end = self._find_brace_block_end(lines, i)
                
                methods.append({
                    "type": "method_definition",
                    "name": method_name,
                    "start_line": method_start + 1,
                    "end_line": method_end + 1,
                })
                
                i = method_end
                continue
        
        return methods

    def _parse_component_methods_arkts(self, lines: list[str], struct_start: int, struct_end: int) -> list[dict]:
        """解析ArkTS组件内部的方法"""
        methods = []
        
        for i in range(struct_start + 1, struct_end):
            line = lines[i]
            stripped = line.strip()
            
            # 跳过空行和注释
            if not stripped or stripped.startswith("//"):
                continue
            
            # 检测build方法
            if re.match(r"^\s*build\s*\(\s*\)\s*\{", stripped):
                method_start = i
                method_end = self._find_brace_block_end(lines, i)
                
                methods.append({
                    "type": "method_definition",
                    "name": "build",
                    "start_line": method_start + 1,
                    "end_line": method_end + 1,
                })
                continue
            
            # 检测其他方法
            method_match = re.match(r"^\s*(\w+)\s*\([^)]*\)\s*:\s*(\w+)", stripped)
            if method_match and method_match.group(1) != "build":
                method_name = method_match.group(1)
                method_start = i
                method_end = self._find_brace_block_end(lines, i)
                
                methods.append({
                    "type": "method_definition",
                    "name": method_name,
                    "start_line": method_start + 1,
                    "end_line": method_end + 1,
                })
                continue
        
        return methods

    def _parse_js_ts_java_symbols(self, lines: list[str]) -> list[dict]:
        """JavaScript/TypeScript/Java符号解析 - 迁移自test_chunker_updated"""
        symbols = []
        i = 0
        
        # 正则表达式模式
        import_pattern = re.compile(r"^\s*import\s+(.+)$")
        export_pattern = re.compile(r"^\s*export\s+(.+)$")
        class_pattern = re.compile(r"^\s*(?:export\s+)?(?:abstract\s+)?class\s+(\w+)")
        interface_pattern = re.compile(r"^\s*(?:export\s+)?interface\s+(\w+)")
        function_pattern = re.compile(
            r"^\s*(?:export\s+)?(?:async\s+)?(?:function\s+(\w+)|const\s+(\w+)\s*=\s*(?:async\s+)?\()"
        )
        method_pattern = re.compile(
            r"^\s*(?:private|public|protected|static)?\s*(?:async\s+)?(\w+)\s*\([^)]*\)\s*\{"
        )
        
        while i < len(lines):
            line = lines[i]
            stripped = line.strip()
            
            # 跳过空行和注释
            if not stripped or stripped.startswith("//") or stripped.startswith("/*"):
                i += 1
                continue
            
            # 检测import语句
            import_match = import_pattern.match(stripped)
            if import_match:
                symbols.append({
                    "type": "import",
                    "name": f"import_{i + 1}",
                    "start_line": i + 1,
                    "end_line": i + 1,
                })
                i += 1
                continue
            
            # 检测export语句
            export_match = export_pattern.match(stripped)
            if export_match:
                symbols.append({
                    "type": "export",
                    "name": f"export_{i + 1}",
                    "start_line": i + 1,
                    "end_line": i + 1,
                })
                i += 1
                continue
            
            # 检测类定义
            class_match = class_pattern.match(stripped)
            if class_match:
                class_name = class_match.group(1)
                class_start = i
                class_end = self._find_brace_block_end(lines, i)
                
                symbols.append({
                    "type": "class_definition",
                    "name": class_name,
                    "start_line": class_start + 1,
                    "end_line": class_end + 1,
                })
                
                # 解析类内部的方法
                class_methods = self._parse_class_methods_js(lines, class_start, class_end)
                symbols.extend(class_methods)
                
                i = class_end + 1
                continue
            
            # 检测接口定义
            interface_match = interface_pattern.match(stripped)
            if interface_match:
                interface_name = interface_match.group(1)
                interface_start = i
                interface_end = self._find_brace_block_end(lines, i)
                
                symbols.append({
                    "type": "interface_definition",
                    "name": interface_name,
                    "start_line": interface_start + 1,
                    "end_line": interface_end + 1,
                })
                
                # 解析接口内部的方法
                interface_methods = self._parse_class_methods_js(lines, interface_start, interface_end)
                symbols.extend(interface_methods)
                
                i = interface_end + 1
                continue
            
            # 检测独立函数定义
            function_match = function_pattern.match(stripped)
            if function_match:
                func_name = function_match.group(1) or function_match.group(2)
                func_start = i
                func_end = self._find_brace_block_end(lines, i)
                
                symbols.append({
                    "type": "function_definition",
                    "name": func_name,
                    "start_line": func_start + 1,
                    "end_line": func_end + 1,
                })
                i = func_end + 1
                continue
            
            # 检测独立的方法（不在类内部）
            method_match = method_pattern.match(stripped)
            if method_match:
                method_name = method_match.group(1)
                method_start = i
                method_end = self._find_brace_block_end(lines, i)
                
                symbols.append({
                    "type": "function_definition",
                    "name": method_name,
                    "start_line": method_start + 1,
                    "end_line": method_end + 1,
                })
                i = method_end + 1
                continue
            
            i += 1
        
        return symbols

    def _parse_class_methods_js(self, lines: list[str], class_start: int, class_end: int) -> list[dict]:
        """解析JS/TS/Java类内部的方法"""
        methods = []
        
        for i in range(class_start + 1, class_end):
            line = lines[i]
            stripped = line.strip()
            
            # 跳过空行和注释
            if not stripped or stripped.startswith("//"):
                continue
            
            # 检测方法定义（在类内部）
            method_match = re.match(
                r"^\s*(?:private|public|protected|static)?\s*(?:async\s+)?(\w+)\s*\([^)]*\)\s*\{",
                stripped,
            )
            if method_match:
                method_name = method_match.group(1)
                method_start = i
                method_end = self._find_brace_block_end(lines, i)
                
                methods.append({
                    "type": "method_definition",
                    "name": method_name,
                    "start_line": method_start + 1,
                    "end_line": method_end + 1,
                })
                
                i = method_end
                continue
        
        return methods

    def _parse_generic_symbols(self, lines: list[str]) -> list[dict]:
        """通用符号解析 - 支持多种语言"""
        symbols = []
        
        # 更全面的关键词模式
        patterns = [
            (r"^\s*import\s+(.+)", "import"),
            (r"^\s*export\s+(.+)", "export"),
            (r"^\s*(?:export\s+)?(?:abstract\s+)?class\s+(\w+)", "class_definition"),
            (r"^\s*(?:export\s+)?interface\s+(\w+)", "interface_definition"),
            (r"^\s*(?:export\s+)?(?:async\s+)?function\s+(\w+)", "function_definition"),
            (
                r"^\s*(?:export\s+)?(?:async\s+)?(\w+)\s*\([^)]*\)\s*=\s*",
                "function_definition",
            ),  # 箭头函数
            (
                r"^\s*(?:private|public|protected|static)?\s*(?:async\s+)?(\w+)\s*\([^)]*\)\s*\{",
                "method_definition",
            ),
            (r"^\s*struct\s+(\w+)", "class_definition"),  # Rust风格的struct
            (r"^\s*fn\s+(\w+)", "function_definition"),  # Rust风格的函数
            (r"^\s*const\s+(\w+)\s*:", "constant_declaration"),
            (r"^\s*let\s+(\w+)\s*:", "variable_declaration"),
        ]
        
        compiled_patterns = [
            (re.compile(pattern), symbol_type) for pattern, symbol_type in patterns
        ]
        
        for i, line in enumerate(lines):
            stripped = line.strip()
            
            # 跳过空行和注释
            if not stripped or stripped.startswith(("#", "//", "/*", "*", "--", "///")):
                continue
            
            # 尝试所有模式
            for pattern, symbol_type in compiled_patterns:
                match = pattern.match(stripped)
                if match:
                    name = match.group(1)
                    start_line = i
                    
                    # 根据符号类型找到结束位置
                    if "class" in symbol_type or "interface" in symbol_type:
                        end_line = self._find_brace_block_end(lines, i)
                    elif "function" in symbol_type or "method" in symbol_type:
                        end_line = self._find_brace_block_end(lines, i)
                    elif "import" in symbol_type or "export" in symbol_type:
                        end_line = i  # import/export通常单行
                    else:
                        end_line = i  # 变量声明等通常单行
                    
                    symbols.append({
                        "type": symbol_type,
                        "name": name,
                        "start_line": start_line + 1,
                        "end_line": end_line + 1,
                    })
                    break
        
        return symbols

    def _find_python_block_end(self, lines: list[str], start_idx: int) -> int:
        """Python代码块结束位置查找"""
        if start_idx >= len(lines):
            return start_idx
        
        start_line = lines[start_idx]
        start_indent = len(start_line) - len(start_line.lstrip())
        
        # 查找下一个相同或更少缩进级别的非空行
        for i in range(start_idx + 1, len(lines)):
            line = lines[i]
            
            # 跳过空行，但继续查找
            if not line.strip():
                continue
            
            # 跳过注释，但继续查找
            if line.strip().startswith("#"):
                continue
            
            current_indent = len(line) - len(line.lstrip())
            
            # 如果缩进级别小于等于起始行，说明代码块结束
            if current_indent <= start_indent:
                return i - 1
        
        return len(lines) - 1

    def _find_brace_block_end(self, lines: list[str], start_idx: int) -> int:
        """大括号代码块结束位置查找"""
        if start_idx >= len(lines):
            return start_idx
        
        brace_count = 0
        in_string = False
        string_char = None
        
        for i in range(start_idx, len(lines)):
            line = lines[i]
            
            # 跳过注释（更智能的注释处理）
            if "//" in line:
                # 找到不是字符串中的//
                in_str = False
                str_char = None
                comment_pos = -1
                
                for j, char in enumerate(line):
                    if not in_str and char in ['"', "'"]:
                        in_str = True
                        str_char = char
                    elif (in_str and char == str_char and (j == 0 or line[j - 1] != "\\")):
                        in_str = False
                        str_char = None
                    elif (not in_str and char == "/" and j + 1 < len(line) and line[j + 1] == "/"):
                        comment_pos = j
                        break
                
                if comment_pos != -1:
                    line = line[:comment_pos]
            
            j = 0
            while j < len(line):
                char = line[j]
                
                # 处理字符串
                if not in_string and char in ['"', "'"]:
                    in_string = True
                    string_char = char
                elif in_string and char == string_char:
                    # 检查是否是转义字符
                    if j > 0 and line[j - 1] != "\\":
                        in_string = False
                        string_char = None
                
                # 统计大括号（不在字符串中）
                if not in_string:
                    if char == "{":
                        brace_count += 1
                    elif char == "}":
                        brace_count -= 1
                        if brace_count == 0:
                            return i
                
                j += 1
        
        return len(lines) - 1

    def _generate_missing_symbols(self, lines: list[str], existing_symbols: list[dict]) -> list[dict]:
        """生成遗漏的symbols，确保覆盖所有内容"""
        if not existing_symbols:
            # 如果没有识别到任何符号，生成文件级别的symbol
            return [{
                "type": "other",
                "name": "file_content",
                "start_line": 1,
                "end_line": len(lines),
            }]
        
        # 按开始行排序
        existing_symbols = sorted(existing_symbols, key=lambda x: x["start_line"])
        
        # 找出未覆盖的区域
        covered_ranges = [(s["start_line"], s["end_line"]) for s in existing_symbols]
        uncovered_ranges = []
        
        # 检查文件开头到第一个符号
        if covered_ranges[0][0] > 1:
            uncovered_ranges.append((1, covered_ranges[0][0] - 1))
        
        # 检查符号之间的间隙
        for i in range(len(covered_ranges) - 1):
            current_end = covered_ranges[i][1]
            next_start = covered_ranges[i + 1][0]
            if next_start > current_end + 1:
                uncovered_ranges.append((current_end + 1, next_start - 1))
        
        # 检查最后一个符号到文件末尾
        if covered_ranges[-1][1] < len(lines):
            uncovered_ranges.append((covered_ranges[-1][1] + 1, len(lines)))
        
        # 为每个未覆盖区域生成symbol
        missing_symbols = []
        for start_line, end_line in uncovered_ranges:
            if start_line <= end_line:  # 确保范围有效
                # 分析未覆盖区域的内容类型
                content_type = self._analyze_uncovered_content(lines, start_line - 1, end_line - 1)
                
                missing_symbols.append({
                    "type": content_type,
                    "name": f"{content_type}_{start_line}",
                    "start_line": start_line,
                    "end_line": end_line,
                })
        
        # 合并所有符号
        all_symbols = existing_symbols + missing_symbols
        
        # 按开始行排序
        return sorted(all_symbols, key=lambda x: x["start_line"])

    def _analyze_uncovered_content(self, lines: list[str], start_idx: int, end_idx: int) -> str:
        """分析未覆盖区域的内容类型"""
        content = "\n".join(lines[start_idx : end_idx + 1])
        stripped_content = content.strip()
        
        # 检查是否只有装饰器（以@开头）
        if re.search(r"^@\w+", stripped_content) and not re.search(
            r"(?:function|class|struct|interface)\s+\w+", content
        ):
            return "decorator_section"
        
        # 检查是否只有import语句
        if re.search(r"^\s*import\s+", stripped_content) and not re.search(
            r"@\w+", content
        ):
            return "import_section"
        
        # 检查是否包含import和装饰器（应该分开处理）
        has_import = re.search(r"\bimport\s+", content)
        has_decorator = re.search(r"@\w+", content)
        
        if has_import and not has_decorator:
            return "import_section"
        elif has_decorator and not has_import:
            return "decorator_section"
        elif has_import and has_decorator:
            # 同时有import和装饰器，应该标记为需要进一步分割
            return "mixed_section"
        elif re.search(r"//.*", content) or re.search(r"/\*.*\*/", content, re.DOTALL):
            return "comment_section"
        elif re.search(r"function\s+\w+|def\s+\w+|fn\s+\w+", content):
            return "function_definition"
        elif re.search(r"class\s+\w+|struct\s+\w+|interface\s+\w+", content):
            return "class_definition"
        else:
            return "other"

    def _convert_to_chunker_format(self, symbols: list[dict], lines: list[str], module_name: str) -> Dict[str, Any]:
        """转换为chunker.py标准格式"""
        # 转换为清洁的解析树，避免循环引用
        clean_symbols = []
        for symbol in symbols:
            clean_symbol = {
                "type": symbol["type"],
                "name": symbol["name"],
                "start_line": symbol["start_line"],
                "end_line": symbol["end_line"],
            }
            clean_symbols.append(clean_symbol)
        
        return {"type": "enhanced_parse", "symbols": clean_symbols, "lines": lines, "module_name": module_name}

    def chunk_by_symbols(
        self, parse_tree: object, file_info: FileInfo
    ) -> List[CodeChunk]:
        """按符号级粒度分片代码"""
        if not isinstance(parse_tree, dict):
            return self._chunk_by_lines(file_info)

        parse_type = parse_tree.get("type")
        if parse_type not in ["simple_parse", "tree_sitter_parse", "arkts_parse", "enhanced_parse"]:
            return self._chunk_by_lines(file_info)

        chunks = self._chunk_from_ast_parse(parse_tree, file_info)

        # 应用智能合并（如果启用）
        if self.enable_smart_merge:
            chunks = self._merge_small_chunks(chunks, file_info)

        return chunks

    def _chunk_from_ast_parse(
        self, parse_result: Dict[str, Any], file_info: FileInfo
    ) -> List[CodeChunk]:
        """从AST解析结果创建分片"""
        symbols = parse_result.get("symbols", [])
        lines = parse_result.get("lines", [])

        if not symbols or not lines:
            return self._chunk_by_lines(file_info)

        # 构建符号层级关系
        symbol_hierarchy = self._build_symbol_hierarchy(symbols)

        # 递归分片
        chunks = self._recursive_chunk_symbols(
            symbol_hierarchy, lines, file_info, ChunkContext()
        )

        # 后处理：去重、排序、合并
        return self._post_process_chunks(chunks)

    def _build_symbol_hierarchy(self, symbols: List[Dict]) -> List[Dict]:
        """构建符号层级关系"""
        # 按开始行排序
        sorted_symbols = sorted(symbols, key=lambda x: x["start_line"])

        # 建立父子关系
        hierarchy = []
        for symbol in sorted_symbols:
            symbol["children"] = []
            symbol["parent"] = None

            # 找到父符号
            for parent in reversed(hierarchy):
                if (
                    symbol["start_line"] >= parent["start_line"]
                    and symbol["end_line"] <= parent["end_line"]
                    and self._can_contain_children(parent["type"])
                ):
                    symbol["parent"] = parent
                    parent["children"].append(symbol)
                    break

            hierarchy.append(symbol)

        # 返回顶级符号
        return [s for s in hierarchy if s["parent"] is None]

    def _can_contain_children(self, symbol_type: str) -> bool:
        """判断符号类型是否可以包含子节点"""
        container_types = {
            "class_definition",
            "interface_definition",
            "struct_declaration",
            "enum_declaration",
            "namespace_declaration",
            "function_definition",
            "method_definition",
            "module",
        }
        return symbol_type in container_types

    def _recursive_chunk_symbols(
        self,
        symbols: List[Dict],
        lines: List[str],
        file_info: FileInfo,
        context: ChunkContext,
        depth: int = 0,
    ) -> List[CodeChunk]:
        """递归分片符号"""
        if depth > self.max_recursion_depth:
            return []

        chunks = []

        for symbol in symbols:
            # 更新上下文
            new_context = self._update_context_from_symbol(context, symbol)

            # 检查符号是否需要拆分
            should_split = self._should_split_symbol(symbol)

            if should_split:
                # 递归处理子符号
                child_chunks = self._recursive_chunk_symbols(
                    symbol["children"], lines, file_info, new_context, depth + 1
                )
                chunks.extend(child_chunks)

                # 处理符号本身的内容
                symbol_chunks = self._process_symbol_content(
                    symbol, lines, file_info, new_context
                )
                chunks.extend(symbol_chunks)
            else:
                # 即使不需要拆分，也要处理子符号
                if symbol.get("children"):
                    child_chunks = self._recursive_chunk_symbols(
                        symbol["children"], lines, file_info, new_context, depth + 1
                    )
                    chunks.extend(child_chunks)

                # 创建单个chunk
                chunk = self._create_chunk_from_symbol(
                    symbol, lines, file_info, new_context
                )
                if chunk:
                    chunks.append(chunk)

        return chunks

    def _update_context_from_symbol(
        self, context: ChunkContext, symbol: Dict
    ) -> ChunkContext:
        """从符号更新上下文信息"""
        new_context = ChunkContext(
            parent_classes=context.parent_classes.copy(),
            parent_functions=context.parent_functions.copy(),
            namespace=context.namespace,
            imports=context.imports.copy(),
            dependencies=context.dependencies.copy(),
        )

        # 根据符号类型更新上下文
        symbol_type = symbol.get("type", "")
        symbol_name = symbol.get("name", "")

        if symbol_type in [
            "class_definition",
            "interface_definition",
            "struct_declaration",
        ]:
            new_context.parent_classes.append(symbol_name)
        elif symbol_type in ["function_definition", "method_definition"]:
            new_context.parent_functions.append(symbol_name)
        elif symbol_type == "namespace_declaration":
            new_context.namespace = symbol_name

        return new_context

    def _should_split_symbol(self, symbol: Dict) -> bool:
        """判断符号是否需要拆分"""
        symbol_type = symbol.get("type", "")
        start_line = symbol.get("start_line", 1)
        end_line = symbol.get("end_line", start_line)
        node_lines = end_line - start_line + 1

        # 根据符号类型确定拆分阈值
        if symbol_type in [
            "class_definition",
            "interface_definition",
            "struct_declaration",
        ]:
            return node_lines > self.class_max_lines
        elif symbol_type in ["function_definition", "method_definition"]:
            return node_lines > self.function_max_lines
        else:
            return node_lines > self.chunk_max_lines

    def _process_symbol_content(
        self, symbol: Dict, lines: List[str], file_info: FileInfo, context: ChunkContext
    ) -> List[CodeChunk]:
        """处理符号内容"""
        chunks = []

        # 对于需要拆分的符号，按固定大小分割
        start_line = symbol.get("start_line", 1)
        end_line = symbol.get("end_line", start_line)
        symbol_lines = lines[start_line - 1 : end_line]

        # 按固定大小分割
        current_start = 0
        part_index = 1

        while current_start < len(symbol_lines):
            current_end = min(current_start + self.chunk_max_lines, len(symbol_lines))

            # 尝试在更好的位置断开
            if current_end < len(symbol_lines):
                for i in range(
                    current_end - 1,
                    max(current_start + self.min_chunk_size, current_end - 20),
                    -1,
                ):
                    line = symbol_lines[i].strip()
                    if (
                        not line
                        or line.startswith("//")
                        or line == "}"
                        or line.endswith("};")
                    ):
                        current_end = i + 1
                        break

            chunk_lines = symbol_lines[current_start:current_end]
            chunk_name = f"{symbol['name']}_part_{part_index}"

            if self._has_meaningful_content(chunk_lines):
                chunk = self._create_chunk_with_context(
                    chunk_lines,
                    start_line + current_start,
                    start_line + current_end - 1,
                    chunk_name,
                    symbol["type"],
                    file_info,
                    context,
                )
                if chunk:
                    chunks.append(chunk)
                    part_index += 1

            current_start = current_end
        return chunks

    def _find_remaining_ranges(
        self, symbol: Dict, covered_lines: set
    ) -> List[Tuple[int, int]]:
        """找到未覆盖的行范围"""
        ranges = []
        current_start = None
        start_line = symbol.get("start_line", 1)
        end_line = symbol.get("end_line", start_line)

        for line_num in range(start_line, end_line + 1):
            if line_num not in covered_lines:
                if current_start is None:
                    current_start = line_num
            else:
                if current_start is not None:
                    ranges.append((current_start, line_num - 1))
                    current_start = None

        # 处理最后一个范围
        if current_start is not None:
            ranges.append((current_start, end_line))

        return ranges

    def _create_chunk_from_symbol(
        self, symbol: Dict, lines: List[str], file_info: FileInfo, context: ChunkContext
    ) -> Optional[CodeChunk]:
        """从符号创建chunk"""
        start_line = symbol.get("start_line", 1)
        end_line = symbol.get("end_line", start_line)
        symbol_lines = lines[start_line - 1 : end_line]

        if not self._has_meaningful_content(symbol_lines):
            return None

        return self._create_chunk_with_context(
            symbol_lines,
            start_line,
            end_line,
            symbol.get("name", "unknown"),
            symbol.get("type", "other"),
            file_info,
            context,
        )

    def _create_chunk_with_context(
        self,
        lines: List[str],
        start_line: int,
        end_line: int,
        symbol_name: str,
        symbol_type: str,
        file_info: FileInfo,
        context: ChunkContext,
    ) -> Optional[CodeChunk]:
        """创建带上下文的chunk"""
        content = "\n".join(lines)
        cleaned_content = clean_code_content(content)

        if not cleaned_content.strip():
            return None

        # 生成链式symbol_name格式
        chain_symbol_name = self._build_chain_symbol_name(
            symbol_name, context, file_info
        )

        # 添加上下文信息
        if self.include_parent_context:
            context_info = self._build_context_info(context)
            if context_info:
                cleaned_content = f"{context_info}\n\n{cleaned_content}"

        chunk_id = generate_chunk_id(
            file_info.relative_path, start_line, end_line, chain_symbol_name
        )
        chunk_type = self._map_symbol_type_to_chunk_type(symbol_type)

        return CodeChunk(
            id=chunk_id,
            file_path=file_info.file_path,
            relative_path=file_info.relative_path,
            start_line=start_line,
            end_line=end_line,
            content=cleaned_content,
            chunk_type=chunk_type,
            symbol_name=chain_symbol_name,
            language=file_info.language,
        )

    def _build_context_info(self, context: ChunkContext) -> str:
        """构建上下文信息字符串"""
        context_parts = []

        # 添加命名空间
        if context.namespace:
            context_parts.append(f"namespace {context.namespace}")

        # 添加父级类
        if context.parent_classes:
            class_hierarchy = " -> ".join(
                context.parent_classes[-self.max_context_depth :]
            )
            context_parts.append(f"class hierarchy: {class_hierarchy}")

        # 添加父级函数
        if context.parent_functions:
            function_stack = " -> ".join(
                context.parent_functions[-self.max_context_depth :]
            )
            context_parts.append(f"function stack: {function_stack}")

        # 添加导入
        if context.imports and self.include_imports:
            imports_str = ", ".join(context.imports[:5])  # 限制导入数量
            context_parts.append(f"imports: {imports_str}")

        return "// Context: " + " | ".join(context_parts) if context_parts else ""

    def _build_chain_symbol_name(
        self, symbol_name: str, context: ChunkContext, file_info: FileInfo
    ) -> str:
        """构建链式symbol_name格式：module_name.class_name.func_name"""
        # 获取模块名
        module_name = self.extract_module_name(file_info)

        # 构建链式名称组件
        chain_components = []

        # 添加模块名
        if module_name:
            chain_components.append(module_name)

        # 添加命名空间
        if context.namespace:
            chain_components.append(context.namespace)

        # 添加父级类
        for class_name in context.parent_classes[-self.max_context_depth :]:
            if class_name and class_name not in chain_components:
                chain_components.append(class_name)

        # 添加父级函数（对于嵌套函数）
        for func_name in context.parent_functions[-self.max_context_depth :]:
            if (
                func_name
                and func_name != symbol_name
                and func_name not in chain_components
            ):
                chain_components.append(func_name)

        # 添加当前符号名
        if symbol_name and symbol_name not in chain_components:
            chain_components.append(symbol_name)

        # 如果没有组件，返回原始符号名
        if not chain_components:
            return symbol_name

        # 如果只有模块名和当前符号名，直接使用模块名.符号名格式
        if len(chain_components) == 2 and module_name and symbol_name:
            return f"{module_name}.{symbol_name}"

        # 构建完整的链式名称
        return ".".join(chain_components)

    def _merge_small_chunks(
        self, chunks: List[CodeChunk], file_info: FileInfo
    ) -> List[CodeChunk]:
        """合并小chunk，避免过度碎片化

        合并规则：
        1. 小于3行的chunk考虑合并
        2. 相同类型的相邻chunk可以合并
        3. import语句可以合并在一起
        4. 保持语义完整性
        """
        if not chunks:
            return chunks

        # 按起始行排序
        sorted_chunks = sorted(chunks, key=lambda x: x.start_line)
        merged_chunks = []

        i = 0
        while i < len(sorted_chunks):
            current_chunk = sorted_chunks[i]
            current_lines = current_chunk.end_line - current_chunk.start_line + 1

            # 特殊处理：import语句合并，但要避免与装饰器合并
            if current_chunk.chunk_type == "import" or (
                current_chunk.chunk_type == "other"
                and "import" in current_chunk.symbol_name
            ):
                # 检查是否可以将连续的import合并
                import_candidates = [current_chunk]
                j = i + 1

                while j < len(sorted_chunks):
                    next_chunk = sorted_chunks[j]

                    # 如果遇到装饰器或类定义，停止合并
                    if next_chunk.chunk_type in [
                        "decorator_section",
                        "class_definition",
                    ] or (
                        next_chunk.chunk_type == "other"
                        and (
                            "@" in next_chunk.content or "struct" in next_chunk.content
                        )
                        and "import" not in next_chunk.symbol_name
                    ):  # 确保不是import
                        break

                    # 如果是import语句，添加到合并候选
                    if next_chunk.chunk_type == "import" or (
                        next_chunk.chunk_type == "other"
                        and "import" in next_chunk.symbol_name
                    ):
                        # 检查是否相邻
                        if next_chunk.start_line == import_candidates[-1].end_line + 1:
                            import_candidates.append(next_chunk)
                            j += 1
                        else:
                            break
                    else:
                        break

                # 如果找到多个import，合并它们
                if len(import_candidates) > 1:
                    merged_chunk = self._create_merged_chunk(
                        import_candidates, file_info
                    )
                    merged_chunks.append(merged_chunk)
                    i = j
                    continue

            # 对于其他小chunk，尝试合并
            merge_candidates = [current_chunk]
            j = i + 1

            while j < len(sorted_chunks):
                next_chunk = sorted_chunks[j]
                next_lines = next_chunk.end_line - next_chunk.start_line + 1

                # 特殊规则：不同类型的chunk不应该混合合并
                # 特别是装饰器不应该与代码元素合并
                if (
                    current_chunk.chunk_type == "import"
                    and next_chunk.chunk_type not in ["import", "other"]
                ):
                    break

                if (
                    current_chunk.chunk_type == "decorator_section"
                    and next_chunk.chunk_type not in ["decorator_section", "other"]
                ):
                    break

                # 特殊规则：装饰器不应该与import合并
                if (
                    merge_candidates[0].chunk_type in ["import", "other"]
                    and "import" in merge_candidates[0].symbol_name
                    and (
                        next_chunk.chunk_type == "decorator_section"
                        or (
                            next_chunk.chunk_type == "other"
                            and "@" in next_chunk.content
                        )
                    )
                ):
                    break

                # 检查是否可以合并
                if (
                    next_chunk.start_line == merge_candidates[-1].end_line + 1  # 相邻
                    and next_lines < 5  # 下一个chunk也很小
                    and self._can_merge_chunks(merge_candidates[-1], next_chunk)
                ):  # 类型兼容
                    merge_candidates.append(next_chunk)
                    j += 1
                else:
                    break

            # 如果合并后的总大小合理，进行合并
            total_lines = sum(
                candidate.end_line - candidate.start_line + 1
                for candidate in merge_candidates
            )
            if (
                total_lines <= self.max_merge_lines and len(merge_candidates) > 1
            ):  # 合并后不超过最大行数且有多个chunk
                merged_chunk = self._create_merged_chunk(merge_candidates, file_info)
                merged_chunks.append(merged_chunk)
            else:
                # 不合并，保留原始chunks
                merged_chunks.extend(merge_candidates)

            i = j

        return merged_chunks

    def _can_merge_chunks(self, chunk1: CodeChunk, chunk2: CodeChunk) -> bool:
        """判断两个chunk是否可以合并"""
        # import语句可以合并（但不包括装饰器）
        if chunk1.chunk_type == "import" and chunk2.chunk_type == "import":
            return True

        if (
            chunk1.chunk_type == "other"
            and chunk2.chunk_type == "other"
            and "import" in chunk1.symbol_name
            and "import" in chunk2.symbol_name
            and "decorator" not in chunk1.symbol_name
            and "decorator" not in chunk2.symbol_name
        ):
            return True

        # 相同类型的chunk可以合并
        if chunk1.chunk_type == chunk2.chunk_type:
            return True

        # 装饰器section不应该与import合并
        if (
            chunk1.chunk_type == "import_section"
            and chunk2.chunk_type in ["decorator_section", "other"]
            and ("decorator" in chunk2.symbol_name or "@" in chunk2.content)
        ):
            return False

        # 不同类型但都是小chunk，且语义相关（排除装饰器与import的合并）
        if (
            chunk1.chunk_type in ["other"]
            and chunk2.chunk_type in ["other"]
            and "decorator" not in chunk1.symbol_name
            and "decorator" not in chunk2.symbol_name
            and "import" not in chunk1.symbol_name
            and "import" not in chunk2.symbol_name
        ):
            return True

        return False

    def _create_merged_chunk(
        self, chunks: List[CodeChunk], file_info: FileInfo
    ) -> CodeChunk:
        """创建合并后的chunk"""
        start_line = chunks[0].start_line
        end_line = chunks[-1].end_line

        # 生成新的symbol name和type
        if len(chunks) == 1:
            symbol_name = chunks[0].symbol_name
            chunk_type = chunks[0].chunk_type
        else:
            # 根据主要类型决定
            types = [c.chunk_type for c in chunks]
            if "import" in " ".join(c.symbol_name for c in chunks):
                symbol_name = "import_section"
                chunk_type = "import_section"
            elif all(t == "other" for t in types):
                symbol_name = "merged_section"
                chunk_type = "other"
            else:
                symbol_name = "merged_section"
                chunk_type = "other"

        # 提取合并后的内容
        original_lines = file_info.content.split("\n")
        merged_content = "\n".join(original_lines[start_line - 1 : end_line])

        # 清理内容
        cleaned_content = clean_code_content(merged_content)

        # 创建新的chunk
        chunk_id = generate_chunk_id(
            file_info.relative_path, start_line, end_line, symbol_name
        )
        return CodeChunk(
            id=chunk_id,
            symbol_name=symbol_name,
            chunk_type=chunk_type,
            start_line=start_line,
            end_line=end_line,
            content=cleaned_content,
            file_path=file_info.file_path,
            relative_path=file_info.relative_path,
            language=file_info.language,
        )

    def _map_symbol_type_to_chunk_type(self, symbol_type: str) -> str:
        """将符号类型映射到chunk类型"""
        type_mapping = {
            "class_definition": ChunkType.CLASS.value,
            "interface_definition": ChunkType.INTERFACE.value,
            "struct_declaration": ChunkType.CLASS.value,
            "function_definition": ChunkType.FUNCTION.value,
            "method_definition": ChunkType.METHOD.value,
            "constructor_definition": ChunkType.METHOD.value,
            "decorator_section": "decorator_section",
            "import_section": "import_section",
            "import": "import",
            "export": "export",
            "other": ChunkType.OTHER.value,
        }
        return type_mapping.get(symbol_type, ChunkType.OTHER.value)

    def _detect_decorator_section(
        self, lines: List[str], start_line: int
    ) -> Optional[Tuple[str, int]]:
        """检测装饰器部分 - 通用装饰器处理"""
        # 装饰器模式：@DecoratorName 或 @DecoratorName(params)
        decorator_pattern = r"^\s*@(\w+)(?:\([^)]*\))?\s*"

        original_lines = lines[start_line - 1 :] if start_line > 0 else lines

        decorator_lines = []
        current_line_idx = 0

        # 收集连续的装饰器
        while current_line_idx < len(original_lines):
            line = original_lines[current_line_idx]

            # 检查是否是装饰器
            if re.match(decorator_pattern, line):
                decorator_lines.append(line)
                current_line_idx += 1
            # 检查是否是空行或注释（可以包含在装饰器部分中）
            elif (
                line.strip() == ""
                or line.strip().startswith("//")
                or line.strip().startswith("/*")
            ):
                decorator_lines.append(line)
                current_line_idx += 1
            else:
                break

        # 如果找到装饰器，返回装饰器部分和结束行号
        if decorator_lines:
            end_line = start_line + len(decorator_lines) - 1
            content = "\n".join(decorator_lines)
            return (content, end_line)

        return None

    def _detect_import_section(
        self, lines: List[str], start_line: int
    ) -> Optional[Tuple[str, int]]:
        """检测import部分"""
        # import模式：import xxx 或 from xxx import xxx
        import_pattern = r"^\s*(?:import|from)\s+"

        original_lines = lines[start_line - 1 :] if start_line > 0 else lines

        import_lines = []
        current_line_idx = 0

        # 收集连续的import语句
        while current_line_idx < len(original_lines):
            line = original_lines[current_line_idx]

            # 检查是否是import语句
            if re.match(import_pattern, line):
                import_lines.append(line)
                current_line_idx += 1
            # 检查是否是空行（可以分隔import语句）
            elif line.strip() == "":
                # 如果已经收集了import语句，空行可以作为分隔符
                if import_lines:
                    # 检查下一行是否还是import语句
                    if current_line_idx + 1 < len(original_lines) and re.match(
                        import_pattern, original_lines[current_line_idx + 1]
                    ):
                        import_lines.append(line)
                        current_line_idx += 1
                    else:
                        break
                else:
                    current_line_idx += 1
            else:
                break

        # 如果找到import语句，返回import部分和结束行号
        if import_lines:
            end_line = start_line + len(import_lines) - 1
            content = "\n".join(import_lines)
            return (content, end_line)

        return None

    def _has_meaningful_content(self, lines: List[str]) -> bool:
        """检查行列表是否包含有意义的内容"""
        for line in lines:
            stripped = line.strip()
            if (
                stripped
                and not stripped.startswith("//")
                and not stripped.startswith("/*")
                and stripped != "*/"
                and not stripped.startswith("*")
            ):
                return True
        return False

    def _post_process_chunks(self, chunks: List[CodeChunk]) -> List[CodeChunk]:
        """后处理chunks：去重、排序、合并"""
        if not chunks:
            return chunks

        # 按开始行排序
        sorted_chunks = sorted(chunks, key=lambda x: x.start_line)

        # 去除重叠
        non_overlapping = []
        for chunk in sorted_chunks:
            if not self._has_overlap_with_existing(chunk, non_overlapping):
                non_overlapping.append(chunk)
        return non_overlapping

    def _has_overlap_with_existing(
        self, chunk: CodeChunk, existing_chunks: List[CodeChunk]
    ) -> bool:
        """检查chunk是否与现有chunks重叠"""
        for existing in existing_chunks:
            if chunk.file_path == existing.file_path:
                # 如果chunk完全包含在existing中，且existing不是拆分后的chunk，则认为是重叠
                if (
                    chunk.start_line >= existing.start_line
                    and chunk.end_line <= existing.end_line
                ):
                    # 如果existing是拆分后的chunk（包含_part_），则不是重叠
                    if "_part_" in existing.symbol_name:
                        continue
                    # 如果chunk是拆分后的chunk，则不是重叠
                    if "_part_" in chunk.symbol_name:
                        continue
                    return True
                # 如果chunk与existing有部分重叠，则认为是重叠
                elif not (
                    chunk.end_line < existing.start_line
                    or existing.end_line < chunk.start_line
                ):
                    return True
        return False

    def _chunk_by_lines(self, file_info: FileInfo) -> List[CodeChunk]:
        """按行数分片（降级方案）"""
        chunks = []
        lines = file_info.content.split("\n")
        current_start = 0
        chunk_index = 1

        while current_start < len(lines):
            # 首先尝试检测装饰器部分
            decorator_result = self._detect_decorator_section(lines, current_start + 1)
            if decorator_result:
                content, end_line = decorator_result
                chunk = self._create_chunk(
                    content.split("\n"),
                    current_start + 1,
                    end_line,
                    f"decorator_section_{chunk_index}",
                    "decorator_section",
                    file_info,
                )
                if chunk:
                    chunks.append(chunk)
                    chunk_index += 1
                current_start = end_line
                continue

            # 尝试检测import部分
            import_result = self._detect_import_section(lines, current_start + 1)
            if import_result:
                content, end_line = import_result
                chunk = self._create_chunk(
                    content.split("\n"),
                    current_start + 1,
                    end_line,
                    f"import_section_{chunk_index}",
                    "import_section",
                    file_info,
                )
                if chunk:
                    chunks.append(chunk)
                    chunk_index += 1
                current_start = end_line
                continue

            # 常规行分片
            current_end = min(current_start + self.chunk_max_lines, len(lines))
            chunk_lines = lines[current_start:current_end]

            # 跳过空白分片
            if not self._has_meaningful_content(chunk_lines):
                current_start = current_end
                continue

            # 检测是否是import语句
            if any(
                line.strip().startswith(("import ", "from ")) for line in chunk_lines
            ):
                symbol_type = "import"
                symbol_name = f"import_{chunk_index}"
            elif any("@" in line for line in chunk_lines):
                symbol_type = "decorator_section"
                symbol_name = f"decorator_section_{chunk_index}"
            else:
                symbol_type = "other"
                symbol_name = f"chunk_{chunk_index}"

            chunk = self._create_chunk(
                chunk_lines,
                current_start + 1,
                current_end,
                symbol_name,
                symbol_type,
                file_info,
            )

            if chunk:
                chunks.append(chunk)
                chunk_index += 1

            current_start = current_end

        # 应用智能合并（如果启用）
        if self.enable_smart_merge:
            chunks = self._merge_small_chunks(chunks, file_info)

        return chunks

    def _create_chunk(
        self,
        lines: List[str],
        start_line: int,
        end_line: int,
        symbol_name: str,
        symbol_type: str,
        file_info: FileInfo,
    ) -> Optional[CodeChunk]:
        """创建代码分片（兼容性方法）"""
        content = "\n".join(lines)
        cleaned_content = clean_code_content(content)

        if not cleaned_content.strip():
            return None

        # 生成链式symbol_name格式
        chain_symbol_name = self._build_chain_symbol_name(
            symbol_name, ChunkContext(), file_info
        )

        chunk_id = generate_chunk_id(
            file_info.relative_path, start_line, end_line, chain_symbol_name
        )
        chunk_type = self._determine_chunk_type(symbol_type)

        return CodeChunk(
            id=chunk_id,
            file_path=file_info.file_path,
            relative_path=file_info.relative_path,
            start_line=start_line,
            end_line=end_line,
            content=cleaned_content,
            chunk_type=chunk_type,
            symbol_name=chain_symbol_name,
            language=file_info.language,
        )

    def _determine_chunk_type(self, symbol_type: str) -> str:
        """确定分片类型"""
        # 直接使用传入的symbol_type作为chunk_type（因为已经映射好了）
        if symbol_type in ["import", "import_section", "decorator_section", "export"]:
            return symbol_type
        return self._map_symbol_type_to_chunk_type(symbol_type)

    def chunk_markdown(self, file_info: FileInfo) -> List[CodeChunk]:
        """分片Markdown文件（保持兼容性）"""
        # 这里可以复用原有的markdown分片逻辑
        # 为了简化，暂时返回空列表
        return []
