#!/usr/bin/env python3
"""
Codebase Retriever 主程序
智能代码库检索工具
"""

from __future__ import annotations

import argparse
import asyncio
import fnmatch
import logging
import os
import sys
import time
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from codebase_processor.chunker import CodeChunker
from codebase_processor.loader import CodebaseLoader
from codebase_processor.parser import TreeSitterParser
from config import INDEX_CONFIG, SERVICE_CONFIG, SYMBOL_INDEX_CONFIG, ignore_config
from embedding_processor.calculator import EmbeddingCalculator
from index_manager.incremental_updater import IncrementalUpdater
from index_manager.repo_index import RepoIndex
from index_manager.scheduler import IndexScheduler
from insight_generator.code_insight import CodeInsightGenerator
from insight_generator.keyword_extractor import KeywordExtractor
from retrieval_service.retrieval_engine import RetrievalEngine
from symbol_processor import SymbolExtractor, SymbolIndex
from utils.helpers import ensure_directory
from web_service.api import api

logger = logging.getLogger(__name__)


class CodebaseRetriever:
    """代码库检索器主类"""

    def __init__(self, repo_path=None, cache_dir=None):
        self.repo_path = repo_path
        self.port = SERVICE_CONFIG["port"]
        self.index_cache_dir = cache_dir or SERVICE_CONFIG["index_cache_dir"]
        self.index_file_name = SERVICE_CONFIG["index_file_name"]

        # 初始化组件
        self.loader = CodebaseLoader()
        self.parser = TreeSitterParser()
        self.chunker = CodeChunker()
        self.insight_generator = CodeInsightGenerator()
        self.keyword_extractor = KeywordExtractor()
        self.embedding_calculator = EmbeddingCalculator()
        self.repo_index = RepoIndex()
        # 符号处理
        self.symbol_extractor = SymbolExtractor()
        self.symbol_index = SymbolIndex()
        # 确保 repo_index 使用同一个 symbol_index 实例
        self.repo_index.symbol_index = self.symbol_index
        # 增量更新
        self.incremental_updater = IncrementalUpdater(
            self.repo_index, self.symbol_index
        )
        self.scheduler = None
        self.retrieval_engine = None

        # 如果提供了repo_path，立即设置路径
        if self.repo_path:
            self._setup_paths()

    def _setup_paths(self):
        """设置路径相关配置"""
        # 设置仓库路径
        self.repo_index.repo_path = self.repo_path
        self.repo_index.created_at = datetime.now()

        # 生成缓存目录
        cache_dir_path = self._get_repo_cache_dir(self.index_cache_dir, self.repo_path)

        # 设置索引目录路径
        self.index_dir = os.path.join(cache_dir_path, self.repo_name)

        # 为增量更新器设置索引目录
        self.incremental_updater.set_index_dir(self.index_dir)

    def parse_arguments(self):
        """解析命令行参数"""
        parser = argparse.ArgumentParser(
            description="智能代码库检索工具",
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
示例用法:
  python main.py --repo-path /path/to/repo --port 5001
  python main.py --repo-path ./my-project --clear-cache
  python main.py --repo-path /path/to/repo --log-file app.log  # 同时输出到控制台和日志文件
  python main.py --repo-path /path/to/repo --agent-query     # 启用Agent查询模式
  python main.py --list-cache                    # 列出所有缓存
  python main.py --clean-all-cache               # 清理所有缓存

缓存机制:
  每个repo都有独立的缓存目录，格式为: {repo_name}_{hash}
  这样可以避免多个repo之间的缓存冲突。
            """,
        )

        parser.add_argument("--repo-path", help="代码库路径")

        parser.add_argument(
            "--port",
            type=int,
            default=SERVICE_CONFIG["port"],
            help=f"服务端口 (默认: {SERVICE_CONFIG['port']})",
        )

        parser.add_argument(
            "--index-dir",
            default=SERVICE_CONFIG["index_cache_dir"],
            help=f"索引缓存目录 (默认: {SERVICE_CONFIG['index_cache_dir']})",
        )

        parser.add_argument(
            "--clear-cache", action="store_true", help="清除现有索引缓存，强制重新构建"
        )

        parser.add_argument(
            "--no-incremental",
            action="store_true",
            help="禁用增量更新，总是重新构建索引",
        )

        parser.add_argument(
            "--log-level",
            choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
            default="INFO", # For cmp test
            help="日志级别",
        )

        parser.add_argument(
            "--scan-interval", type=int, default=60, help="定时扫描间隔（秒，默认60秒）"
        )

        parser.add_argument("--no-scheduler", action="store_true", help="禁用定时扫描")

        parser.add_argument(
            "--list-cache", action="store_true", help="列出所有缓存目录"
        )

        parser.add_argument(
            "--clean-all-cache", action="store_true", help="清理所有缓存目录"
        )

        parser.add_argument(
            "--log-file", 
            type=str,
            help="指定日志文件路径，同时输出到控制台和文件"
        )
        
        parser.add_argument(
            "--agent-query",
            action="store_true",
            help="启用Agent查询模式，使用LLM智能分析查询并制定检索策略"
        )

        return parser.parse_args()

    async def initialize(self, args):
        """初始化系统"""
        self.repo_path = os.path.abspath(args.repo_path)
        self.port = args.port
        
        # 处理Agent查询模式设置
        self.agent_mode = args.agent_query

        # 为每个repo创建独立的缓存目录
        self.index_cache_dir = self._get_repo_cache_dir(args.index_dir, self.repo_path)

        logger.critical("初始化代码库检索器...")
        logger.critical(f"仓库路径: {self.repo_path}")
        logger.critical(f"服务端口: {self.port}")
        logger.critical(f"Agent查询模式: {'启用' if self.agent_mode else '禁用'}")
        logger.info(f"索引目录: {self.index_cache_dir}")

        # 验证仓库路径
        if not os.path.exists(self.repo_path):
            raise FileNotFoundError(f"仓库路径不存在: {self.repo_path}")

        if not os.path.isdir(self.repo_path):
            raise NotADirectoryError(f"路径不是目录: {self.repo_path}")

        # 创建索引目录
        ensure_directory(self.index_cache_dir)

        # 设置索引目录路径
        self.index_dir = os.path.join(self.index_cache_dir, self.repo_name)

        # 为增量更新器设置索引目录
        self.incremental_updater.set_index_dir(self.index_dir)
        # 符号索引文件路径（与 RepoIndex 保持一致）
        self.symbol_index_file_path = os.path.join(
            self.index_dir,
            SYMBOL_INDEX_CONFIG.get("symbol_index_file", "symbol_index.json"),
        )

        # 清除缓存（如果需要）
        if args.clear_cache:
            self._clear_cache()

        # 设置仓库路径到索引
        self.repo_index.repo_path = self.repo_path
        self.repo_index.created_at = datetime.now()

    def _should_ignore_cache_path(self, path: str) -> bool:
        """检查缓存路径是否应该被忽略"""
        # 获取文件名或目录名
        basename = os.path.basename(path)

        # 检查每个忽略模式
        for pattern in ignore_config.all_ignore_patterns:
            if fnmatch.fnmatch(basename, pattern) or fnmatch.fnmatch(path, pattern):
                return True

        return False

    def _get_repo_cache_dir(self, base_cache_dir: str, repo_path: str) -> str:
        """为repo生成独立的缓存目录"""
        import hashlib

        # 使用repo路径的哈希值作为目录名
        repo_hash = hashlib.md5(repo_path.encode("utf-8")).hexdigest()[:12]

        # 获取repo名称作为可读标识
        self.repo_name = os.path.basename(repo_path.rstrip("/"))
        if not self.repo_name:
            self.repo_name = "root"

        # 组合目录名：repo名称_哈希值
        cache_dir_name = f"{self.repo_name}_{repo_hash}"

        cache_dir_path = os.path.join(base_cache_dir, cache_dir_name)

        # 创建一个信息文件，记录repo路径和创建时间
        info_file = os.path.join(cache_dir_path, "repo_info.txt")
        if not os.path.exists(info_file):
            ensure_directory(cache_dir_path)
            with open(info_file, "w", encoding="utf-8") as f:
                f.write(f"Repo Path: {repo_path}\n")
                f.write(f"Created: {datetime.now().isoformat()}\n")
                f.write(f"Cache Dir: {cache_dir_name}\n")

        return cache_dir_path

    def list_cache_directories(self, base_cache_dir: str):
        """列出所有缓存目录"""
        if not os.path.exists(base_cache_dir):
            logger.warning(f"缓存目录不存在: {base_cache_dir}")
            return

        logger.info(f"缓存目录: {base_cache_dir}")
        logger.info("-" * 60)

        cache_dirs = []
        for item in os.listdir(base_cache_dir):
            item_path = os.path.join(base_cache_dir, item)
            if os.path.isdir(item_path):
                info_file = os.path.join(item_path, "repo_info.txt")
                if os.path.exists(info_file):
                    try:
                        with open(info_file, encoding="utf-8") as f:
                            info = f.read()
                        cache_dirs.append((item, info, item_path))
                    except Exception:
                        cache_dirs.append((item, "无法读取信息", item_path))
                else:
                    cache_dirs.append((item, "旧格式缓存", item_path))

        if not cache_dirs:
            logger.info("没有找到缓存目录")
            return

        for i, (dir_name, info, full_path) in enumerate(cache_dirs, 1):
            logger.info(f"{i}. {dir_name}")
            for line in info.strip().split("\n"):
                logger.info(f"   {line}")

            # 计算目录大小（跳过忽略的文件）
            try:
                total_size = 0
                for dirpath, dirnames, filenames in os.walk(full_path):
                    # 过滤忽略的目录
                    dirnames[:] = [
                        d
                        for d in dirnames
                        if not self._should_ignore_cache_path(os.path.join(dirpath, d))
                    ]

                    for filename in filenames:
                        filepath = os.path.join(dirpath, filename)
                        if not self._should_ignore_cache_path(filepath):
                            total_size += os.path.getsize(filepath)

                size_mb = total_size / (1024 * 1024)
                logger.info(f"   大小: {size_mb:.2f} MB")
            except Exception:
                logger.warning("   大小: 无法计算")

            logger.info("")

    def clean_all_cache(self, base_cache_dir: str):
        """清理所有缓存目录"""
        if not os.path.exists(base_cache_dir):
            logger.warning(f"缓存目录不存在: {base_cache_dir}")
            return

        import shutil

        logger.info(f"清理缓存目录: {base_cache_dir}")

        try:
            shutil.rmtree(base_cache_dir)
            logger.info("所有缓存已清理")
        except Exception as e:
            logger.error(f"清理缓存失败: {e}")

    def _validate_index(self) -> bool:
        """验证索引完整性"""
        try:
            # 检查基本统计信息
            stats = self.repo_index.get_stats()
            if stats["total_chunks"] == 0:
                logger.warning("索引验证失败: 没有代码分片")
                return False

            # 检查仓库路径是否匹配
            if self.repo_index.repo_path != self.repo_path:
                logger.warning(
                    f"索引验证失败: 仓库路径不匹配 (索引: {self.repo_index.repo_path}, 当前: {self.repo_path})"
                )
                return False

            # 检查是否有嵌入向量
            chunks_with_embeddings = 0
            for chunk in self.repo_index.get_all_chunks():
                if chunk.embedding and any(chunk.embedding):
                    chunks_with_embeddings += 1

            if chunks_with_embeddings == 0:
                logger.warning("索引验证失败: 没有嵌入向量")
                return False

            logger.info(
                f"索引验证成功: {stats['total_chunks']}个分片, {chunks_with_embeddings}个有效嵌入向量"
            )
            return True

        except Exception as e:
            logger.error(f"索引验证异常: {e}")
            return False

    def _clear_cache(self):
        """清除索引缓存"""
        logger.info("清除索引缓存...")

        # 如果整个缓存目录存在，删除整个目录
        if os.path.exists(self.index_cache_dir):
            import shutil

            shutil.rmtree(self.index_cache_dir)
            logger.info(f"已删除缓存目录: {self.index_cache_dir}")

            # 重新创建目录
            ensure_directory(self.index_cache_dir)
        else:
            # 如果目录不存在，只删除可能存在的索引目录
            if os.path.exists(self.index_dir):
                import shutil

                shutil.rmtree(self.index_dir)

            # 无其他缓存文件可删

    async def load_or_build_index(self, force_rebuild: bool = False):
        """加载或构建索引"""
        # 确保路径已设置
        if not hasattr(self, "index_dir") or not self.index_dir:
            if not self.repo_path:
                raise ValueError("未设置repo_path，无法构建索引")
            self._setup_paths()

        logger.info("检查索引状态...")

        # 检查索引目录是否存在
        metadata_file = os.path.join(self.index_dir, "metadata.json")
        compressed_metadata = metadata_file + ".gz"
        index_exists = os.path.exists(self.index_dir) and (
            os.path.exists(metadata_file) or os.path.exists(compressed_metadata)
        )

        # 尝试加载现有索引
        index_loaded = False
        if not force_rebuild and index_exists:
            logger.critical("发现现有索引，尝试加载...")
            index_loaded = self.repo_index.load_index(self.index_dir)

            if index_loaded:
                logger.info("索引加载成功")

                # 试图加载符号索引（若存在）
                symbol_loaded = self.symbol_index.load_index(
                    self.symbol_index_file_path
                )
                if symbol_loaded:
                    logger.info("符号索引加载成功")
                else:
                    logger.info("符号索引加载失败或不存在")

                # 无论符号索引是否加载成功，都要确保 repo_index 使用同一个 symbol_index 实例
                # 这样即使符号索引为空，RetrievalEngine也能正确初始化
                self.repo_index.symbol_index = self.symbol_index

                # 验证索引完整性
                if self._validate_index():
                    # 检查是否需要增量更新
                    if self.incremental_updater.is_incremental_update_needed(
                        self.repo_path
                    ):
                        logger.info("检测到文件变更，执行增量更新...")
                        t0 = time.time()
                        update_stats = await self.incremental_updater.update_index(
                            self.repo_path
                        )
                        t1 = time.time()
                        logger.info(
                            f"增量更新完成: {update_stats}, 耗时: {t1 - t0:.2f}秒"
                        )

                        # 保存更新后的索引元数据
                        logger.info("保存更新后的索引元数据...")
                        self.repo_index.save_metadata(self.index_dir)
                        # 存符号索引
                        self.symbol_index.save_index(self.symbol_index_file_path)
                    else:
                        # logger.info("无需更新，索引是最新的")
                        # 如果符号索引为空，单独构建符号索引
                        if not self.symbol_index.get_all_symbols():
                            logger.info("符号索引缺失或为空，构建符号索引...")
                            await self._build_symbol_index_only()
                else:
                    logger.warning("索引验证失败，需要重新构建")
                    index_loaded = False
            else:
                logger.warning("索引加载失败，需要重新构建")

        # 如果索引加载失败或需要重建，则重新构建
        if not index_loaded:
            logger.critical("构建新索引...")
            await self._build_full_index()

    async def _build_full_index(self):
        """按文件粒度构建完整索引"""
        start_time = time.time()

        # 阶段1: 加载文件
        logger.critical("阶段1: 加载代码文件...")
        file_infos = self.loader.load_files(self.repo_path)
        logger.info(f"加载了 {len(file_infos)} 个文件")

        if not file_infos:
            logger.warning("未找到任何支持的代码文件，将创建空索引")
            # 创建空索引目录
            ensure_directory(self.index_dir)
            # 设置仓库基本信息
            self.repo_index.repo_path = self.repo_path
            self.repo_index.created_at = datetime.now()
            # 生成空仓库洞察
            repo_insight = self.insight_generator.generate_repo_insight([])
            self.repo_index.set_repo_insight(repo_insight)
            # 保存元数据
            self.repo_index.save_metadata(self.index_dir)
            logger.info("空索引创建完成")
            return

        # 显示文件统计
        file_stats = self.loader.get_file_stats(self.repo_path)
        logger.info(f"文件统计: {file_stats['languages']}")

        # 按文件粒度并行处理
        logger.critical("阶段2: 按文件粒度并行处理...")
        total_chunks = 0
        processed_files = 0

        # 确保索引目录存在
        ensure_directory(self.index_dir)

        # 并行处理文件
        import asyncio

        max_concurrent = INDEX_CONFIG.get("max_concurrent_files", 5)
        semaphore = asyncio.Semaphore(max_concurrent)

        async def process_file_with_semaphore(file_info):
            async with semaphore:
                return await self._process_and_save_single_file(file_info)

        # 批量执行任务，显示进度（按批次动态创建协程，避免中断时产生未等待的协程）
        batch_size = max(range(25,20,-1), key=lambda step: len(file_infos) % step or step)
        for i in range(0, len(file_infos), batch_size):
            current_files = file_infos[i : i + batch_size]
            batch_tasks = [process_file_with_semaphore(file_info) for file_info in current_files]
            logger.critical(
                f"  处理进度: {i + 1}-{min(i + batch_size, len(file_infos))}/{len(file_infos)} 文件"
            )

            # 执行当前批次
            batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)

            # 处理结果
            for j, result in enumerate(batch_results):
                file_info = current_files[j]
                if isinstance(result, Exception):
                    logger.warning(f"处理文件失败 {file_info.relative_path}: {result}")
                    continue

                if result:
                    total_chunks += len(result)
                    processed_files += 1
                    logger.debug(
                        f"  文件 {file_info.relative_path}: {len(result)} 个分片"
                    )
            logger.debug(f"处理完成{i + batch_size}/{len(file_infos)}")

        logger.info(
            f"并行处理完成: {processed_files} 个文件，生成了 {total_chunks} 个代码分片"
        )

        if total_chunks == 0:
            raise ValueError("未生成任何代码分片")

        # 生成仓库洞察
        logger.critical("生成仓库洞察...")
        all_chunks = self.repo_index.get_all_chunks()
        repo_insight = self.insight_generator.generate_repo_insight(all_chunks)
        self.repo_index.set_repo_insight(repo_insight)

        # 最终保存元数据
        logger.critical("保存索引元数据...")
        self.repo_index.save_metadata(self.index_dir)

        # 显示构建统计
        build_time = time.time() - start_time
        index_stats = self.repo_index.get_stats()

        logger.critical("索引构建完成!")
        logger.info(f"构建时间: {build_time:.2f} 秒")
        logger.info(f"总分片数: {index_stats['total_chunks']}")
        logger.info(f"总文件数: {index_stats['total_files']}")
        logger.info(f"支持语言: {list(index_stats['languages'].keys())}")
        logger.info(f"索引已保存到: {self.index_dir}")

    async def _process_and_save_single_file(self, file_info) -> list:
        """完整处理并保存单个文件：分片 -> 洞察 -> 关键词 -> 嵌入 -> 保存"""
        try:
            # 1. 解析文件
            parse_tree = self.parser.parse_file(file_info)
            if not parse_tree:
                return []

            symbol_count = 0
            # 2. 分片和符号提取
            if file_info.language == "markdown":
                chunks = self.chunker.chunk_markdown(file_info)
            else:
                chunks = self.chunker.chunk_by_symbols(parse_tree, file_info)

            # 符号提取
            try:
                symbols = self.symbol_extractor.extract_symbols(parse_tree, file_info)
                for sym in symbols:
                    self.symbol_index.add_symbol(sym)
                symbol_count += len(symbols)
            except Exception as se:
                logger.warning(f"符号提取失败 {file_info.relative_path}: {se}")

            if not chunks:
                # 即使没有分片，也要更新文件校验和并保存空索引，避免重复处理
                logger.debug(f"文件 {file_info.relative_path} 无法分片或为空文件，保存空索引")
                
                # 更新文件校验和
                self.repo_index.update_file_checksum(
                    file_info.relative_path, file_info.crc32
                )
                
                # 保存空索引（重要：避免定时刷新时重复处理）
                self.repo_index.save_file_index(self.index_dir, file_info.relative_path)
                
                return []

            # 3. 生成洞察
            # chunks = await self.insight_generator.update_chunk_insights(chunks) # For cmp test

            # 4. 提取关键词
            chunks = self.keyword_extractor.update_chunk_keywords(chunks)

            # 5/6. 并行执行：文件级洞察（LLM）与嵌入计算
            embedding_task = asyncio.create_task(
                self.embedding_calculator.update_chunk_embeddings(chunks)
            )
            insight_task = asyncio.create_task(
                self.insight_generator.generate_file_insight(
                    file_info.relative_path, chunks
                )
            )

            emb_result, file_insight = await asyncio.gather(
                embedding_task, insight_task, return_exceptions=True
            )

            # 处理嵌入结果（必须回填）
            if isinstance(emb_result, Exception):
                logger.warning(
                    f"计算嵌入失败 {file_info.relative_path}: {emb_result}，将使用零向量降级"
                )
                # 保留原 chunks（内部降级零向量）
            else:
                chunks = emb_result

            # 处理洞察结果（非关键路径，失败可忽略）
            if isinstance(file_insight, Exception):
                logger.debug(f"文件级洞察失败 {file_info.relative_path}: {file_insight}")
            elif file_insight:
                self.repo_index.set_file_insight(file_info.relative_path, file_insight)

            # 7. 将分片添加到索引
            for chunk in chunks:
                self.repo_index.add_chunk(chunk)

            # 8. 更新文件校验和
            self.repo_index.update_file_checksum(
                file_info.relative_path, file_info.crc32
            )

            # 8. 立即保存该文件的索引
            self.repo_index.save_file_index(self.index_dir, file_info.relative_path)

            return chunks

        except Exception as e:
            logger.error(f"完整处理文件失败 {file_info.relative_path}: {e}")
            return []

    async def _build_symbol_index_only(self):
        """仅构建符号索引（在已有代码分片索引的情况下）"""
        logger.info("开始单独构建符号索引...")
        symbol_count = 0
        # 加载文件
        file_infos = self.loader.load_files(self.repo_path)
        for i, file_info in enumerate(file_infos):
            if i % 50 == 0:
                logger.info(f"  符号索引进度: {i + 1}/{len(file_infos)}")
            try:
                parse_tree = self.parser.parse_file(file_info)
                if not parse_tree or file_info.language == "markdown":
                    continue
                symbols = self.symbol_extractor.extract_symbols(parse_tree, file_info)
                for sym in symbols:
                    self.symbol_index.add_symbol(sym)
                symbol_count += len(symbols)
            except Exception as e:
                logger.info(f"符号提取失败 {file_info.relative_path}: {e}")
                continue
        # 保存
        self.symbol_index.save_index(self.symbol_index_file_path)
        logger.info(
            f"符号索引构建完成: {symbol_count} 个符号，保存到 {self.symbol_index_file_path}"
        )

    def start_service(self, enable_scheduler: bool = True, scan_interval: int = 300):
        """启动Web服务"""
        logger.critical("启动检索服务...")

        # 创建检索引擎
        self.retrieval_engine = RetrievalEngine(self.repo_index, self.repo_path, self.agent_mode)

        # 设置API的检索引擎与符号索引
        api.set_retrieval_engine(self.retrieval_engine)
        api.set_symbol_index(self.symbol_index)

        # 启动定时扫描（如果启用）
        if enable_scheduler:
            self.scheduler = IndexScheduler(self.incremental_updater, self.repo_path)
            self.scheduler.set_scan_interval(scan_interval)

            # 设置更新回调
            def on_index_update(**_kwargs):
                # logger.info(f"索引已更新: {_kwargs}")
                pass

            self.scheduler.set_update_callback(on_index_update)
            self.scheduler.start()

        # 显示服务信息
        index_stats = self.repo_index.get_stats()
        logger.info("服务已就绪:")
        logger.critical(f"  服务地址: http://127.0.0.1:{self.port}")
        logger.info(
            f"  索引状态: {index_stats['total_files']} 个文件，{index_stats['total_chunks']} 个分片，{index_stats['total_keywords']} 个关键词"
        )
        logger.info(f"  定时扫描: {'启用' if enable_scheduler else '禁用'}")
        if enable_scheduler:
            logger.info(f"  扫描间隔: {scan_interval}秒")
        logger.info("  API端点:")
        logger.info("    健康检查: GET /health")
        logger.info("    代码检索: POST /query")
        logger.info("    系统统计: GET /stats")
        logger.info("    刷新索引: POST /indexes/update")
        logger.info("    重建索引: POST /reindex")
        logger.info("    仓库洞察: POST /codebase_insight")
        logger.info("    符号索引: POST /symbol_query")

        # 启动服务
        try:
            api.run(host="127.0.0.1", port=self.port)
        finally:
            # 服务关闭时停止定时扫描
            if self.scheduler:
                self.scheduler.stop()

    async def initialize_async(self, args):
        """异步初始化部分"""
        # 加载或构建索引
        await self.load_or_build_index(force_rebuild=args.no_incremental)

    def run(self):
        """运行主程序"""
        try:
            # 解析参数
            args = self.parse_arguments()

            # 处理缓存管理命令
            if args.list_cache:
                self.list_cache_directories(SERVICE_CONFIG["index_cache_dir"])
                return

            if args.clean_all_cache:
                self.clean_all_cache(SERVICE_CONFIG["index_cache_dir"])
                return

            # 检查repo-path参数
            if not args.repo_path:
                logger.error("错误: 启动服务需要指定 --repo-path 参数")
                sys.exit(1)

            # 同步初始化
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            # 设置日志
            if args.log_level:
                level = getattr(logging, args.log_level.upper(), logging.INFO)
            else:
                level = logging.INFO
            
            # 配置日志格式
            log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            date_format = "%Y-%m-%d %H:%M:%S"
            
            # 获取根logger
            root_logger = logging.getLogger()
            root_logger.setLevel(level)
            
            # 清除可能存在的默认handler
            for handler in root_logger.handlers[:]:
                root_logger.removeHandler(handler)
            
            # 创建控制台handler
            console_handler = logging.StreamHandler()
            console_handler.setLevel(level)
            console_formatter = logging.Formatter(log_format, date_format)
            console_handler.setFormatter(console_formatter)
            root_logger.addHandler(console_handler)
            
            # 如果指定了日志文件，创建文件handler
            if args.log_file:
                file_handler = logging.FileHandler(args.log_file, mode='a', encoding='utf-8')
                file_handler.setLevel(level)
                file_formatter = logging.Formatter(log_format, date_format)
                file_handler.setFormatter(file_formatter)
                root_logger.addHandler(file_handler)
                logger.info(f"日志同时输出到文件: {args.log_file}")

            logging.getLogger("httpx").setLevel(logging.WARNING)
            logging.getLogger("httpcore").setLevel(logging.WARNING)
            logging.getLogger("hpack").setLevel(logging.WARNING)
            logging.getLogger("openai").setLevel(logging.WARNING)

            try:
                # 初始化
                loop.run_until_complete(self.initialize(args))

                # 异步初始化
                loop.run_until_complete(self.initialize_async(args))
            except Exception as init_error:
                logger.error(f"初始化异常: {init_error}")
                raise
            finally:
                # 安全关闭事件循环
                try:
                    # 取消所有未完成的任务
                    pending_tasks = [task for task in asyncio.all_tasks(loop) if not task.done()]
                    if pending_tasks:
                        logger.debug(f"取消 {len(pending_tasks)} 个未完成的任务")
                        for task in pending_tasks:
                            task.cancel()
                        # 等待任务取消完成
                        loop.run_until_complete(asyncio.gather(*pending_tasks, return_exceptions=True))
                finally:
                    if not loop.is_closed():
                        loop.close()

            # 启动服务（同步）
            self.start_service(
                enable_scheduler=not args.no_scheduler, scan_interval=args.scan_interval
            )

        except KeyboardInterrupt:
            logger.info("\n收到中断信号，正在关闭服务...")
        except Exception as e:
            logger.exception(f"程序异常: {e}")
            sys.exit(1)


def main():
    """主函数"""
    retriever = CodebaseRetriever()
    retriever.run()


if __name__ == "__main__":
    # 设置事件循环策略（Windows兼容性）
    if sys.platform.startswith("win"):
        # 对 httpx/uvicorn 更稳定的选择：SelectorEventLoopPolicy
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

    # 运行主程序
    main()
