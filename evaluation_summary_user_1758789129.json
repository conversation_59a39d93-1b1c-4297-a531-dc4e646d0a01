{"overall_metrics": {"path_matching": {"total_queries": 1661, "exact_match_count": 1263, "exact_match_rate": 0.7603853100541842, "avg_k": 5.669476219145094, "success_rate_at_1": 0.4599638771824202, "success_rate_at_3": 0.6478025285972305, "success_rate_at_5": 0.7092113184828417, "success_rate_at_k": 0.7284768211920529, "precision_at_1": 0.4599638771824202, "precision_at_3": 0.26590407385109205, "precision_at_5": 0.22549668874171985, "precision_at_k": 0.01053582179409993, "mrr_at_k": 0.5664320594784173, "ndcg_at_k": 0.049140615889029515, "f1_score": 0.4599638771824202, "avg_query_time_ms": 2533.553909351135, "max_query_time_ms": 81512.86387443542, "min_query_time_ms": 1158.6670875549316}, "content_matching": {"total_queries": 1661, "exact_match_count": 1205, "exact_match_rate": 0.7254665863937387, "avg_k": 5.669476219145094, "success_rate_at_1": 0.4124021673690548, "success_rate_at_3": 0.6074653822998194, "success_rate_at_5": 0.6742925948223961, "success_rate_at_k": 0.695364238410596, "precision_at_1": 0.4124021673690548, "precision_at_3": 0.2482440297009821, "precision_at_5": 0.21229179209311486, "precision_at_k": 0.010435480634156122, "mrr_at_k": 0.5223347476658736, "ndcg_at_k": 0.04792877780394794, "f1_score": 0.4124021673690548, "avg_query_time_ms": 2533.553909351135, "max_query_time_ms": 81512.86387443542, "min_query_time_ms": 1158.6670875549316}, "total_queries": 1661, "exact_match_count": 1263, "exact_match_rate": 0.7603853100541842, "avg_k": 5.669476219145094, "success_rate_at_1": 0.4599638771824202, "success_rate_at_3": 0.6478025285972305, "success_rate_at_5": 0.7092113184828417, "success_rate_at_k": 0.7284768211920529, "precision_at_1": 0.4599638771824202, "precision_at_3": 0.26590407385109205, "precision_at_5": 0.22549668874171985, "precision_at_k": 0.01053582179409993, "mrr_at_k": 0.5664320594784173, "ndcg_at_k": 0.049140615889029515, "f1_score": 0.4599638771824202, "avg_query_time_ms": 2533.553909351135, "max_query_time_ms": 81512.86387443542, "min_query_time_ms": 1158.6670875549316}, "intent_metrics_overall": {"debugging": {"path_matching": {"total_queries": 296, "exact_match_count": 254, "exact_match_rate": 0.8581081081081081, "avg_k": 6.081081081081081, "success_rate_at_1": 0.5540540540540541, "success_rate_at_3": 0.7567567567567568, "success_rate_at_5": 0.8108108108108109, "success_rate_at_k": 0.8277027027027027, "precision_at_1": 0.5540540540540541, "precision_at_3": 0.3023648648648647, "precision_at_5": 0.2465653153153157, "precision_at_k": 0.0152027027027027, "mrr_at_k": 0.6665915915915914, "ndcg_at_k": 0.0733622909713101, "f1_score": 0.5540540540540541, "avg_query_time_ms": 2560.619317196511, "max_query_time_ms": 35463.72389793396, "min_query_time_ms": 1209.8898887634277}, "content_matching": {"total_queries": 296, "exact_match_count": 248, "exact_match_rate": 0.8378378378378378, "avg_k": 6.081081081081081, "success_rate_at_1": 0.5168918918918919, "success_rate_at_3": 0.722972972972973, "success_rate_at_5": 0.793918918918919, "success_rate_at_k": 0.8108108108108109, "precision_at_1": 0.5168918918918919, "precision_at_3": 0.2905405405405404, "precision_at_5": 0.24127252252252288, "precision_at_k": 0.01632882882882883, "mrr_at_k": 0.6334298584298583, "ndcg_at_k": 0.07462576984939802, "f1_score": 0.5168918918918919, "avg_query_time_ms": 2560.619317196511, "max_query_time_ms": 35463.72389793396, "min_query_time_ms": 1209.8898887634277}, "total_queries": 296, "exact_match_count": 254, "exact_match_rate": 0.8581081081081081, "avg_k": 6.081081081081081, "success_rate_at_1": 0.5540540540540541, "success_rate_at_3": 0.7567567567567568, "success_rate_at_5": 0.8108108108108109, "success_rate_at_k": 0.8277027027027027, "precision_at_1": 0.5540540540540541, "precision_at_3": 0.3023648648648647, "precision_at_5": 0.2465653153153157, "precision_at_k": 0.0152027027027027, "mrr_at_k": 0.6665915915915914, "ndcg_at_k": 0.0733622909713101, "f1_score": 0.5540540540540541, "avg_query_time_ms": 2560.619317196511, "max_query_time_ms": 35463.72389793396, "min_query_time_ms": 1209.8898887634277}, "understanding": {"path_matching": {"total_queries": 295, "exact_match_count": 255, "exact_match_rate": 0.864406779661017, "avg_k": 5.437288135593221, "success_rate_at_1": 0.5898305084745763, "success_rate_at_3": 0.7796610169491526, "success_rate_at_5": 0.8338983050847457, "success_rate_at_k": 0.8338983050847457, "precision_at_1": 0.5898305084745763, "precision_at_3": 0.33050847457627097, "precision_at_5": 0.28310734463276876, "precision_at_k": 0.28310734463276876, "mrr_at_k": 0.6942345977939198, "ndcg_at_k": 0.7262978367041141, "f1_score": 0.5898305084745763, "avg_query_time_ms": 2711.9824482222734, "max_query_time_ms": 81512.86387443542, "min_query_time_ms": 1158.6670875549316}, "content_matching": {"total_queries": 295, "exact_match_count": 241, "exact_match_rate": 0.8169491525423729, "avg_k": 5.437288135593221, "success_rate_at_1": 0.5220338983050847, "success_rate_at_3": 0.7423728813559322, "success_rate_at_5": 0.7898305084745763, "success_rate_at_k": 0.7898305084745763, "precision_at_1": 0.5220338983050847, "precision_at_3": 0.310734463276836, "precision_at_5": 0.2638983050847461, "precision_at_k": 0.2638983050847461, "mrr_at_k": 0.6372168415388754, "ndcg_at_k": 0.6732033262042814, "f1_score": 0.5220338983050847, "avg_query_time_ms": 2711.9824482222734, "max_query_time_ms": 81512.86387443542, "min_query_time_ms": 1158.6670875549316}, "total_queries": 295, "exact_match_count": 255, "exact_match_rate": 0.864406779661017, "avg_k": 5.437288135593221, "success_rate_at_1": 0.5898305084745763, "success_rate_at_3": 0.7796610169491526, "success_rate_at_5": 0.8338983050847457, "success_rate_at_k": 0.8338983050847457, "precision_at_1": 0.5898305084745763, "precision_at_3": 0.33050847457627097, "precision_at_5": 0.28310734463276876, "precision_at_k": 0.28310734463276876, "mrr_at_k": 0.6942345977939198, "ndcg_at_k": 0.7262978367041141, "f1_score": 0.5898305084745763, "avg_query_time_ms": 2711.9824482222734, "max_query_time_ms": 81512.86387443542, "min_query_time_ms": 1158.6670875549316}, "optimization": {"path_matching": {"total_queries": 284, "exact_match_count": 194, "exact_match_rate": 0.6830985915492958, "avg_k": 5.75, "success_rate_at_1": 0.4014084507042254, "success_rate_at_3": 0.5598591549295775, "success_rate_at_5": 0.6232394366197183, "success_rate_at_k": 0.647887323943662, "precision_at_1": 0.4014084507042254, "precision_at_3": 0.22417840375586873, "precision_at_5": 0.19002347417840393, "precision_at_k": 0.010563380281690137, "mrr_at_k": 0.49614213056114465, "ndcg_at_k": 0.04454598807964769, "f1_score": 0.4014084507042254, "avg_query_time_ms": 2738.6604488735466, "max_query_time_ms": 35429.983139038086, "min_query_time_ms": 1226.1531352996826}, "content_matching": {"total_queries": 284, "exact_match_count": 192, "exact_match_rate": 0.676056338028169, "avg_k": 5.75, "success_rate_at_1": 0.3732394366197183, "success_rate_at_3": 0.5492957746478874, "success_rate_at_5": 0.6091549295774648, "success_rate_at_k": 0.6373239436619719, "precision_at_1": 0.3732394366197183, "precision_at_3": 0.21772300469483585, "precision_at_5": 0.18239436619718322, "precision_at_k": 0.00997652582159624, "mrr_at_k": 0.47351469930695284, "ndcg_at_k": 0.04472747005542448, "f1_score": 0.3732394366197183, "avg_query_time_ms": 2738.6604488735466, "max_query_time_ms": 35429.983139038086, "min_query_time_ms": 1226.1531352996826}, "total_queries": 284, "exact_match_count": 194, "exact_match_rate": 0.6830985915492958, "avg_k": 5.75, "success_rate_at_1": 0.4014084507042254, "success_rate_at_3": 0.5598591549295775, "success_rate_at_5": 0.6232394366197183, "success_rate_at_k": 0.647887323943662, "precision_at_1": 0.4014084507042254, "precision_at_3": 0.22417840375586873, "precision_at_5": 0.19002347417840393, "precision_at_k": 0.010563380281690137, "mrr_at_k": 0.49614213056114465, "ndcg_at_k": 0.04454598807964769, "f1_score": 0.4014084507042254, "avg_query_time_ms": 2738.6604488735466, "max_query_time_ms": 35429.983139038086, "min_query_time_ms": 1226.1531352996826}, "implementation": {"path_matching": {"total_queries": 287, "exact_match_count": 229, "exact_match_rate": 0.7979094076655052, "avg_k": 6.634146341463414, "success_rate_at_1": 0.4425087108013937, "success_rate_at_3": 0.6445993031358885, "success_rate_at_5": 0.7177700348432056, "success_rate_at_k": 0.7630662020905923, "precision_at_1": 0.4425087108013937, "precision_at_3": 0.25900116144018576, "precision_at_5": 0.2149825783972128, "precision_at_k": 0.008461921353907415, "mrr_at_k": 0.5582185719816382, "ndcg_at_k": 0.042561805322326784, "f1_score": 0.4425087108013937, "avg_query_time_ms": 2215.5065494962687, "max_query_time_ms": 9586.543798446655, "min_query_time_ms": 1163.3169651031494}, "content_matching": {"total_queries": 287, "exact_match_count": 220, "exact_match_rate": 0.7665505226480837, "avg_k": 6.634146341463414, "success_rate_at_1": 0.3902439024390244, "success_rate_at_3": 0.6027874564459931, "success_rate_at_5": 0.6794425087108014, "success_rate_at_k": 0.735191637630662, "precision_at_1": 0.3902439024390244, "precision_at_3": 0.2450638792102208, "precision_at_5": 0.2065040650406506, "precision_at_k": 0.008461921353907415, "mrr_at_k": 0.5111498257839721, "ndcg_at_k": 0.04036344381162485, "f1_score": 0.3902439024390244, "avg_query_time_ms": 2215.5065494962687, "max_query_time_ms": 9586.543798446655, "min_query_time_ms": 1163.3169651031494}, "total_queries": 287, "exact_match_count": 229, "exact_match_rate": 0.7979094076655052, "avg_k": 6.634146341463414, "success_rate_at_1": 0.4425087108013937, "success_rate_at_3": 0.6445993031358885, "success_rate_at_5": 0.7177700348432056, "success_rate_at_k": 0.7630662020905923, "precision_at_1": 0.4425087108013937, "precision_at_3": 0.25900116144018576, "precision_at_5": 0.2149825783972128, "precision_at_k": 0.008461921353907415, "mrr_at_k": 0.5582185719816382, "ndcg_at_k": 0.042561805322326784, "f1_score": 0.4425087108013937, "avg_query_time_ms": 2215.5065494962687, "max_query_time_ms": 9586.543798446655, "min_query_time_ms": 1163.3169651031494}, "testing": {"path_matching": {"total_queries": 250, "exact_match_count": 166, "exact_match_rate": 0.664, "avg_k": 4.168, "success_rate_at_1": 0.372, "success_rate_at_3": 0.552, "success_rate_at_5": 0.628, "success_rate_at_k": 0.608, "precision_at_1": 0.372, "precision_at_3": 0.24133333333333362, "precision_at_5": 0.22586666666666683, "precision_at_k": 0.028, "mrr_at_k": 0.47658253968253966, "ndcg_at_k": 0.08778671376830988, "f1_score": 0.372, "avg_query_time_ms": 2545.3870782852173, "max_query_time_ms": 33711.30323410034, "min_query_time_ms": 1246.469259262085}, "content_matching": {"total_queries": 250, "exact_match_count": 155, "exact_match_rate": 0.62, "avg_k": 4.168, "success_rate_at_1": 0.356, "success_rate_at_3": 0.52, "success_rate_at_5": 0.6, "success_rate_at_k": 0.576, "precision_at_1": 0.356, "precision_at_3": 0.22666666666666685, "precision_at_5": 0.21426666666666677, "precision_at_k": 0.026, "mrr_at_k": 0.45130000000000003, "ndcg_at_k": 0.08221555672545239, "f1_score": 0.356, "avg_query_time_ms": 2545.3870782852173, "max_query_time_ms": 33711.30323410034, "min_query_time_ms": 1246.469259262085}, "total_queries": 250, "exact_match_count": 166, "exact_match_rate": 0.664, "avg_k": 4.168, "success_rate_at_1": 0.372, "success_rate_at_3": 0.552, "success_rate_at_5": 0.628, "success_rate_at_k": 0.608, "precision_at_1": 0.372, "precision_at_3": 0.24133333333333362, "precision_at_5": 0.22586666666666683, "precision_at_k": 0.028, "mrr_at_k": 0.47658253968253966, "ndcg_at_k": 0.08778671376830988, "f1_score": 0.372, "avg_query_time_ms": 2545.3870782852173, "max_query_time_ms": 33711.30323410034, "min_query_time_ms": 1246.469259262085}, "integration": {"path_matching": {"total_queries": 249, "exact_match_count": 165, "exact_match_rate": 0.6626506024096386, "avg_k": 5.759036144578313, "success_rate_at_1": 0.36947791164658633, "success_rate_at_3": 0.5622489959839357, "success_rate_at_5": 0.6104417670682731, "success_rate_at_k": 0.6305220883534136, "precision_at_1": 0.36947791164658633, "precision_at_3": 0.22623828647925054, "precision_at_5": 0.18440428380187426, "precision_at_k": 0.011378848728246316, "mrr_at_k": 0.4758016191751131, "ndcg_at_k": 0.050643974152486664, "f1_score": 0.36947791164658633, "avg_query_time_ms": 2410.7557693159724, "max_query_time_ms": 38333.45532417297, "min_query_time_ms": 1213.907241821289}, "content_matching": {"total_queries": 249, "exact_match_count": 149, "exact_match_rate": 0.5983935742971888, "avg_k": 5.759036144578313, "success_rate_at_1": 0.285140562248996, "success_rate_at_3": 0.46987951807228917, "success_rate_at_5": 0.5381526104417671, "success_rate_at_k": 0.5662650602409639, "precision_at_1": 0.285140562248996, "precision_at_3": 0.18406961178045522, "precision_at_5": 0.15548862115127174, "precision_at_k": 0.012717536813922351, "mrr_at_k": 0.3940587747816664, "ndcg_at_k": 0.05621366414138042, "f1_score": 0.285140562248996, "avg_query_time_ms": 2410.7557693159724, "max_query_time_ms": 38333.45532417297, "min_query_time_ms": 1213.907241821289}, "total_queries": 249, "exact_match_count": 165, "exact_match_rate": 0.6626506024096386, "avg_k": 5.759036144578313, "success_rate_at_1": 0.36947791164658633, "success_rate_at_3": 0.5622489959839357, "success_rate_at_5": 0.6104417670682731, "success_rate_at_k": 0.6305220883534136, "precision_at_1": 0.36947791164658633, "precision_at_3": 0.22623828647925054, "precision_at_5": 0.18440428380187426, "precision_at_k": 0.011378848728246316, "mrr_at_k": 0.4758016191751131, "ndcg_at_k": 0.050643974152486664, "f1_score": 0.36947791164658633, "avg_query_time_ms": 2410.7557693159724, "max_query_time_ms": 38333.45532417297, "min_query_time_ms": 1213.907241821289}}, "repo_metrics": {"legado-Harmony": {"path_matching": {"total_queries": 599, "exact_match_count": 387, "exact_match_rate": 0.6460767946577629, "avg_k": 6.764607679465776, "success_rate_at_1": 0.35225375626043404, "success_rate_at_3": 0.5141903171953256, "success_rate_at_5": 0.5843071786310517, "success_rate_at_k": 0.6210350584307178, "precision_at_1": 0.35225375626043404, "precision_at_3": 0.1830829159710622, "precision_at_5": 0.1419031719532562, "precision_at_k": 0.0073932745051275926, "mrr_at_k": 0.44824442854492924, "ndcg_at_k": 0.04137486580540908, "f1_score": 0.35225375626043404, "avg_query_time_ms": 2829.711059099048, "max_query_time_ms": 17061.41495704651, "min_query_time_ms": 1209.8898887634277}, "content_matching": {"total_queries": 599, "exact_match_count": 387, "exact_match_rate": 0.6460767946577629, "avg_k": 6.764607679465776, "success_rate_at_1": 0.34056761268781305, "success_rate_at_3": 0.5041736227045075, "success_rate_at_5": 0.5792988313856428, "success_rate_at_k": 0.6210350584307178, "precision_at_1": 0.34056761268781305, "precision_at_3": 0.17890929326655472, "precision_at_5": 0.14006677796327285, "precision_at_k": 0.007154781779155734, "mrr_at_k": 0.4390598086758354, "ndcg_at_k": 0.03674269897670564, "f1_score": 0.34056761268781305, "avg_query_time_ms": 2829.711059099048, "max_query_time_ms": 17061.41495704651, "min_query_time_ms": 1209.8898887634277}, "total_queries": 599, "exact_match_count": 387, "exact_match_rate": 0.6460767946577629, "avg_k": 6.764607679465776, "success_rate_at_1": 0.35225375626043404, "success_rate_at_3": 0.5141903171953256, "success_rate_at_5": 0.5843071786310517, "success_rate_at_k": 0.6210350584307178, "precision_at_1": 0.35225375626043404, "precision_at_3": 0.1830829159710622, "precision_at_5": 0.1419031719532562, "precision_at_k": 0.0073932745051275926, "mrr_at_k": 0.44824442854492924, "ndcg_at_k": 0.04137486580540908, "f1_score": 0.35225375626043404, "avg_query_time_ms": 2829.711059099048, "max_query_time_ms": 17061.41495704651, "min_query_time_ms": 1209.8898887634277}, "HOme_App": {"path_matching": {"total_queries": 201, "exact_match_count": 155, "exact_match_rate": 0.7711442786069652, "avg_k": 5.502487562189055, "success_rate_at_1": 0.4079601990049751, "success_rate_at_3": 0.6019900497512438, "success_rate_at_5": 0.6965174129353234, "success_rate_at_k": 0.7064676616915423, "precision_at_1": 0.4079601990049751, "precision_at_3": 0.23963515754560555, "precision_at_5": 0.2105306799336652, "precision_at_k": 0.009950248756218905, "mrr_at_k": 0.5249190555160703, "ndcg_at_k": 0.03500044611225245, "f1_score": 0.4079601990049751, "avg_query_time_ms": 2218.619914790291, "max_query_time_ms": 16903.612852096558, "min_query_time_ms": 1213.907241821289}, "content_matching": {"total_queries": 201, "exact_match_count": 155, "exact_match_rate": 0.7711442786069652, "avg_k": 5.502487562189055, "success_rate_at_1": 0.4527363184079602, "success_rate_at_3": 0.6517412935323383, "success_rate_at_5": 0.7213930348258707, "success_rate_at_k": 0.7313432835820896, "precision_at_1": 0.4527363184079602, "precision_at_3": 0.2595356550580435, "precision_at_5": 0.21965174129353257, "precision_at_k": 0.009950248756218905, "mrr_at_k": 0.5627833056937533, "ndcg_at_k": 0.03748800830130718, "f1_score": 0.4527363184079602, "avg_query_time_ms": 2218.619914790291, "max_query_time_ms": 16903.612852096558, "min_query_time_ms": 1213.907241821289}, "total_queries": 201, "exact_match_count": 155, "exact_match_rate": 0.7711442786069652, "avg_k": 5.502487562189055, "success_rate_at_1": 0.4079601990049751, "success_rate_at_3": 0.6019900497512438, "success_rate_at_5": 0.6965174129353234, "success_rate_at_k": 0.7064676616915423, "precision_at_1": 0.4079601990049751, "precision_at_3": 0.23963515754560555, "precision_at_5": 0.2105306799336652, "precision_at_k": 0.009950248756218905, "mrr_at_k": 0.5249190555160703, "ndcg_at_k": 0.03500044611225245, "f1_score": 0.4079601990049751, "avg_query_time_ms": 2218.619914790291, "max_query_time_ms": 16903.612852096558, "min_query_time_ms": 1213.907241821289}, "S1-Orange": {"path_matching": {"total_queries": 301, "exact_match_count": 242, "exact_match_rate": 0.8039867109634552, "avg_k": 5.591362126245847, "success_rate_at_1": 0.5514950166112956, "success_rate_at_3": 0.7275747508305648, "success_rate_at_5": 0.7840531561461794, "success_rate_at_k": 0.7873754152823921, "precision_at_1": 0.5514950166112956, "precision_at_3": 0.28460686600221463, "precision_at_5": 0.2353820598006651, "precision_at_k": 0.008859357696566997, "mrr_at_k": 0.6458867267837367, "ndcg_at_k": 0.04179198960947127, "f1_score": 0.5514950166112956, "avg_query_time_ms": 3173.9714993194884, "max_query_time_ms": 81512.86387443542, "min_query_time_ms": 1163.3169651031494}, "content_matching": {"total_queries": 301, "exact_match_count": 210, "exact_match_rate": 0.6976744186046512, "avg_k": 5.591362126245847, "success_rate_at_1": 0.3853820598006645, "success_rate_at_3": 0.5813953488372093, "success_rate_at_5": 0.6677740863787376, "success_rate_at_k": 0.6777408637873754, "precision_at_1": 0.3853820598006645, "precision_at_3": 0.22369878183831693, "precision_at_5": 0.19396456256921407, "precision_at_k": 0.008859357696566997, "mrr_at_k": 0.4948214944892685, "ndcg_at_k": 0.03823942003496427, "f1_score": 0.3853820598006645, "avg_query_time_ms": 3173.9714993194884, "max_query_time_ms": 81512.86387443542, "min_query_time_ms": 1163.3169651031494}, "total_queries": 301, "exact_match_count": 242, "exact_match_rate": 0.8039867109634552, "avg_k": 5.591362126245847, "success_rate_at_1": 0.5514950166112956, "success_rate_at_3": 0.7275747508305648, "success_rate_at_5": 0.7840531561461794, "success_rate_at_k": 0.7873754152823921, "precision_at_1": 0.5514950166112956, "precision_at_3": 0.28460686600221463, "precision_at_5": 0.2353820598006651, "precision_at_k": 0.008859357696566997, "mrr_at_k": 0.6458867267837367, "ndcg_at_k": 0.04179198960947127, "f1_score": 0.5514950166112956, "avg_query_time_ms": 3173.9714993194884, "max_query_time_ms": 81512.86387443542, "min_query_time_ms": 1163.3169651031494}, "interview-guide": {"path_matching": {"total_queries": 226, "exact_match_count": 206, "exact_match_rate": 0.911504424778761, "avg_k": 5.079646017699115, "success_rate_at_1": 0.5752212389380531, "success_rate_at_3": 0.8141592920353983, "success_rate_at_5": 0.8628318584070797, "success_rate_at_k": 0.8628318584070797, "precision_at_1": 0.5752212389380531, "precision_at_3": 0.357669616519174, "precision_at_5": 0.3065634218289092, "precision_at_k": 0.3065634218289092, "mrr_at_k": 0.7076555696024721, "ndcg_at_k": 0.7417200865069804, "f1_score": 0.5752212389380531, "avg_query_time_ms": 2157.************, "max_query_time_ms": 38333.45532417297, "min_query_time_ms": 1158.6670875549316}, "content_matching": {"total_queries": 226, "exact_match_count": 199, "exact_match_rate": 0.8805309734513275, "avg_k": 5.079646017699115, "success_rate_at_1": 0.5265486725663717, "success_rate_at_3": 0.7477876106194691, "success_rate_at_5": 0.8185840707964602, "success_rate_at_k": 0.8185840707964602, "precision_at_1": 0.5265486725663717, "precision_at_3": 0.32227138643067876, "precision_at_5": 0.2806784660766968, "precision_at_k": 0.2806784660766968, "mrr_at_k": 0.6561139204944513, "ndcg_at_k": 0.6900538874766897, "f1_score": 0.5265486725663717, "avg_query_time_ms": 2157.************, "max_query_time_ms": 38333.45532417297, "min_query_time_ms": 1158.6670875549316}, "total_queries": 226, "exact_match_count": 206, "exact_match_rate": 0.911504424778761, "avg_k": 5.079646017699115, "success_rate_at_1": 0.5752212389380531, "success_rate_at_3": 0.8141592920353983, "success_rate_at_5": 0.8628318584070797, "success_rate_at_k": 0.8628318584070797, "precision_at_1": 0.5752212389380531, "precision_at_3": 0.357669616519174, "precision_at_5": 0.3065634218289092, "precision_at_k": 0.3065634218289092, "mrr_at_k": 0.7076555696024721, "ndcg_at_k": 0.7417200865069804, "f1_score": 0.5752212389380531, "avg_query_time_ms": 2157.************, "max_query_time_ms": 38333.45532417297, "min_query_time_ms": 1158.6670875549316}, "Wechat_ArkTs": {"path_matching": {"total_queries": 152, "exact_match_count": 118, "exact_match_rate": 0.7763157894736842, "avg_k": 3.776315789473684, "success_rate_at_1": 0.506578947368421, "success_rate_at_3": 0.7171052631578947, "success_rate_at_5": 0.75, "success_rate_at_k": 0.743421052631579, "precision_at_1": 0.506578947368421, "precision_at_3": 0.354166666666667, "precision_at_5": 0.3260964912280705, "precision_at_k": 0.03289473684210526, "mrr_at_k": 0.6158208020050125, "ndcg_at_k": 0.09878948607897417, "f1_score": 0.506578947368421, "avg_query_time_ms": 1872.917170587339, "max_query_time_ms": 8172.535181045532, "min_query_time_ms": 1220.5281257629395}, "content_matching": {"total_queries": 152, "exact_match_count": 118, "exact_match_rate": 0.7763157894736842, "avg_k": 3.776315789473684, "success_rate_at_1": 0.4868421052631579, "success_rate_at_3": 0.7302631578947368, "success_rate_at_5": 0.756578947368421, "success_rate_at_k": 0.756578947368421, "precision_at_1": 0.4868421052631579, "precision_at_3": 0.35855263157894773, "precision_at_5": 0.3274122807017547, "precision_at_k": 0.03289473684210526, "mrr_at_k": 0.6080827067669172, "ndcg_at_k": 0.09636139235247061, "f1_score": 0.4868421052631579, "avg_query_time_ms": 1872.917170587339, "max_query_time_ms": 8172.535181045532, "min_query_time_ms": 1220.5281257629395}, "total_queries": 152, "exact_match_count": 118, "exact_match_rate": 0.7763157894736842, "avg_k": 3.776315789473684, "success_rate_at_1": 0.506578947368421, "success_rate_at_3": 0.7171052631578947, "success_rate_at_5": 0.75, "success_rate_at_k": 0.743421052631579, "precision_at_1": 0.506578947368421, "precision_at_3": 0.354166666666667, "precision_at_5": 0.3260964912280705, "precision_at_k": 0.03289473684210526, "mrr_at_k": 0.6158208020050125, "ndcg_at_k": 0.09878948607897417, "f1_score": 0.506578947368421, "avg_query_time_ms": 1872.917170587339, "max_query_time_ms": 8172.535181045532, "min_query_time_ms": 1220.5281257629395}, "Chatime": {"path_matching": {"total_queries": 147, "exact_match_count": 130, "exact_match_rate": 0.8843537414965986, "avg_k": 4.925170068027211, "success_rate_at_1": 0.5578231292517006, "success_rate_at_3": 0.7755102040816326, "success_rate_at_5": 0.8163265306122449, "success_rate_at_k": 0.8163265306122449, "precision_at_1": 0.5578231292517006, "precision_at_3": 0.33786848072562414, "precision_at_5": 0.29875283446712053, "precision_at_k": 0.29875283446712053, "mrr_at_k": 0.6800237555339597, "ndcg_at_k": 0.7068067036708877, "f1_score": 0.5578231292517006, "avg_query_time_ms": 1876.8866727141296, "max_query_time_ms": 8407.39107131958, "min_query_time_ms": 1290.815830230713}, "content_matching": {"total_queries": 147, "exact_match_count": 111, "exact_match_rate": 0.7551020408163265, "avg_k": 4.925170068027211, "success_rate_at_1": 0.4421768707482993, "success_rate_at_3": 0.6802721088435374, "success_rate_at_5": 0.7074829931972789, "success_rate_at_k": 0.7074829931972789, "precision_at_1": 0.4421768707482993, "precision_at_3": 0.3027210884353745, "precision_at_5": 0.2678004535147395, "precision_at_k": 0.2678004535147395, "mrr_at_k": 0.5639725731562466, "ndcg_at_k": 0.5955049817923381, "f1_score": 0.4421768707482993, "avg_query_time_ms": 1876.8866727141296, "max_query_time_ms": 8407.39107131958, "min_query_time_ms": 1290.815830230713}, "total_queries": 147, "exact_match_count": 130, "exact_match_rate": 0.8843537414965986, "avg_k": 4.925170068027211, "success_rate_at_1": 0.5578231292517006, "success_rate_at_3": 0.7755102040816326, "success_rate_at_5": 0.8163265306122449, "success_rate_at_k": 0.8163265306122449, "precision_at_1": 0.5578231292517006, "precision_at_3": 0.33786848072562414, "precision_at_5": 0.29875283446712053, "precision_at_k": 0.29875283446712053, "mrr_at_k": 0.6800237555339597, "ndcg_at_k": 0.7068067036708877, "f1_score": 0.5578231292517006, "avg_query_time_ms": 1876.8866727141296, "max_query_time_ms": 8407.39107131958, "min_query_time_ms": 1290.815830230713}, "oh-bill": {"path_matching": {"total_queries": 17, "exact_match_count": 11, "exact_match_rate": 0.6470588235294118, "avg_k": 2.2941176470588234, "success_rate_at_1": 0.5882352941176471, "success_rate_at_3": 0.6470588235294118, "success_rate_at_5": 0.6470588235294118, "success_rate_at_k": 0.5882352941176471, "precision_at_1": 0.5882352941176471, "precision_at_3": 0.4509803921568627, "precision_at_5": 0.4509803921568627, "precision_at_k": 0.11764705882352941, "mrr_at_k": 0.607843137254902, "ndcg_at_k": 0.23529411764705882, "f1_score": 0.5882352941176471, "avg_query_time_ms": 1830.8824230642879, "max_query_time_ms": 3329.26607131958, "min_query_time_ms": 1225.1720428466797}, "content_matching": {"total_queries": 17, "exact_match_count": 11, "exact_match_rate": 0.6470588235294118, "avg_k": 2.2941176470588234, "success_rate_at_1": 0.5882352941176471, "success_rate_at_3": 0.6470588235294118, "success_rate_at_5": 0.6470588235294118, "success_rate_at_k": 0.5882352941176471, "precision_at_1": 0.5882352941176471, "precision_at_3": 0.4509803921568627, "precision_at_5": 0.4509803921568627, "precision_at_k": 0.11764705882352941, "mrr_at_k": 0.607843137254902, "ndcg_at_k": 0.23529411764705882, "f1_score": 0.5882352941176471, "avg_query_time_ms": 1830.8824230642879, "max_query_time_ms": 3329.26607131958, "min_query_time_ms": 1225.1720428466797}, "total_queries": 17, "exact_match_count": 11, "exact_match_rate": 0.6470588235294118, "avg_k": 2.2941176470588234, "success_rate_at_1": 0.5882352941176471, "success_rate_at_3": 0.6470588235294118, "success_rate_at_5": 0.6470588235294118, "success_rate_at_k": 0.5882352941176471, "precision_at_1": 0.5882352941176471, "precision_at_3": 0.4509803921568627, "precision_at_5": 0.4509803921568627, "precision_at_k": 0.11764705882352941, "mrr_at_k": 0.607843137254902, "ndcg_at_k": 0.23529411764705882, "f1_score": 0.5882352941176471, "avg_query_time_ms": 1830.8824230642879, "max_query_time_ms": 3329.26607131958, "min_query_time_ms": 1225.1720428466797}, "music-gathering": {"path_matching": {"total_queries": 18, "exact_match_count": 14, "exact_match_rate": 0.7777777777777778, "avg_k": 5.055555555555555, "success_rate_at_1": 0.3333333333333333, "success_rate_at_3": 0.5555555555555556, "success_rate_at_5": 0.6666666666666666, "success_rate_at_k": 0.6666666666666666, "precision_at_1": 0.3333333333333333, "precision_at_3": 0.34259259259259256, "precision_at_5": 0.3305555555555556, "precision_at_k": 0.3305555555555556, "mrr_at_k": 0.4773809523809524, "ndcg_at_k": 0.5116843681123505, "f1_score": 0.3333333333333333, "avg_query_time_ms": 1808.289713329739, "max_query_time_ms": 2834.0370655059814, "min_query_time_ms": 1226.1531352996826}, "content_matching": {"total_queries": 18, "exact_match_count": 14, "exact_match_rate": 0.7777777777777778, "avg_k": 5.055555555555555, "success_rate_at_1": 0.3333333333333333, "success_rate_at_3": 0.5555555555555556, "success_rate_at_5": 0.6666666666666666, "success_rate_at_k": 0.6666666666666666, "precision_at_1": 0.3333333333333333, "precision_at_3": 0.34259259259259256, "precision_at_5": 0.3305555555555556, "precision_at_k": 0.3305555555555556, "mrr_at_k": 0.4773809523809524, "ndcg_at_k": 0.5116843681123505, "f1_score": 0.3333333333333333, "avg_query_time_ms": 1808.289713329739, "max_query_time_ms": 2834.0370655059814, "min_query_time_ms": 1226.1531352996826}, "total_queries": 18, "exact_match_count": 14, "exact_match_rate": 0.7777777777777778, "avg_k": 5.055555555555555, "success_rate_at_1": 0.3333333333333333, "success_rate_at_3": 0.5555555555555556, "success_rate_at_5": 0.6666666666666666, "success_rate_at_k": 0.6666666666666666, "precision_at_1": 0.3333333333333333, "precision_at_3": 0.34259259259259256, "precision_at_5": 0.3305555555555556, "precision_at_k": 0.3305555555555556, "mrr_at_k": 0.4773809523809524, "ndcg_at_k": 0.5116843681123505, "f1_score": 0.3333333333333333, "avg_query_time_ms": 1808.289713329739, "max_query_time_ms": 2834.0370655059814, "min_query_time_ms": 1226.1531352996826}}, "intent_metrics_per_repo": {"legado-Harmony": {"debugging": {"path_matching": {"total_queries": 105, "exact_match_count": 82, "exact_match_rate": 0.780952380952381, "avg_k": 7.20952380952381, "success_rate_at_1": 0.5047619047619047, "success_rate_at_3": 0.6857142857142857, "success_rate_at_5": 0.7333333333333333, "success_rate_at_k": 0.7714285714285715, "precision_at_1": 0.5047619047619047, "precision_at_3": 0.24761904761904738, "precision_at_5": 0.18047619047619026, "precision_at_k": 0.010884353741496596, "mrr_at_k": 0.6042554799697658, "ndcg_at_k": 0.06302920661191369, "f1_score": 0.5047619047619047, "avg_query_time_ms": 2909.874150866554, "max_query_time_ms": 15054.535865783691, "min_query_time_ms": 1209.8898887634277}, "content_matching": {"total_queries": 105, "exact_match_count": 82, "exact_match_rate": 0.780952380952381, "avg_k": 7.20952380952381, "success_rate_at_1": 0.5047619047619047, "success_rate_at_3": 0.6761904761904762, "success_rate_at_5": 0.7523809523809524, "success_rate_at_k": 0.7714285714285715, "precision_at_1": 0.5047619047619047, "precision_at_3": 0.24444444444444421, "precision_at_5": 0.1838095238095236, "precision_at_k": 0.009523809523809521, "mrr_at_k": 0.6047996976568406, "ndcg_at_k": 0.05379726013692816, "f1_score": 0.5047619047619047, "avg_query_time_ms": 2909.874150866554, "max_query_time_ms": 15054.535865783691, "min_query_time_ms": 1209.8898887634277}, "total_queries": 105, "exact_match_count": 82, "exact_match_rate": 0.780952380952381, "avg_k": 7.20952380952381, "success_rate_at_1": 0.5047619047619047, "success_rate_at_3": 0.6857142857142857, "success_rate_at_5": 0.7333333333333333, "success_rate_at_k": 0.7714285714285715, "precision_at_1": 0.5047619047619047, "precision_at_3": 0.24761904761904738, "precision_at_5": 0.18047619047619026, "precision_at_k": 0.010884353741496596, "mrr_at_k": 0.6042554799697658, "ndcg_at_k": 0.06302920661191369, "f1_score": 0.5047619047619047, "avg_query_time_ms": 2909.874150866554, "max_query_time_ms": 15054.535865783691, "min_query_time_ms": 1209.8898887634277}, "understanding": {"path_matching": {"total_queries": 104, "exact_match_count": 84, "exact_match_rate": 0.8076923076923077, "avg_k": 6.644230769230769, "success_rate_at_1": 0.5480769230769231, "success_rate_at_3": 0.7115384615384616, "success_rate_at_5": 0.7788461538461539, "success_rate_at_k": 0.7980769230769231, "precision_at_1": 0.5480769230769231, "precision_at_3": 0.2532051282051281, "precision_at_5": 0.1945512820512819, "precision_at_k": 0.012362637362637359, "mrr_at_k": 0.6465125152625153, "ndcg_at_k": 0.08298970916895633, "f1_score": 0.5480769230769231, "avg_query_time_ms": 2822.3609993090995, "max_query_time_ms": 17061.41495704651, "min_query_time_ms": 1506.6909790039062}, "content_matching": {"total_queries": 104, "exact_match_count": 82, "exact_match_rate": 0.7884615384615384, "avg_k": 6.644230769230769, "success_rate_at_1": 0.5096153846153846, "success_rate_at_3": 0.7019230769230769, "success_rate_at_5": 0.7596153846153846, "success_rate_at_k": 0.7692307692307693, "precision_at_1": 0.5096153846153846, "precision_at_3": 0.24358974358974345, "precision_at_5": 0.18429487179487164, "precision_at_k": 0.009615384615384612, "mrr_at_k": 0.616693376068376, "ndcg_at_k": 0.0637589399381871, "f1_score": 0.5096153846153846, "avg_query_time_ms": 2822.3609993090995, "max_query_time_ms": 17061.41495704651, "min_query_time_ms": 1506.6909790039062}, "total_queries": 104, "exact_match_count": 84, "exact_match_rate": 0.8076923076923077, "avg_k": 6.644230769230769, "success_rate_at_1": 0.5480769230769231, "success_rate_at_3": 0.7115384615384616, "success_rate_at_5": 0.7788461538461539, "success_rate_at_k": 0.7980769230769231, "precision_at_1": 0.5480769230769231, "precision_at_3": 0.2532051282051281, "precision_at_5": 0.1945512820512819, "precision_at_k": 0.012362637362637359, "mrr_at_k": 0.6465125152625153, "ndcg_at_k": 0.08298970916895633, "f1_score": 0.5480769230769231, "avg_query_time_ms": 2822.3609993090995, "max_query_time_ms": 17061.41495704651, "min_query_time_ms": 1506.6909790039062}, "optimization": {"path_matching": {"total_queries": 104, "exact_match_count": 56, "exact_match_rate": 0.5384615384615384, "avg_k": 7.019230769230769, "success_rate_at_1": 0.22115384615384615, "success_rate_at_3": 0.38461538461538464, "success_rate_at_5": 0.46153846153846156, "success_rate_at_k": 0.5192307692307693, "precision_at_1": 0.22115384615384615, "precision_at_3": 0.13782051282051289, "precision_at_5": 0.11025641025641021, "precision_at_k": 0.009615384615384612, "mrr_at_k": 0.31981456043956047, "ndcg_at_k": 0.040969639953351566, "f1_score": 0.22115384615384615, "avg_query_time_ms": 3290.993238870914, "max_query_time_ms": 10681.198120117188, "min_query_time_ms": 1599.5569229125977}, "content_matching": {"total_queries": 104, "exact_match_count": 61, "exact_match_rate": 0.5865384615384616, "avg_k": 7.019230769230769, "success_rate_at_1": 0.21153846153846154, "success_rate_at_3": 0.3942307692307692, "success_rate_at_5": 0.4807692307692308, "success_rate_at_k": 0.5673076923076923, "precision_at_1": 0.21153846153846154, "precision_at_3": 0.1410256410256411, "precision_at_5": 0.11410256410256404, "precision_at_k": 0.013736263736263733, "mrr_at_k": 0.3233630952380952, "ndcg_at_k": 0.053123581216877776, "f1_score": 0.21153846153846154, "avg_query_time_ms": 3290.993238870914, "max_query_time_ms": 10681.198120117188, "min_query_time_ms": 1599.5569229125977}, "total_queries": 104, "exact_match_count": 56, "exact_match_rate": 0.5384615384615384, "avg_k": 7.019230769230769, "success_rate_at_1": 0.22115384615384615, "success_rate_at_3": 0.38461538461538464, "success_rate_at_5": 0.46153846153846156, "success_rate_at_k": 0.5192307692307693, "precision_at_1": 0.22115384615384615, "precision_at_3": 0.13782051282051289, "precision_at_5": 0.11025641025641021, "precision_at_k": 0.009615384615384612, "mrr_at_k": 0.31981456043956047, "ndcg_at_k": 0.040969639953351566, "f1_score": 0.22115384615384615, "avg_query_time_ms": 3290.993238870914, "max_query_time_ms": 10681.198120117188, "min_query_time_ms": 1599.5569229125977}, "implementation": {"path_matching": {"total_queries": 103, "exact_match_count": 69, "exact_match_rate": 0.6699029126213593, "avg_k": 8.12621359223301, "success_rate_at_1": 0.2912621359223301, "success_rate_at_3": 0.46601941747572817, "success_rate_at_5": 0.5631067961165048, "success_rate_at_k": 0.6310679611650486, "precision_at_1": 0.2912621359223301, "precision_at_3": 0.16019417475728162, "precision_at_5": 0.12362459546925558, "precision_at_k": 0.007281553398058253, "mrr_at_k": 0.3982008013561412, "ndcg_at_k": 0.03790908124311205, "f1_score": 0.2912621359223301, "avg_query_time_ms": 2528.1611414788995, "max_query_time_ms": 9586.543798446655, "min_query_time_ms": 1440.997838973999}, "content_matching": {"total_queries": 103, "exact_match_count": 69, "exact_match_rate": 0.6699029126213593, "avg_k": 8.12621359223301, "success_rate_at_1": 0.27184466019417475, "success_rate_at_3": 0.44660194174757284, "success_rate_at_5": 0.5436893203883495, "success_rate_at_k": 0.6407766990291263, "precision_at_1": 0.27184466019417475, "precision_at_3": 0.15533980582524282, "precision_at_5": 0.12135922330097079, "precision_at_k": 0.007281553398058253, "mrr_at_k": 0.37933425797503467, "ndcg_at_k": 0.03790908124311205, "f1_score": 0.27184466019417475, "avg_query_time_ms": 2528.1611414788995, "max_query_time_ms": 9586.543798446655, "min_query_time_ms": 1440.997838973999}, "total_queries": 103, "exact_match_count": 69, "exact_match_rate": 0.6699029126213593, "avg_k": 8.12621359223301, "success_rate_at_1": 0.2912621359223301, "success_rate_at_3": 0.46601941747572817, "success_rate_at_5": 0.5631067961165048, "success_rate_at_k": 0.6310679611650486, "precision_at_1": 0.2912621359223301, "precision_at_3": 0.16019417475728162, "precision_at_5": 0.12362459546925558, "precision_at_k": 0.007281553398058253, "mrr_at_k": 0.3982008013561412, "ndcg_at_k": 0.03790908124311205, "f1_score": 0.2912621359223301, "avg_query_time_ms": 2528.1611414788995, "max_query_time_ms": 9586.543798446655, "min_query_time_ms": 1440.997838973999}, "testing": {"path_matching": {"total_queries": 96, "exact_match_count": 48, "exact_match_rate": 0.5, "avg_k": 4.854166666666667, "success_rate_at_1": 0.2604166666666667, "success_rate_at_3": 0.3958333333333333, "success_rate_at_5": 0.4583333333333333, "success_rate_at_k": 0.4583333333333333, "precision_at_1": 0.2604166666666667, "precision_at_3": 0.1493055555555556, "precision_at_5": 0.12743055555555552, "precision_at_k": 0.12743055555555552, "mrr_at_k": 0.34175347222222224, "ndcg_at_k": 0.3659474570631971, "f1_score": 0.2604166666666667, "avg_query_time_ms": 2909.081200758616, "max_query_time_ms": 10450.469255447388, "min_query_time_ms": 1346.4739322662354}, "content_matching": {"total_queries": 96, "exact_match_count": 48, "exact_match_rate": 0.5, "avg_k": 4.854166666666667, "success_rate_at_1": 0.2708333333333333, "success_rate_at_3": 0.3958333333333333, "success_rate_at_5": 0.4583333333333333, "success_rate_at_k": 0.4583333333333333, "precision_at_1": 0.2708333333333333, "precision_at_3": 0.1493055555555556, "precision_at_5": 0.12795138888888885, "precision_at_k": 0.12795138888888885, "mrr_at_k": 0.34869791666666666, "ndcg_at_k": 0.3711557903965304, "f1_score": 0.2708333333333333, "avg_query_time_ms": 2909.081200758616, "max_query_time_ms": 10450.469255447388, "min_query_time_ms": 1346.4739322662354}, "total_queries": 96, "exact_match_count": 48, "exact_match_rate": 0.5, "avg_k": 4.854166666666667, "success_rate_at_1": 0.2604166666666667, "success_rate_at_3": 0.3958333333333333, "success_rate_at_5": 0.4583333333333333, "success_rate_at_k": 0.4583333333333333, "precision_at_1": 0.2604166666666667, "precision_at_3": 0.1493055555555556, "precision_at_5": 0.12743055555555552, "precision_at_k": 0.12743055555555552, "mrr_at_k": 0.34175347222222224, "ndcg_at_k": 0.3659474570631971, "f1_score": 0.2604166666666667, "avg_query_time_ms": 2909.081200758616, "max_query_time_ms": 10450.469255447388, "min_query_time_ms": 1346.4739322662354}, "integration": {"path_matching": {"total_queries": 87, "exact_match_count": 48, "exact_match_rate": 0.5517241379310345, "avg_k": 6.563218390804598, "success_rate_at_1": 0.26436781609195403, "success_rate_at_3": 0.41379310344827586, "success_rate_at_5": 0.4827586206896552, "success_rate_at_k": 0.4942528735632184, "precision_at_1": 0.26436781609195403, "precision_at_3": 0.1398467432950192, "precision_at_5": 0.1078544061302682, "precision_at_k": 0.0049261083743842365, "mrr_at_k": 0.3532247765006385, "ndcg_at_k": 0.028735632183908046, "f1_score": 0.26436781609195403, "avg_query_time_ms": 2459.757528085818, "max_query_time_ms": 5476.258039474487, "min_query_time_ms": 1403.0389785766602}, "content_matching": {"total_queries": 87, "exact_match_count": 45, "exact_match_rate": 0.5172413793103449, "avg_k": 6.563218390804598, "success_rate_at_1": 0.25287356321839083, "success_rate_at_3": 0.3793103448275862, "success_rate_at_5": 0.4482758620689655, "success_rate_at_k": 0.47126436781609193, "precision_at_1": 0.25287356321839083, "precision_at_3": 0.12835249042145597, "precision_at_5": 0.10095785440613027, "precision_at_k": 0.003284072249589491, "mrr_at_k": 0.33540868454661554, "ndcg_at_k": 0.016444558138774632, "f1_score": 0.25287356321839083, "avg_query_time_ms": 2459.757528085818, "max_query_time_ms": 5476.258039474487, "min_query_time_ms": 1403.0389785766602}, "total_queries": 87, "exact_match_count": 48, "exact_match_rate": 0.5517241379310345, "avg_k": 6.563218390804598, "success_rate_at_1": 0.26436781609195403, "success_rate_at_3": 0.41379310344827586, "success_rate_at_5": 0.4827586206896552, "success_rate_at_k": 0.4942528735632184, "precision_at_1": 0.26436781609195403, "precision_at_3": 0.1398467432950192, "precision_at_5": 0.1078544061302682, "precision_at_k": 0.0049261083743842365, "mrr_at_k": 0.3532247765006385, "ndcg_at_k": 0.028735632183908046, "f1_score": 0.26436781609195403, "avg_query_time_ms": 2459.757528085818, "max_query_time_ms": 5476.258039474487, "min_query_time_ms": 1403.0389785766602}}, "HOme_App": {"debugging": {"path_matching": {"total_queries": 37, "exact_match_count": 32, "exact_match_rate": 0.8648648648648649, "avg_k": 6.162162162162162, "success_rate_at_1": 0.4864864864864865, "success_rate_at_3": 0.7027027027027027, "success_rate_at_5": 0.8108108108108109, "success_rate_at_k": 0.8378378378378378, "precision_at_1": 0.4864864864864865, "precision_at_3": 0.2792792792792793, "precision_at_5": 0.2414414414414414, "precision_at_k": 0.009009009009009009, "mrr_at_k": 0.6151222651222652, "ndcg_at_k": 0.025153420488470083, "f1_score": 0.4864864864864865, "avg_query_time_ms": 2193.3193851161645, "max_query_time_ms": 5969.582080841064, "min_query_time_ms": 1262.882947921753}, "content_matching": {"total_queries": 37, "exact_match_count": 32, "exact_match_rate": 0.8648648648648649, "avg_k": 6.162162162162162, "success_rate_at_1": 0.5945945945945946, "success_rate_at_3": 0.8108108108108109, "success_rate_at_5": 0.8648648648648649, "success_rate_at_k": 0.8648648648648649, "precision_at_1": 0.5945945945945946, "precision_at_3": 0.31531531531531537, "precision_at_5": 0.25225225225225223, "precision_at_k": 0.009009009009009009, "mrr_at_k": 0.7027027027027027, "ndcg_at_k": 0.025153420488470083, "f1_score": 0.5945945945945946, "avg_query_time_ms": 2193.3193851161645, "max_query_time_ms": 5969.582080841064, "min_query_time_ms": 1262.882947921753}, "total_queries": 37, "exact_match_count": 32, "exact_match_rate": 0.8648648648648649, "avg_k": 6.162162162162162, "success_rate_at_1": 0.4864864864864865, "success_rate_at_3": 0.7027027027027027, "success_rate_at_5": 0.8108108108108109, "success_rate_at_k": 0.8378378378378378, "precision_at_1": 0.4864864864864865, "precision_at_3": 0.2792792792792793, "precision_at_5": 0.2414414414414414, "precision_at_k": 0.009009009009009009, "mrr_at_k": 0.6151222651222652, "ndcg_at_k": 0.025153420488470083, "f1_score": 0.4864864864864865, "avg_query_time_ms": 2193.3193851161645, "max_query_time_ms": 5969.582080841064, "min_query_time_ms": 1262.882947921753}, "understanding": {"path_matching": {"total_queries": 37, "exact_match_count": 31, "exact_match_rate": 0.8378378378378378, "avg_k": 6.027027027027027, "success_rate_at_1": 0.4594594594594595, "success_rate_at_3": 0.6486486486486487, "success_rate_at_5": 0.7297297297297297, "success_rate_at_k": 0.7297297297297297, "precision_at_1": 0.4594594594594595, "precision_at_3": 0.31531531531531537, "precision_at_5": 0.2932432432432432, "precision_at_k": 0.009009009009009009, "mrr_at_k": 0.577091377091377, "ndcg_at_k": 0.023968994790122745, "f1_score": 0.4594594594594595, "avg_query_time_ms": 2039.649892497707, "max_query_time_ms": 4032.521963119507, "min_query_time_ms": 1335.9360694885254}, "content_matching": {"total_queries": 37, "exact_match_count": 32, "exact_match_rate": 0.8648648648648649, "avg_k": 6.027027027027027, "success_rate_at_1": 0.43243243243243246, "success_rate_at_3": 0.7027027027027027, "success_rate_at_5": 0.7837837837837838, "success_rate_at_k": 0.7837837837837838, "precision_at_1": 0.43243243243243246, "precision_at_3": 0.33333333333333337, "precision_at_5": 0.30405405405405395, "precision_at_k": 0.009009009009009009, "mrr_at_k": 0.581081081081081, "ndcg_at_k": 0.023968994790122745, "f1_score": 0.43243243243243246, "avg_query_time_ms": 2039.649892497707, "max_query_time_ms": 4032.521963119507, "min_query_time_ms": 1335.9360694885254}, "total_queries": 37, "exact_match_count": 31, "exact_match_rate": 0.8378378378378378, "avg_k": 6.027027027027027, "success_rate_at_1": 0.4594594594594595, "success_rate_at_3": 0.6486486486486487, "success_rate_at_5": 0.7297297297297297, "success_rate_at_k": 0.7297297297297297, "precision_at_1": 0.4594594594594595, "precision_at_3": 0.31531531531531537, "precision_at_5": 0.2932432432432432, "precision_at_k": 0.009009009009009009, "mrr_at_k": 0.577091377091377, "ndcg_at_k": 0.023968994790122745, "f1_score": 0.4594594594594595, "avg_query_time_ms": 2039.649892497707, "max_query_time_ms": 4032.521963119507, "min_query_time_ms": 1335.9360694885254}, "implementation": {"path_matching": {"total_queries": 34, "exact_match_count": 26, "exact_match_rate": 0.7647058823529411, "avg_k": 6.852941176470588, "success_rate_at_1": 0.35294117647058826, "success_rate_at_3": 0.5294117647058824, "success_rate_at_5": 0.6176470588235294, "success_rate_at_k": 0.6470588235294118, "precision_at_1": 0.35294117647058826, "precision_at_3": 0.17647058823529407, "precision_at_5": 0.1333333333333334, "precision_at_k": 0.008403361344537815, "mrr_at_k": 0.4610294117647059, "ndcg_at_k": 0.03122371504837796, "f1_score": 0.35294117647058826, "avg_query_time_ms": 1931.61080865299, "max_query_time_ms": 3281.06689453125, "min_query_time_ms": 1288.1698608398438}, "content_matching": {"total_queries": 34, "exact_match_count": 26, "exact_match_rate": 0.7647058823529411, "avg_k": 6.852941176470588, "success_rate_at_1": 0.4117647058823529, "success_rate_at_3": 0.5882352941176471, "success_rate_at_5": 0.6176470588235294, "success_rate_at_k": 0.6764705882352942, "precision_at_1": 0.4117647058823529, "precision_at_3": 0.19607843137254896, "precision_at_5": 0.1333333333333334, "precision_at_k": 0.004201680672268907, "mrr_at_k": 0.5127450980392156, "ndcg_at_k": 0.018556757457984047, "f1_score": 0.4117647058823529, "avg_query_time_ms": 1931.61080865299, "max_query_time_ms": 3281.06689453125, "min_query_time_ms": 1288.1698608398438}, "total_queries": 34, "exact_match_count": 26, "exact_match_rate": 0.7647058823529411, "avg_k": 6.852941176470588, "success_rate_at_1": 0.35294117647058826, "success_rate_at_3": 0.5294117647058824, "success_rate_at_5": 0.6176470588235294, "success_rate_at_k": 0.6470588235294118, "precision_at_1": 0.35294117647058826, "precision_at_3": 0.17647058823529407, "precision_at_5": 0.1333333333333334, "precision_at_k": 0.008403361344537815, "mrr_at_k": 0.4610294117647059, "ndcg_at_k": 0.03122371504837796, "f1_score": 0.35294117647058826, "avg_query_time_ms": 1931.61080865299, "max_query_time_ms": 3281.06689453125, "min_query_time_ms": 1288.1698608398438}, "optimization": {"path_matching": {"total_queries": 37, "exact_match_count": 25, "exact_match_rate": 0.6756756756756757, "avg_k": 5.054054054054054, "success_rate_at_1": 0.4594594594594595, "success_rate_at_3": 0.5405405405405406, "success_rate_at_5": 0.5945945945945946, "success_rate_at_k": 0.5945945945945946, "precision_at_1": 0.4594594594594595, "precision_at_3": 0.18918918918918912, "precision_at_5": 0.1481981981981982, "precision_at_k": 0.1481981981981982, "mrr_at_k": 0.5107250107250108, "ndcg_at_k": 0.5232798139499132, "f1_score": 0.4594594594594595, "avg_query_time_ms": 2402.8692052171036, "max_query_time_ms": 13975.872993469238, "min_query_time_ms": 1316.2150382995605}, "content_matching": {"total_queries": 37, "exact_match_count": 25, "exact_match_rate": 0.6756756756756757, "avg_k": 5.054054054054054, "success_rate_at_1": 0.4864864864864865, "success_rate_at_3": 0.5945945945945946, "success_rate_at_5": 0.6486486486486487, "success_rate_at_k": 0.6486486486486487, "precision_at_1": 0.4864864864864865, "precision_at_3": 0.20720720720720712, "precision_at_5": 0.15900900900900905, "precision_at_k": 0.15900900900900905, "mrr_at_k": 0.5435435435435435, "ndcg_at_k": 0.5673589964788714, "f1_score": 0.4864864864864865, "avg_query_time_ms": 2402.8692052171036, "max_query_time_ms": 13975.872993469238, "min_query_time_ms": 1316.2150382995605}, "total_queries": 37, "exact_match_count": 25, "exact_match_rate": 0.6756756756756757, "avg_k": 5.054054054054054, "success_rate_at_1": 0.4594594594594595, "success_rate_at_3": 0.5405405405405406, "success_rate_at_5": 0.5945945945945946, "success_rate_at_k": 0.5945945945945946, "precision_at_1": 0.4594594594594595, "precision_at_3": 0.18918918918918912, "precision_at_5": 0.1481981981981982, "precision_at_k": 0.1481981981981982, "mrr_at_k": 0.5107250107250108, "ndcg_at_k": 0.5232798139499132, "f1_score": 0.4594594594594595, "avg_query_time_ms": 2402.8692052171036, "max_query_time_ms": 13975.872993469238, "min_query_time_ms": 1316.2150382995605}, "integration": {"path_matching": {"total_queries": 27, "exact_match_count": 21, "exact_match_rate": 0.7777777777777778, "avg_k": 5.0, "success_rate_at_1": 0.2962962962962963, "success_rate_at_3": 0.6296296296296297, "success_rate_at_5": 0.7407407407407407, "success_rate_at_k": 0.7407407407407407, "precision_at_1": 0.2962962962962963, "precision_at_3": 0.2716049382716048, "precision_at_5": 0.2518518518518519, "precision_at_k": 0.2518518518518519, "mrr_at_k": 0.48919753086419754, "ndcg_at_k": 0.5496099149182163, "f1_score": 0.2962962962962963, "avg_query_time_ms": 1988.7145272007695, "max_query_time_ms": 4372.027158737183, "min_query_time_ms": 1213.907241821289}, "content_matching": {"total_queries": 27, "exact_match_count": 17, "exact_match_rate": 0.6296296296296297, "avg_k": 5.0, "success_rate_at_1": 0.2962962962962963, "success_rate_at_3": 0.5185185185185185, "success_rate_at_5": 0.5925925925925926, "success_rate_at_k": 0.5925925925925926, "precision_at_1": 0.2962962962962963, "precision_at_3": 0.20987654320987653, "precision_at_5": 0.18888888888888894, "precision_at_k": 0.18888888888888894, "mrr_at_k": 0.4243827160493827, "ndcg_at_k": 0.46355562533348427, "f1_score": 0.2962962962962963, "avg_query_time_ms": 1988.7145272007695, "max_query_time_ms": 4372.027158737183, "min_query_time_ms": 1213.907241821289}, "total_queries": 27, "exact_match_count": 21, "exact_match_rate": 0.7777777777777778, "avg_k": 5.0, "success_rate_at_1": 0.2962962962962963, "success_rate_at_3": 0.6296296296296297, "success_rate_at_5": 0.7407407407407407, "success_rate_at_k": 0.7407407407407407, "precision_at_1": 0.2962962962962963, "precision_at_3": 0.2716049382716048, "precision_at_5": 0.2518518518518519, "precision_at_k": 0.2518518518518519, "mrr_at_k": 0.48919753086419754, "ndcg_at_k": 0.5496099149182163, "f1_score": 0.2962962962962963, "avg_query_time_ms": 1988.7145272007695, "max_query_time_ms": 4372.027158737183, "min_query_time_ms": 1213.907241821289}, "testing": {"path_matching": {"total_queries": 29, "exact_match_count": 20, "exact_match_rate": 0.6896551724137931, "avg_k": 3.4482758620689653, "success_rate_at_1": 0.3448275862068966, "success_rate_at_3": 0.5517241379310345, "success_rate_at_5": 0.6896551724137931, "success_rate_at_k": 0.5517241379310345, "precision_at_1": 0.3448275862068966, "precision_at_3": 0.20114942528735627, "precision_at_5": 0.19712643678160924, "precision_at_k": 0.20114942528735627, "mrr_at_k": 0.4695402298850575, "ndcg_at_k": 0.4663351384236493, "f1_score": 0.3448275862068966, "avg_query_time_ms": 2794.707561361379, "max_query_time_ms": 16903.612852096558, "min_query_time_ms": 1356.297254562378}, "content_matching": {"total_queries": 29, "exact_match_count": 23, "exact_match_rate": 0.7931034482758621, "avg_k": 3.4482758620689653, "success_rate_at_1": 0.4482758620689655, "success_rate_at_3": 0.6551724137931034, "success_rate_at_5": 0.7931034482758621, "success_rate_at_k": 0.6551724137931034, "precision_at_1": 0.4482758620689655, "precision_at_3": 0.2816091954022988, "precision_at_5": 0.2775862068965517, "precision_at_k": 0.2816091954022988, "mrr_at_k": 0.5729885057471265, "ndcg_at_k": 0.5697834142857183, "f1_score": 0.4482758620689655, "avg_query_time_ms": 2794.707561361379, "max_query_time_ms": 16903.612852096558, "min_query_time_ms": 1356.297254562378}, "total_queries": 29, "exact_match_count": 20, "exact_match_rate": 0.6896551724137931, "avg_k": 3.4482758620689653, "success_rate_at_1": 0.3448275862068966, "success_rate_at_3": 0.5517241379310345, "success_rate_at_5": 0.6896551724137931, "success_rate_at_k": 0.5517241379310345, "precision_at_1": 0.3448275862068966, "precision_at_3": 0.20114942528735627, "precision_at_5": 0.19712643678160924, "precision_at_k": 0.20114942528735627, "mrr_at_k": 0.4695402298850575, "ndcg_at_k": 0.4663351384236493, "f1_score": 0.3448275862068966, "avg_query_time_ms": 2794.707561361379, "max_query_time_ms": 16903.612852096558, "min_query_time_ms": 1356.297254562378}}, "S1-Orange": {"implementation": {"path_matching": {"total_queries": 52, "exact_match_count": 46, "exact_match_rate": 0.8846153846153846, "avg_k": 6.038461538461538, "success_rate_at_1": 0.6538461538461539, "success_rate_at_3": 0.8461538461538461, "success_rate_at_5": 0.8846153846153846, "success_rate_at_k": 0.8846153846153846, "precision_at_1": 0.6538461538461539, "precision_at_3": 0.33012820512820507, "precision_at_5": 0.26474358974358964, "precision_at_k": 0.009615384615384616, "mrr_at_k": 0.7490384615384615, "ndcg_at_k": 0.040979418337912646, "f1_score": 0.6538461538461539, "avg_query_time_ms": 2290.760338306427, "max_query_time_ms": 5456.777095794678, "min_query_time_ms": 1163.3169651031494}, "content_matching": {"total_queries": 52, "exact_match_count": 41, "exact_match_rate": 0.7884615384615384, "avg_k": 6.038461538461538, "success_rate_at_1": 0.4423076923076923, "success_rate_at_3": 0.7115384615384616, "success_rate_at_5": 0.7692307692307693, "success_rate_at_k": 0.7884615384615384, "precision_at_1": 0.4423076923076923, "precision_at_3": 0.2820512820512821, "precision_at_5": 0.23493589743589738, "precision_at_k": 0.009615384615384616, "mrr_at_k": 0.576602564102564, "ndcg_at_k": 0.03388191359890221, "f1_score": 0.4423076923076923, "avg_query_time_ms": 2290.760338306427, "max_query_time_ms": 5456.777095794678, "min_query_time_ms": 1163.3169651031494}, "total_queries": 52, "exact_match_count": 46, "exact_match_rate": 0.8846153846153846, "avg_k": 6.038461538461538, "success_rate_at_1": 0.6538461538461539, "success_rate_at_3": 0.8461538461538461, "success_rate_at_5": 0.8846153846153846, "success_rate_at_k": 0.8846153846153846, "precision_at_1": 0.6538461538461539, "precision_at_3": 0.33012820512820507, "precision_at_5": 0.26474358974358964, "precision_at_k": 0.009615384615384616, "mrr_at_k": 0.7490384615384615, "ndcg_at_k": 0.040979418337912646, "f1_score": 0.6538461538461539, "avg_query_time_ms": 2290.760338306427, "max_query_time_ms": 5456.777095794678, "min_query_time_ms": 1163.3169651031494}, "understanding": {"path_matching": {"total_queries": 53, "exact_match_count": 45, "exact_match_rate": 0.8490566037735849, "avg_k": 4.9245283018867925, "success_rate_at_1": 0.6415094339622641, "success_rate_at_3": 0.7924528301886793, "success_rate_at_5": 0.8301886792452831, "success_rate_at_k": 0.8301886792452831, "precision_at_1": 0.6415094339622641, "precision_at_3": 0.31761006289308186, "precision_at_5": 0.27169811320754705, "precision_at_k": 0.27169811320754705, "mrr_at_k": 0.7155884995507638, "ndcg_at_k": 0.742287705275354, "f1_score": 0.6415094339622641, "avg_query_time_ms": 4356.69235013566, "max_query_time_ms": 81512.86387443542, "min_query_time_ms": 1426.7477989196777}, "content_matching": {"total_queries": 53, "exact_match_count": 38, "exact_match_rate": 0.7169811320754716, "avg_k": 4.9245283018867925, "success_rate_at_1": 0.4716981132075472, "success_rate_at_3": 0.660377358490566, "success_rate_at_5": 0.6981132075471698, "success_rate_at_k": 0.6981132075471698, "precision_at_1": 0.4716981132075472, "precision_at_3": 0.26100628930817615, "precision_at_5": 0.22327044025157228, "precision_at_k": 0.22327044025157228, "mrr_at_k": 0.5615004492362983, "ndcg_at_k": 0.5938146817578344, "f1_score": 0.4716981132075472, "avg_query_time_ms": 4356.69235013566, "max_query_time_ms": 81512.86387443542, "min_query_time_ms": 1426.7477989196777}, "total_queries": 53, "exact_match_count": 45, "exact_match_rate": 0.8490566037735849, "avg_k": 4.9245283018867925, "success_rate_at_1": 0.6415094339622641, "success_rate_at_3": 0.7924528301886793, "success_rate_at_5": 0.8301886792452831, "success_rate_at_k": 0.8301886792452831, "precision_at_1": 0.6415094339622641, "precision_at_3": 0.31761006289308186, "precision_at_5": 0.27169811320754705, "precision_at_k": 0.27169811320754705, "mrr_at_k": 0.7155884995507638, "ndcg_at_k": 0.742287705275354, "f1_score": 0.6415094339622641, "avg_query_time_ms": 4356.69235013566, "max_query_time_ms": 81512.86387443542, "min_query_time_ms": 1426.7477989196777}, "debugging": {"path_matching": {"total_queries": 52, "exact_match_count": 44, "exact_match_rate": 0.8461538461538461, "avg_k": 6.5, "success_rate_at_1": 0.5576923076923077, "success_rate_at_3": 0.75, "success_rate_at_5": 0.8269230769230769, "success_rate_at_k": 0.8269230769230769, "precision_at_1": 0.5576923076923077, "precision_at_3": 0.2756410256410257, "precision_at_5": 0.21826923076923077, "precision_at_k": 0.003205128205128205, "mrr_at_k": 0.6612179487179487, "ndcg_at_k": 0.019230769230769232, "f1_score": 0.5576923076923077, "avg_query_time_ms": 3321.7234153013965, "max_query_time_ms": 35463.72389793396, "min_query_time_ms": 1410.0120067596436}, "content_matching": {"total_queries": 52, "exact_match_count": 41, "exact_match_rate": 0.7884615384615384, "avg_k": 6.5, "success_rate_at_1": 0.4230769230769231, "success_rate_at_3": 0.5961538461538461, "success_rate_at_5": 0.7307692307692307, "success_rate_at_k": 0.7307692307692307, "precision_at_1": 0.4230769230769231, "precision_at_3": 0.22435897435897434, "precision_at_5": 0.19647435897435897, "precision_at_k": 0.003205128205128205, "mrr_at_k": 0.5367521367521367, "ndcg_at_k": 0.019230769230769232, "f1_score": 0.4230769230769231, "avg_query_time_ms": 3321.7234153013965, "max_query_time_ms": 35463.72389793396, "min_query_time_ms": 1410.0120067596436}, "total_queries": 52, "exact_match_count": 44, "exact_match_rate": 0.8461538461538461, "avg_k": 6.5, "success_rate_at_1": 0.5576923076923077, "success_rate_at_3": 0.75, "success_rate_at_5": 0.8269230769230769, "success_rate_at_k": 0.8269230769230769, "precision_at_1": 0.5576923076923077, "precision_at_3": 0.2756410256410257, "precision_at_5": 0.21826923076923077, "precision_at_k": 0.003205128205128205, "mrr_at_k": 0.6612179487179487, "ndcg_at_k": 0.019230769230769232, "f1_score": 0.5576923076923077, "avg_query_time_ms": 3321.7234153013965, "max_query_time_ms": 35463.72389793396, "min_query_time_ms": 1410.0120067596436}, "optimization": {"path_matching": {"total_queries": 50, "exact_match_count": 38, "exact_match_rate": 0.76, "avg_k": 5.7, "success_rate_at_1": 0.48, "success_rate_at_3": 0.7, "success_rate_at_5": 0.76, "success_rate_at_k": 0.76, "precision_at_1": 0.48, "precision_at_3": 0.2433333333333334, "precision_at_5": 0.19833333333333333, "precision_at_k": 0.013333333333333332, "mrr_at_k": 0.5986666666666666, "ndcg_at_k": 0.060355651216119986, "f1_score": 0.48, "avg_query_time_ms": 3341.3503313064575, "max_query_time_ms": 35429.983139038086, "min_query_time_ms": 1426.2971878051758}, "content_matching": {"total_queries": 50, "exact_match_count": 33, "exact_match_rate": 0.66, "avg_k": 5.7, "success_rate_at_1": 0.34, "success_rate_at_3": 0.6, "success_rate_at_5": 0.66, "success_rate_at_k": 0.66, "precision_at_1": 0.34, "precision_at_3": 0.20666666666666664, "precision_at_5": 0.16966666666666666, "precision_at_k": 0.01, "mrr_at_k": 0.4696666666666666, "ndcg_at_k": 0.05, "f1_score": 0.34, "avg_query_time_ms": 3341.3503313064575, "max_query_time_ms": 35429.983139038086, "min_query_time_ms": 1426.2971878051758}, "total_queries": 50, "exact_match_count": 38, "exact_match_rate": 0.76, "avg_k": 5.7, "success_rate_at_1": 0.48, "success_rate_at_3": 0.7, "success_rate_at_5": 0.76, "success_rate_at_k": 0.76, "precision_at_1": 0.48, "precision_at_3": 0.2433333333333334, "precision_at_5": 0.19833333333333333, "precision_at_k": 0.013333333333333332, "mrr_at_k": 0.5986666666666666, "ndcg_at_k": 0.060355651216119986, "f1_score": 0.48, "avg_query_time_ms": 3341.3503313064575, "max_query_time_ms": 35429.983139038086, "min_query_time_ms": 1426.2971878051758}, "testing": {"path_matching": {"total_queries": 46, "exact_match_count": 33, "exact_match_rate": 0.717391304347826, "avg_k": 4.304347826086956, "success_rate_at_1": 0.45652173913043476, "success_rate_at_3": 0.6086956521739131, "success_rate_at_5": 0.6956521739130435, "success_rate_at_k": 0.6956521739130435, "precision_at_1": 0.45652173913043476, "precision_at_3": 0.29347826086956535, "precision_at_5": 0.2728260869565216, "precision_at_k": 0.02717391304347826, "mrr_at_k": 0.5456521739130434, "ndcg_at_k": 0.0650496276025705, "f1_score": 0.45652173913043476, "avg_query_time_ms": 2812.0113870371943, "max_query_time_ms": 33711.30323410034, "min_query_time_ms": 1376.6260147094727}, "content_matching": {"total_queries": 46, "exact_match_count": 26, "exact_match_rate": 0.5652173913043478, "avg_k": 4.304347826086956, "success_rate_at_1": 0.32608695652173914, "success_rate_at_3": 0.45652173913043476, "success_rate_at_5": 0.5652173913043478, "success_rate_at_k": 0.5652173913043478, "precision_at_1": 0.32608695652173914, "precision_at_3": 0.19927536231884052, "precision_at_5": 0.19057971014492756, "precision_at_k": 0.021739130434782608, "mrr_at_k": 0.4076086956521739, "ndcg_at_k": 0.05133376339449535, "f1_score": 0.32608695652173914, "avg_query_time_ms": 2812.0113870371943, "max_query_time_ms": 33711.30323410034, "min_query_time_ms": 1376.6260147094727}, "total_queries": 46, "exact_match_count": 33, "exact_match_rate": 0.717391304347826, "avg_k": 4.304347826086956, "success_rate_at_1": 0.45652173913043476, "success_rate_at_3": 0.6086956521739131, "success_rate_at_5": 0.6956521739130435, "success_rate_at_k": 0.6956521739130435, "precision_at_1": 0.45652173913043476, "precision_at_3": 0.29347826086956535, "precision_at_5": 0.2728260869565216, "precision_at_k": 0.02717391304347826, "mrr_at_k": 0.5456521739130434, "ndcg_at_k": 0.0650496276025705, "f1_score": 0.45652173913043476, "avg_query_time_ms": 2812.0113870371943, "max_query_time_ms": 33711.30323410034, "min_query_time_ms": 1376.6260147094727}, "integration": {"path_matching": {"total_queries": 48, "exact_match_count": 36, "exact_match_rate": 0.75, "avg_k": 5.979166666666667, "success_rate_at_1": 0.5, "success_rate_at_3": 0.6458333333333334, "success_rate_at_5": 0.6875, "success_rate_at_k": 0.7083333333333334, "precision_at_1": 0.5, "precision_at_3": 0.24305555555555566, "precision_at_5": 0.1847222222222222, "precision_at_k": 0.013888888888888888, "mrr_at_k": 0.585813492063492, "ndcg_at_k": 0.061055761626529016, "f1_score": 0.5, "avg_query_time_ms": 2837.************, "max_query_time_ms": 36369.************, "min_query_time_ms": 1329.3981552124023}, "content_matching": {"total_queries": 48, "exact_match_count": 31, "exact_match_rate": 0.6458333333333334, "avg_k": 5.979166666666667, "success_rate_at_1": 0.2916666666666667, "success_rate_at_3": 0.4375, "success_rate_at_5": 0.5625, "success_rate_at_k": 0.6041666666666666, "precision_at_1": 0.2916666666666667, "precision_at_3": 0.15972222222222215, "precision_at_5": 0.1430555555555556, "precision_at_k": 0.01736111111111111, "mrr_at_k": 0.3969576719576719, "ndcg_at_k": 0.06911519511058196, "f1_score": 0.2916666666666667, "avg_query_time_ms": 2837.************, "max_query_time_ms": 36369.************, "min_query_time_ms": 1329.3981552124023}, "total_queries": 48, "exact_match_count": 36, "exact_match_rate": 0.75, "avg_k": 5.979166666666667, "success_rate_at_1": 0.5, "success_rate_at_3": 0.6458333333333334, "success_rate_at_5": 0.6875, "success_rate_at_k": 0.7083333333333334, "precision_at_1": 0.5, "precision_at_3": 0.24305555555555566, "precision_at_5": 0.1847222222222222, "precision_at_k": 0.013888888888888888, "mrr_at_k": 0.585813492063492, "ndcg_at_k": 0.061055761626529016, "f1_score": 0.5, "avg_query_time_ms": 2837.************, "max_query_time_ms": 36369.************, "min_query_time_ms": 1329.3981552124023}}, "interview-guide": {"understanding": {"path_matching": {"total_queries": 41, "exact_match_count": 39, "exact_match_rate": 0.9512195121951219, "avg_k": 4.2926829268292686, "success_rate_at_1": 0.6341463414634146, "success_rate_at_3": 0.9024390243902439, "success_rate_at_5": 0.9512195121951219, "success_rate_at_k": 0.9512195121951219, "precision_at_1": 0.6341463414634146, "precision_at_3": 0.410569105691057, "precision_at_5": 0.35934959349593487, "precision_at_k": 0.036585365853658534, "mrr_at_k": 0.7723577235772358, "ndcg_at_k": 0.10545038582409183, "f1_score": 0.6341463414634146, "avg_query_time_ms": 2051.199662976149, "max_query_time_ms": 5869.012117385864, "min_query_time_ms": 1158.6670875549316}, "content_matching": {"total_queries": 41, "exact_match_count": 37, "exact_match_rate": 0.9024390243902439, "avg_k": 4.2926829268292686, "success_rate_at_1": 0.5853658536585366, "success_rate_at_3": 0.8292682926829268, "success_rate_at_5": 0.8780487804878049, "success_rate_at_k": 0.8780487804878049, "precision_at_1": 0.5853658536585366, "precision_at_3": 0.3658536585365854, "precision_at_5": 0.31788617886178855, "precision_at_k": 0.036585365853658534, "mrr_at_k": 0.7154471544715447, "ndcg_at_k": 0.10545038582409183, "f1_score": 0.5853658536585366, "avg_query_time_ms": 2051.199662976149, "max_query_time_ms": 5869.012117385864, "min_query_time_ms": 1158.6670875549316}, "total_queries": 41, "exact_match_count": 39, "exact_match_rate": 0.9512195121951219, "avg_k": 4.2926829268292686, "success_rate_at_1": 0.6341463414634146, "success_rate_at_3": 0.9024390243902439, "success_rate_at_5": 0.9512195121951219, "success_rate_at_k": 0.9512195121951219, "precision_at_1": 0.6341463414634146, "precision_at_3": 0.410569105691057, "precision_at_5": 0.35934959349593487, "precision_at_k": 0.036585365853658534, "mrr_at_k": 0.7723577235772358, "ndcg_at_k": 0.10545038582409183, "f1_score": 0.6341463414634146, "avg_query_time_ms": 2051.199662976149, "max_query_time_ms": 5869.012117385864, "min_query_time_ms": 1158.6670875549316}, "implementation": {"path_matching": {"total_queries": 39, "exact_match_count": 37, "exact_match_rate": 0.9487179487179487, "avg_k": 5.6923076923076925, "success_rate_at_1": 0.5384615384615384, "success_rate_at_3": 0.717948717948718, "success_rate_at_5": 0.8717948717948718, "success_rate_at_k": 0.8974358974358975, "precision_at_1": 0.5384615384615384, "precision_at_3": 0.3376068376068377, "precision_at_5": 0.31965811965811963, "precision_at_k": 0.01282051282051282, "mrr_at_k": 0.6733211233211234, "ndcg_at_k": 0.07692307692307693, "f1_score": 0.5384615384615384, "avg_query_time_ms": 1944.2978149805313, "max_query_time_ms": 5056.797981262207, "min_query_time_ms": 1389.091968536377}, "content_matching": {"total_queries": 39, "exact_match_count": 37, "exact_match_rate": 0.9487179487179487, "avg_k": 5.6923076923076925, "success_rate_at_1": 0.5641025641025641, "success_rate_at_3": 0.717948717948718, "success_rate_at_5": 0.8717948717948718, "success_rate_at_k": 0.8974358974358975, "precision_at_1": 0.5641025641025641, "precision_at_3": 0.3376068376068377, "precision_at_5": 0.31965811965811963, "precision_at_k": 0.01282051282051282, "mrr_at_k": 0.6861416361416361, "ndcg_at_k": 0.07692307692307693, "f1_score": 0.5641025641025641, "avg_query_time_ms": 1944.2978149805313, "max_query_time_ms": 5056.797981262207, "min_query_time_ms": 1389.091968536377}, "total_queries": 39, "exact_match_count": 37, "exact_match_rate": 0.9487179487179487, "avg_k": 5.6923076923076925, "success_rate_at_1": 0.5384615384615384, "success_rate_at_3": 0.717948717948718, "success_rate_at_5": 0.8717948717948718, "success_rate_at_k": 0.8974358974358975, "precision_at_1": 0.5384615384615384, "precision_at_3": 0.3376068376068377, "precision_at_5": 0.31965811965811963, "precision_at_k": 0.01282051282051282, "mrr_at_k": 0.6733211233211234, "ndcg_at_k": 0.07692307692307693, "f1_score": 0.5384615384615384, "avg_query_time_ms": 1944.2978149805313, "max_query_time_ms": 5056.797981262207, "min_query_time_ms": 1389.091968536377}, "debugging": {"path_matching": {"total_queries": 41, "exact_match_count": 41, "exact_match_rate": 1.0, "avg_k": 5.219512195121951, "success_rate_at_1": 0.6585365853658537, "success_rate_at_3": 0.926829268292683, "success_rate_at_5": 0.926829268292683, "success_rate_at_k": 0.926829268292683, "precision_at_1": 0.6585365853658537, "precision_at_3": 0.394308943089431, "precision_at_5": 0.31016260162601617, "precision_at_k": 0.31016260162601617, "mrr_at_k": 0.8002322880371661, "ndcg_at_k": 0.8246170130662092, "f1_score": 0.6585365853658537, "avg_query_time_ms": 2083.1473048140365, "max_query_time_ms": 6804.700136184692, "min_query_time_ms": 1268.005132675171}, "content_matching": {"total_queries": 41, "exact_match_count": 41, "exact_match_rate": 1.0, "avg_k": 5.219512195121951, "success_rate_at_1": 0.5853658536585366, "success_rate_at_3": 0.8292682926829268, "success_rate_at_5": 0.9024390243902439, "success_rate_at_k": 0.9024390243902439, "precision_at_1": 0.5853658536585366, "precision_at_3": 0.36178861788617894, "precision_at_5": 0.3052845528455284, "precision_at_k": 0.3052845528455284, "mrr_at_k": 0.7353658536585367, "ndcg_at_k": 0.765432925724039, "f1_score": 0.5853658536585366, "avg_query_time_ms": 2083.1473048140365, "max_query_time_ms": 6804.700136184692, "min_query_time_ms": 1268.005132675171}, "total_queries": 41, "exact_match_count": 41, "exact_match_rate": 1.0, "avg_k": 5.219512195121951, "success_rate_at_1": 0.6585365853658537, "success_rate_at_3": 0.926829268292683, "success_rate_at_5": 0.926829268292683, "success_rate_at_k": 0.926829268292683, "precision_at_1": 0.6585365853658537, "precision_at_3": 0.394308943089431, "precision_at_5": 0.31016260162601617, "precision_at_k": 0.31016260162601617, "mrr_at_k": 0.8002322880371661, "ndcg_at_k": 0.8246170130662092, "f1_score": 0.6585365853658537, "avg_query_time_ms": 2083.1473048140365, "max_query_time_ms": 6804.700136184692, "min_query_time_ms": 1268.005132675171}, "testing": {"path_matching": {"total_queries": 29, "exact_match_count": 25, "exact_match_rate": 0.8620689655172413, "avg_k": 3.413793103448276, "success_rate_at_1": 0.41379310344827586, "success_rate_at_3": 0.8275862068965517, "success_rate_at_5": 0.8620689655172413, "success_rate_at_k": 0.8275862068965517, "precision_at_1": 0.41379310344827586, "precision_at_3": 0.3160919540229885, "precision_at_5": 0.3, "precision_at_k": 0.3160919540229885, "mrr_at_k": 0.617816091954023, "ndcg_at_k": 0.6658378460591233, "f1_score": 0.41379310344827586, "avg_query_time_ms": 1894.2547995468665, "max_query_time_ms": 3492.1600818634033, "min_query_time_ms": 1263.594150543213}, "content_matching": {"total_queries": 29, "exact_match_count": 23, "exact_match_rate": 0.7931034482758621, "avg_k": 3.413793103448276, "success_rate_at_1": 0.4482758620689655, "success_rate_at_3": 0.7586206896551724, "success_rate_at_5": 0.7931034482758621, "success_rate_at_k": 0.7586206896551724, "precision_at_1": 0.4482758620689655, "precision_at_3": 0.28735632183908033, "precision_at_5": 0.2741379310344827, "precision_at_k": 0.28735632183908033, "mrr_at_k": 0.6005747126436782, "ndcg_at_k": 0.6350520094827656, "f1_score": 0.4482758620689655, "avg_query_time_ms": 1894.2547995468665, "max_query_time_ms": 3492.1600818634033, "min_query_time_ms": 1263.594150543213}, "total_queries": 29, "exact_match_count": 25, "exact_match_rate": 0.8620689655172413, "avg_k": 3.413793103448276, "success_rate_at_1": 0.41379310344827586, "success_rate_at_3": 0.8275862068965517, "success_rate_at_5": 0.8620689655172413, "success_rate_at_k": 0.8275862068965517, "precision_at_1": 0.41379310344827586, "precision_at_3": 0.3160919540229885, "precision_at_5": 0.3, "precision_at_k": 0.3160919540229885, "mrr_at_k": 0.617816091954023, "ndcg_at_k": 0.6658378460591233, "f1_score": 0.41379310344827586, "avg_query_time_ms": 1894.2547995468665, "max_query_time_ms": 3492.1600818634033, "min_query_time_ms": 1263.594150543213}, "integration": {"path_matching": {"total_queries": 38, "exact_match_count": 29, "exact_match_rate": 0.7631578947368421, "avg_k": 5.684210526315789, "success_rate_at_1": 0.5263157894736842, "success_rate_at_3": 0.7105263157894737, "success_rate_at_5": 0.7105263157894737, "success_rate_at_k": 0.7631578947368421, "precision_at_1": 0.5263157894736842, "precision_at_3": 0.3157894736842106, "precision_at_5": 0.25043859649122807, "precision_at_k": 0.013157894736842105, "mrr_at_k": 0.618421052631579, "ndcg_at_k": 0.056077098778196256, "f1_score": 0.5263157894736842, "avg_query_time_ms": 2852.2673719807676, "max_query_time_ms": 38333.45532417297, "min_query_time_ms": 1357.3708534240723}, "content_matching": {"total_queries": 38, "exact_match_count": 27, "exact_match_rate": 0.7105263157894737, "avg_k": 5.684210526315789, "success_rate_at_1": 0.3157894736842105, "success_rate_at_3": 0.5789473684210527, "success_rate_at_5": 0.631578947368421, "success_rate_at_k": 0.6842105263157895, "precision_at_1": 0.3157894736842105, "precision_at_3": 0.23684210526315788, "precision_at_5": 0.19254385964912288, "precision_at_k": 0.017543859649122806, "mrr_at_k": 0.4564327485380117, "ndcg_at_k": 0.06923499351503837, "f1_score": 0.3157894736842105, "avg_query_time_ms": 2852.2673719807676, "max_query_time_ms": 38333.45532417297, "min_query_time_ms": 1357.3708534240723}, "total_queries": 38, "exact_match_count": 29, "exact_match_rate": 0.7631578947368421, "avg_k": 5.684210526315789, "success_rate_at_1": 0.5263157894736842, "success_rate_at_3": 0.7105263157894737, "success_rate_at_5": 0.7105263157894737, "success_rate_at_k": 0.7631578947368421, "precision_at_1": 0.5263157894736842, "precision_at_3": 0.3157894736842106, "precision_at_5": 0.25043859649122807, "precision_at_k": 0.013157894736842105, "mrr_at_k": 0.618421052631579, "ndcg_at_k": 0.056077098778196256, "f1_score": 0.5263157894736842, "avg_query_time_ms": 2852.2673719807676, "max_query_time_ms": 38333.45532417297, "min_query_time_ms": 1357.3708534240723}, "optimization": {"path_matching": {"total_queries": 38, "exact_match_count": 35, "exact_match_rate": 0.9210526315789473, "avg_k": 5.815789473684211, "success_rate_at_1": 0.631578947368421, "success_rate_at_3": 0.7894736842105263, "success_rate_at_5": 0.8421052631578947, "success_rate_at_k": 0.8947368421052632, "precision_at_1": 0.631578947368421, "precision_at_3": 0.35526315789473695, "precision_at_5": 0.2934210526315789, "precision_at_k": 0.008771929824561403, "mrr_at_k": 0.7309941520467836, "ndcg_at_k": 0.042919204041354145, "f1_score": 0.631578947368421, "avg_query_time_ms": 2079.2825849432693, "max_query_time_ms": 3935.340166091919, "min_query_time_ms": 1299.593210220337}, "content_matching": {"total_queries": 38, "exact_match_count": 34, "exact_match_rate": 0.8947368421052632, "avg_k": 5.815789473684211, "success_rate_at_1": 0.631578947368421, "success_rate_at_3": 0.7631578947368421, "success_rate_at_5": 0.8157894736842105, "success_rate_at_k": 0.868421052631579, "precision_at_1": 0.631578947368421, "precision_at_3": 0.3289473684210527, "precision_at_5": 0.2671052631578947, "precision_at_k": 0.008771929824561403, "mrr_at_k": 0.7178362573099415, "ndcg_at_k": 0.042919204041354145, "f1_score": 0.631578947368421, "avg_query_time_ms": 2079.2825849432693, "max_query_time_ms": 3935.340166091919, "min_query_time_ms": 1299.593210220337}, "total_queries": 38, "exact_match_count": 35, "exact_match_rate": 0.9210526315789473, "avg_k": 5.815789473684211, "success_rate_at_1": 0.631578947368421, "success_rate_at_3": 0.7894736842105263, "success_rate_at_5": 0.8421052631578947, "success_rate_at_k": 0.8947368421052632, "precision_at_1": 0.631578947368421, "precision_at_3": 0.35526315789473695, "precision_at_5": 0.2934210526315789, "precision_at_k": 0.008771929824561403, "mrr_at_k": 0.7309941520467836, "ndcg_at_k": 0.042919204041354145, "f1_score": 0.631578947368421, "avg_query_time_ms": 2079.2825849432693, "max_query_time_ms": 3935.340166091919, "min_query_time_ms": 1299.593210220337}}, "Wechat_ArkTs": {"implementation": {"path_matching": {"total_queries": 27, "exact_match_count": 22, "exact_match_rate": 0.8148148148148148, "avg_k": 4.592592592592593, "success_rate_at_1": 0.48148148148148145, "success_rate_at_3": 0.7777777777777778, "success_rate_at_5": 0.7777777777777778, "success_rate_at_k": 0.7777777777777778, "precision_at_1": 0.48148148148148145, "precision_at_3": 0.3827160493827161, "precision_at_5": 0.3308641975308642, "precision_at_k": 0.3308641975308642, "mrr_at_k": 0.6225749559082893, "ndcg_at_k": 0.658725130423287, "f1_score": 0.48148148148148145, "avg_query_time_ms": 1924.5886361157452, "max_query_time_ms": 4433.072328567505, "min_query_time_ms": 1220.5281257629395}, "content_matching": {"total_queries": 27, "exact_match_count": 21, "exact_match_rate": 0.7777777777777778, "avg_k": 4.592592592592593, "success_rate_at_1": 0.4444444444444444, "success_rate_at_3": 0.7037037037037037, "success_rate_at_5": 0.7407407407407407, "success_rate_at_k": 0.7407407407407407, "precision_at_1": 0.4444444444444444, "precision_at_3": 0.35802469135802467, "precision_at_5": 0.32160493827160497, "precision_at_k": 0.32160493827160497, "mrr_at_k": 0.576278659611993, "ndcg_at_k": 0.6142713083678031, "f1_score": 0.4444444444444444, "avg_query_time_ms": 1924.5886361157452, "max_query_time_ms": 4433.072328567505, "min_query_time_ms": 1220.5281257629395}, "total_queries": 27, "exact_match_count": 22, "exact_match_rate": 0.8148148148148148, "avg_k": 4.592592592592593, "success_rate_at_1": 0.48148148148148145, "success_rate_at_3": 0.7777777777777778, "success_rate_at_5": 0.7777777777777778, "success_rate_at_k": 0.7777777777777778, "precision_at_1": 0.48148148148148145, "precision_at_3": 0.3827160493827161, "precision_at_5": 0.3308641975308642, "precision_at_k": 0.3308641975308642, "mrr_at_k": 0.6225749559082893, "ndcg_at_k": 0.658725130423287, "f1_score": 0.48148148148148145, "avg_query_time_ms": 1924.5886361157452, "max_query_time_ms": 4433.072328567505, "min_query_time_ms": 1220.5281257629395}, "understanding": {"path_matching": {"total_queries": 28, "exact_match_count": 27, "exact_match_rate": 0.9642857142857143, "avg_k": 4.178571428571429, "success_rate_at_1": 0.6785714285714286, "success_rate_at_3": 0.9285714285714286, "success_rate_at_5": 0.9642857142857143, "success_rate_at_k": 0.9285714285714286, "precision_at_1": 0.6785714285714286, "precision_at_3": 0.4761904761904763, "precision_at_5": 0.4363095238095237, "precision_at_k": 0.026785714285714284, "mrr_at_k": 0.8047619047619048, "ndcg_at_k": 0.07610463405612349, "f1_score": 0.6785714285714286, "avg_query_time_ms": 2154.9211229596817, "max_query_time_ms": 8172.535181045532, "min_query_time_ms": 1419.4798469543457}, "content_matching": {"total_queries": 28, "exact_match_count": 27, "exact_match_rate": 0.9642857142857143, "avg_k": 4.178571428571429, "success_rate_at_1": 0.6428571428571429, "success_rate_at_3": 0.9642857142857143, "success_rate_at_5": 0.9642857142857143, "success_rate_at_k": 0.9642857142857143, "precision_at_1": 0.6428571428571429, "precision_at_3": 0.4880952380952382, "precision_at_5": 0.4363095238095237, "precision_at_k": 0.026785714285714284, "mrr_at_k": 0.7976190476190477, "ndcg_at_k": 0.07610463405612349, "f1_score": 0.6428571428571429, "avg_query_time_ms": 2154.9211229596817, "max_query_time_ms": 8172.535181045532, "min_query_time_ms": 1419.4798469543457}, "total_queries": 28, "exact_match_count": 27, "exact_match_rate": 0.9642857142857143, "avg_k": 4.178571428571429, "success_rate_at_1": 0.6785714285714286, "success_rate_at_3": 0.9285714285714286, "success_rate_at_5": 0.9642857142857143, "success_rate_at_k": 0.9285714285714286, "precision_at_1": 0.6785714285714286, "precision_at_3": 0.4761904761904763, "precision_at_5": 0.4363095238095237, "precision_at_k": 0.026785714285714284, "mrr_at_k": 0.8047619047619048, "ndcg_at_k": 0.07610463405612349, "f1_score": 0.6785714285714286, "avg_query_time_ms": 2154.9211229596817, "max_query_time_ms": 8172.535181045532, "min_query_time_ms": 1419.4798469543457}, "debugging": {"path_matching": {"total_queries": 29, "exact_match_count": 24, "exact_match_rate": 0.8275862068965517, "avg_k": 3.7241379310344827, "success_rate_at_1": 0.5517241379310345, "success_rate_at_3": 0.7931034482758621, "success_rate_at_5": 0.7931034482758621, "success_rate_at_k": 0.7931034482758621, "precision_at_1": 0.5517241379310345, "precision_at_3": 0.3333333333333333, "precision_at_5": 0.29425287356321833, "precision_at_k": 0.034482758620689655, "mrr_at_k": 0.6715927750410509, "ndcg_at_k": 0.11247791403941086, "f1_score": 0.5517241379310345, "avg_query_time_ms": 1801.0891881482355, "max_query_time_ms": 2992.8061962127686, "min_query_time_ms": 1247.347116470337}, "content_matching": {"total_queries": 29, "exact_match_count": 25, "exact_match_rate": 0.8620689655172413, "avg_k": 3.7241379310344827, "success_rate_at_1": 0.5172413793103449, "success_rate_at_3": 0.8275862068965517, "success_rate_at_5": 0.8275862068965517, "success_rate_at_k": 0.8275862068965517, "precision_at_1": 0.5172413793103449, "precision_at_3": 0.3448275862068966, "precision_at_5": 0.30287356321839076, "precision_at_k": 0.04310344827586207, "mrr_at_k": 0.6600985221674877, "ndcg_at_k": 0.11699273312808184, "f1_score": 0.5172413793103449, "avg_query_time_ms": 1801.0891881482355, "max_query_time_ms": 2992.8061962127686, "min_query_time_ms": 1247.347116470337}, "total_queries": 29, "exact_match_count": 24, "exact_match_rate": 0.8275862068965517, "avg_k": 3.7241379310344827, "success_rate_at_1": 0.5517241379310345, "success_rate_at_3": 0.7931034482758621, "success_rate_at_5": 0.7931034482758621, "success_rate_at_k": 0.7931034482758621, "precision_at_1": 0.5517241379310345, "precision_at_3": 0.3333333333333333, "precision_at_5": 0.29425287356321833, "precision_at_k": 0.034482758620689655, "mrr_at_k": 0.6715927750410509, "ndcg_at_k": 0.11247791403941086, "f1_score": 0.5517241379310345, "avg_query_time_ms": 1801.0891881482355, "max_query_time_ms": 2992.8061962127686, "min_query_time_ms": 1247.347116470337}, "testing": {"path_matching": {"total_queries": 21, "exact_match_count": 16, "exact_match_rate": 0.7619047619047619, "avg_k": 2.8095238095238093, "success_rate_at_1": 0.5238095238095238, "success_rate_at_3": 0.6666666666666666, "success_rate_at_5": 0.7619047619047619, "success_rate_at_k": 0.6666666666666666, "precision_at_1": 0.5238095238095238, "precision_at_3": 0.34920634920634913, "precision_at_5": 0.346031746031746, "precision_at_k": 0.34920634920634913, "mrr_at_k": 0.6111111111111112, "ndcg_at_k": 0.6077075955782341, "f1_score": 0.5238095238095238, "avg_query_time_ms": 1747.775952021281, "max_query_time_ms": 2642.6479816436768, "min_query_time_ms": 1246.469259262085}, "content_matching": {"total_queries": 21, "exact_match_count": 16, "exact_match_rate": 0.7619047619047619, "avg_k": 2.8095238095238093, "success_rate_at_1": 0.5238095238095238, "success_rate_at_3": 0.6666666666666666, "success_rate_at_5": 0.7619047619047619, "success_rate_at_k": 0.6666666666666666, "precision_at_1": 0.5238095238095238, "precision_at_3": 0.34920634920634913, "precision_at_5": 0.346031746031746, "precision_at_k": 0.34920634920634913, "mrr_at_k": 0.6111111111111112, "ndcg_at_k": 0.6077075955782341, "f1_score": 0.5238095238095238, "avg_query_time_ms": 1747.775952021281, "max_query_time_ms": 2642.6479816436768, "min_query_time_ms": 1246.469259262085}, "total_queries": 21, "exact_match_count": 16, "exact_match_rate": 0.7619047619047619, "avg_k": 2.8095238095238093, "success_rate_at_1": 0.5238095238095238, "success_rate_at_3": 0.6666666666666666, "success_rate_at_5": 0.7619047619047619, "success_rate_at_k": 0.6666666666666666, "precision_at_1": 0.5238095238095238, "precision_at_3": 0.34920634920634913, "precision_at_5": 0.346031746031746, "precision_at_k": 0.34920634920634913, "mrr_at_k": 0.6111111111111112, "ndcg_at_k": 0.6077075955782341, "f1_score": 0.5238095238095238, "avg_query_time_ms": 1747.775952021281, "max_query_time_ms": 2642.6479816436768, "min_query_time_ms": 1246.469259262085}, "optimization": {"path_matching": {"total_queries": 25, "exact_match_count": 16, "exact_match_rate": 0.64, "avg_k": 3.92, "success_rate_at_1": 0.44, "success_rate_at_3": 0.52, "success_rate_at_5": 0.56, "success_rate_at_k": 0.56, "precision_at_1": 0.44, "precision_at_3": 0.24666666666666665, "precision_at_5": 0.23733333333333334, "precision_at_k": 0.02, "mrr_at_k": 0.49476190476190474, "ndcg_at_k": 0.037227062322935725, "f1_score": 0.44, "avg_query_time_ms": 1807.4537181854248, "max_query_time_ms": 3106.353998184204, "min_query_time_ms": 1374.3059635162354}, "content_matching": {"total_queries": 25, "exact_match_count": 16, "exact_match_rate": 0.64, "avg_k": 3.92, "success_rate_at_1": 0.44, "success_rate_at_3": 0.6, "success_rate_at_5": 0.6, "success_rate_at_k": 0.6, "precision_at_1": 0.44, "precision_at_3": 0.27333333333333326, "precision_at_5": 0.24533333333333335, "precision_at_k": 0.02, "mrr_at_k": 0.5123809523809524, "ndcg_at_k": 0.04, "f1_score": 0.44, "avg_query_time_ms": 1807.4537181854248, "max_query_time_ms": 3106.353998184204, "min_query_time_ms": 1374.3059635162354}, "total_queries": 25, "exact_match_count": 16, "exact_match_rate": 0.64, "avg_k": 3.92, "success_rate_at_1": 0.44, "success_rate_at_3": 0.52, "success_rate_at_5": 0.56, "success_rate_at_k": 0.56, "precision_at_1": 0.44, "precision_at_3": 0.24666666666666665, "precision_at_5": 0.23733333333333334, "precision_at_k": 0.02, "mrr_at_k": 0.49476190476190474, "ndcg_at_k": 0.037227062322935725, "f1_score": 0.44, "avg_query_time_ms": 1807.4537181854248, "max_query_time_ms": 3106.353998184204, "min_query_time_ms": 1374.3059635162354}, "integration": {"path_matching": {"total_queries": 22, "exact_match_count": 13, "exact_match_rate": 0.5909090909090909, "avg_k": 3.090909090909091, "success_rate_at_1": 0.3181818181818182, "success_rate_at_3": 0.5454545454545454, "success_rate_at_5": 0.5909090909090909, "success_rate_at_k": 0.5454545454545454, "precision_at_1": 0.3181818181818182, "precision_at_3": 0.3181818181818181, "precision_at_5": 0.3037878787878788, "precision_at_k": 0.3181818181818181, "mrr_at_k": 0.43560606060606055, "ndcg_at_k": 0.45562359155844684, "f1_score": 0.3181818181818182, "avg_query_time_ms": 1739.113677631725, "max_query_time_ms": 2424.8712062835693, "min_query_time_ms": 1261.0499858856201}, "content_matching": {"total_queries": 22, "exact_match_count": 13, "exact_match_rate": 0.5909090909090909, "avg_k": 3.090909090909091, "success_rate_at_1": 0.3181818181818182, "success_rate_at_3": 0.5454545454545454, "success_rate_at_5": 0.5909090909090909, "success_rate_at_k": 0.5454545454545454, "precision_at_1": 0.3181818181818182, "precision_at_3": 0.3181818181818181, "precision_at_5": 0.3037878787878788, "precision_at_k": 0.3181818181818181, "mrr_at_k": 0.4431818181818182, "ndcg_at_k": 0.4615749439935131, "f1_score": 0.3181818181818182, "avg_query_time_ms": 1739.113677631725, "max_query_time_ms": 2424.8712062835693, "min_query_time_ms": 1261.0499858856201}, "total_queries": 22, "exact_match_count": 13, "exact_match_rate": 0.5909090909090909, "avg_k": 3.090909090909091, "success_rate_at_1": 0.3181818181818182, "success_rate_at_3": 0.5454545454545454, "success_rate_at_5": 0.5909090909090909, "success_rate_at_k": 0.5454545454545454, "precision_at_1": 0.3181818181818182, "precision_at_3": 0.3181818181818181, "precision_at_5": 0.3037878787878788, "precision_at_k": 0.3181818181818181, "mrr_at_k": 0.43560606060606055, "ndcg_at_k": 0.45562359155844684, "f1_score": 0.3181818181818182, "avg_query_time_ms": 1739.113677631725, "max_query_time_ms": 2424.8712062835693, "min_query_time_ms": 1261.0499858856201}}, "Chatime": {"implementation": {"path_matching": {"total_queries": 25, "exact_match_count": 24, "exact_match_rate": 0.96, "avg_k": 5.56, "success_rate_at_1": 0.6, "success_rate_at_3": 0.84, "success_rate_at_5": 0.84, "success_rate_at_k": 0.96, "precision_at_1": 0.6, "precision_at_3": 0.32666666666666655, "precision_at_5": 0.26133333333333336, "precision_at_k": 0.013333333333333332, "mrr_at_k": 0.7333333333333333, "ndcg_at_k": 0.08, "f1_score": 0.6, "avg_query_time_ms": 2047.9127788543701, "max_query_time_ms": 8407.39107131958, "min_query_time_ms": 1318.9928531646729}, "content_matching": {"total_queries": 25, "exact_match_count": 21, "exact_match_rate": 0.84, "avg_k": 5.56, "success_rate_at_1": 0.44, "success_rate_at_3": 0.72, "success_rate_at_5": 0.76, "success_rate_at_k": 0.8, "precision_at_1": 0.44, "precision_at_3": 0.2866666666666666, "precision_at_5": 0.24533333333333335, "precision_at_k": 0.013333333333333332, "mrr_at_k": 0.5890476190476189, "ndcg_at_k": 0.08, "f1_score": 0.44, "avg_query_time_ms": 2047.9127788543701, "max_query_time_ms": 8407.39107131958, "min_query_time_ms": 1318.9928531646729}, "total_queries": 25, "exact_match_count": 24, "exact_match_rate": 0.96, "avg_k": 5.56, "success_rate_at_1": 0.6, "success_rate_at_3": 0.84, "success_rate_at_5": 0.84, "success_rate_at_k": 0.96, "precision_at_1": 0.6, "precision_at_3": 0.32666666666666655, "precision_at_5": 0.26133333333333336, "precision_at_k": 0.013333333333333332, "mrr_at_k": 0.7333333333333333, "ndcg_at_k": 0.08, "f1_score": 0.6, "avg_query_time_ms": 2047.9127788543701, "max_query_time_ms": 8407.39107131958, "min_query_time_ms": 1318.9928531646729}, "understanding": {"path_matching": {"total_queries": 26, "exact_match_count": 25, "exact_match_rate": 0.9615384615384616, "avg_k": 4.461538461538462, "success_rate_at_1": 0.6538461538461539, "success_rate_at_3": 0.8846153846153846, "success_rate_at_5": 0.9230769230769231, "success_rate_at_k": 0.9230769230769231, "precision_at_1": 0.6538461538461539, "precision_at_3": 0.37179487179487186, "precision_at_5": 0.3224358974358974, "precision_at_k": 0.04807692307692308, "mrr_at_k": 0.7724358974358974, "ndcg_at_k": 0.15384615384615385, "f1_score": 0.6538461538461539, "avg_query_time_ms": 1722.0002321096567, "max_query_time_ms": 2185.1248741149902, "min_query_time_ms": 1328.3441066741943}, "content_matching": {"total_queries": 26, "exact_match_count": 21, "exact_match_rate": 0.8076923076923077, "avg_k": 4.461538461538462, "success_rate_at_1": 0.5384615384615384, "success_rate_at_3": 0.7692307692307693, "success_rate_at_5": 0.8076923076923077, "success_rate_at_k": 0.8076923076923077, "precision_at_1": 0.5384615384615384, "precision_at_3": 0.3333333333333333, "precision_at_5": 0.29423076923076924, "precision_at_k": 0.04807692307692308, "mrr_at_k": 0.6506410256410255, "ndcg_at_k": 0.15384615384615385, "f1_score": 0.5384615384615384, "avg_query_time_ms": 1722.0002321096567, "max_query_time_ms": 2185.1248741149902, "min_query_time_ms": 1328.3441066741943}, "total_queries": 26, "exact_match_count": 25, "exact_match_rate": 0.9615384615384616, "avg_k": 4.461538461538462, "success_rate_at_1": 0.6538461538461539, "success_rate_at_3": 0.8846153846153846, "success_rate_at_5": 0.9230769230769231, "success_rate_at_k": 0.9230769230769231, "precision_at_1": 0.6538461538461539, "precision_at_3": 0.37179487179487186, "precision_at_5": 0.3224358974358974, "precision_at_k": 0.04807692307692308, "mrr_at_k": 0.7724358974358974, "ndcg_at_k": 0.15384615384615385, "f1_score": 0.6538461538461539, "avg_query_time_ms": 1722.0002321096567, "max_query_time_ms": 2185.1248741149902, "min_query_time_ms": 1328.3441066741943}, "debugging": {"path_matching": {"total_queries": 26, "exact_match_count": 25, "exact_match_rate": 0.9615384615384616, "avg_k": 5.076923076923077, "success_rate_at_1": 0.6538461538461539, "success_rate_at_3": 0.8461538461538461, "success_rate_at_5": 0.9230769230769231, "success_rate_at_k": 0.9230769230769231, "precision_at_1": 0.6538461538461539, "precision_at_3": 0.38461538461538464, "precision_at_5": 0.3576923076923076, "precision_at_k": 0.3576923076923076, "mrr_at_k": 0.7728021978021977, "ndcg_at_k": 0.80662223589097, "f1_score": 0.6538461538461539, "avg_query_time_ms": 1915.5981724078838, "max_query_time_ms": 3731.332778930664, "min_query_time_ms": 1477.8571128845215}, "content_matching": {"total_queries": 26, "exact_match_count": 21, "exact_match_rate": 0.8076923076923077, "avg_k": 5.076923076923077, "success_rate_at_1": 0.5, "success_rate_at_3": 0.7692307692307693, "success_rate_at_5": 0.7692307692307693, "success_rate_at_k": 0.7692307692307693, "precision_at_1": 0.5, "precision_at_3": 0.35256410256410253, "precision_at_5": 0.31025641025641026, "precision_at_k": 0.31025641025641026, "mrr_at_k": 0.6324786324786325, "ndcg_at_k": 0.6648299431318748, "f1_score": 0.5, "avg_query_time_ms": 1915.5981724078838, "max_query_time_ms": 3731.332778930664, "min_query_time_ms": 1477.8571128845215}, "total_queries": 26, "exact_match_count": 25, "exact_match_rate": 0.9615384615384616, "avg_k": 5.076923076923077, "success_rate_at_1": 0.6538461538461539, "success_rate_at_3": 0.8461538461538461, "success_rate_at_5": 0.9230769230769231, "success_rate_at_k": 0.9230769230769231, "precision_at_1": 0.6538461538461539, "precision_at_3": 0.38461538461538464, "precision_at_5": 0.3576923076923076, "precision_at_k": 0.3576923076923076, "mrr_at_k": 0.7728021978021977, "ndcg_at_k": 0.80662223589097, "f1_score": 0.6538461538461539, "avg_query_time_ms": 1915.5981724078838, "max_query_time_ms": 3731.332778930664, "min_query_time_ms": 1477.8571128845215}, "optimization": {"path_matching": {"total_queries": 24, "exact_match_count": 19, "exact_match_rate": 0.7916666666666666, "avg_k": 3.9583333333333335, "success_rate_at_1": 0.5, "success_rate_at_3": 0.7083333333333334, "success_rate_at_5": 0.75, "success_rate_at_k": 0.75, "precision_at_1": 0.5, "precision_at_3": 0.34722222222222215, "precision_at_5": 0.32222222222222224, "precision_at_k": 0.0, "mrr_at_k": 0.6215277777777778, "ndcg_at_k": 0.0, "f1_score": 0.5, "avg_query_time_ms": 1870.9357877572377, "max_query_time_ms": 2326.8980979919434, "min_query_time_ms": 1406.6109657287598}, "content_matching": {"total_queries": 24, "exact_match_count": 18, "exact_match_rate": 0.75, "avg_k": 3.9583333333333335, "success_rate_at_1": 0.4583333333333333, "success_rate_at_3": 0.625, "success_rate_at_5": 0.625, "success_rate_at_k": 0.625, "precision_at_1": 0.4583333333333333, "precision_at_3": 0.3194444444444444, "precision_at_5": 0.2916666666666667, "precision_at_k": 0.0, "mrr_at_k": 0.5590277777777778, "ndcg_at_k": 0.0, "f1_score": 0.4583333333333333, "avg_query_time_ms": 1870.9357877572377, "max_query_time_ms": 2326.8980979919434, "min_query_time_ms": 1406.6109657287598}, "total_queries": 24, "exact_match_count": 19, "exact_match_rate": 0.7916666666666666, "avg_k": 3.9583333333333335, "success_rate_at_1": 0.5, "success_rate_at_3": 0.7083333333333334, "success_rate_at_5": 0.75, "success_rate_at_k": 0.75, "precision_at_1": 0.5, "precision_at_3": 0.34722222222222215, "precision_at_5": 0.32222222222222224, "precision_at_k": 0.0, "mrr_at_k": 0.6215277777777778, "ndcg_at_k": 0.0, "f1_score": 0.5, "avg_query_time_ms": 1870.9357877572377, "max_query_time_ms": 2326.8980979919434, "min_query_time_ms": 1406.6109657287598}, "testing": {"path_matching": {"total_queries": 24, "exact_match_count": 20, "exact_match_rate": 0.8333333333333334, "avg_k": 4.375, "success_rate_at_1": 0.4583333333333333, "success_rate_at_3": 0.625, "success_rate_at_5": 0.7083333333333334, "success_rate_at_k": 0.6666666666666666, "precision_at_1": 0.4583333333333333, "precision_at_3": 0.3194444444444444, "precision_at_5": 0.31250000000000006, "precision_at_k": 0.020833333333333332, "mrr_at_k": 0.5709986772486773, "ndcg_at_k": 0.06795540639881073, "f1_score": 0.4583333333333333, "avg_query_time_ms": 1868.2188987731934, "max_query_time_ms": 3065.013885498047, "min_query_time_ms": 1428.9181232452393}, "content_matching": {"total_queries": 24, "exact_match_count": 15, "exact_match_rate": 0.625, "avg_k": 4.375, "success_rate_at_1": 0.3333333333333333, "success_rate_at_3": 0.5416666666666666, "success_rate_at_5": 0.625, "success_rate_at_k": 0.5416666666666666, "precision_at_1": 0.3333333333333333, "precision_at_3": 0.2847222222222222, "precision_at_5": 0.28125000000000006, "precision_at_k": 0.010416666666666666, "mrr_at_k": 0.4402777777777777, "ndcg_at_k": 0.041666666666666664, "f1_score": 0.3333333333333333, "avg_query_time_ms": 1868.2188987731934, "max_query_time_ms": 3065.013885498047, "min_query_time_ms": 1428.9181232452393}, "total_queries": 24, "exact_match_count": 20, "exact_match_rate": 0.8333333333333334, "avg_k": 4.375, "success_rate_at_1": 0.4583333333333333, "success_rate_at_3": 0.625, "success_rate_at_5": 0.7083333333333334, "success_rate_at_k": 0.6666666666666666, "precision_at_1": 0.4583333333333333, "precision_at_3": 0.3194444444444444, "precision_at_5": 0.31250000000000006, "precision_at_k": 0.020833333333333332, "mrr_at_k": 0.5709986772486773, "ndcg_at_k": 0.06795540639881073, "f1_score": 0.4583333333333333, "avg_query_time_ms": 1868.2188987731934, "max_query_time_ms": 3065.013885498047, "min_query_time_ms": 1428.9181232452393}, "integration": {"path_matching": {"total_queries": 22, "exact_match_count": 17, "exact_match_rate": 0.7727272727272727, "avg_k": 6.2272727272727275, "success_rate_at_1": 0.45454545454545453, "success_rate_at_3": 0.7272727272727273, "success_rate_at_5": 0.7272727272727273, "success_rate_at_k": 0.7727272727272727, "precision_at_1": 0.45454545454545453, "precision_at_3": 0.2651515151515151, "precision_at_5": 0.2030303030303031, "precision_at_k": 0.007575757575757575, "mrr_at_k": 0.5833333333333333, "ndcg_at_k": 0.045454545454545456, "f1_score": 0.45454545454545453, "avg_query_time_ms": 1835.7841101559725, "max_query_time_ms": 3310.4450702667236, "min_query_time_ms": 1290.815830230713}, "content_matching": {"total_queries": 22, "exact_match_count": 15, "exact_match_rate": 0.6818181818181818, "avg_k": 6.2272727272727275, "success_rate_at_1": 0.36363636363636365, "success_rate_at_3": 0.6363636363636364, "success_rate_at_5": 0.6363636363636364, "success_rate_at_k": 0.6818181818181818, "precision_at_1": 0.36363636363636365, "precision_at_3": 0.22727272727272724, "precision_at_5": 0.17121212121212126, "precision_at_k": 0.007575757575757575, "mrr_at_k": 0.49242424242424243, "ndcg_at_k": 0.045454545454545456, "f1_score": 0.36363636363636365, "avg_query_time_ms": 1835.7841101559725, "max_query_time_ms": 3310.4450702667236, "min_query_time_ms": 1290.815830230713}, "total_queries": 22, "exact_match_count": 17, "exact_match_rate": 0.7727272727272727, "avg_k": 6.2272727272727275, "success_rate_at_1": 0.45454545454545453, "success_rate_at_3": 0.7272727272727273, "success_rate_at_5": 0.7272727272727273, "success_rate_at_k": 0.7727272727272727, "precision_at_1": 0.45454545454545453, "precision_at_3": 0.2651515151515151, "precision_at_5": 0.2030303030303031, "precision_at_k": 0.007575757575757575, "mrr_at_k": 0.5833333333333333, "ndcg_at_k": 0.045454545454545456, "f1_score": 0.45454545454545453, "avg_query_time_ms": 1835.7841101559725, "max_query_time_ms": 3310.4450702667236, "min_query_time_ms": 1290.815830230713}}, "oh-bill": {"understanding": {"path_matching": {"total_queries": 3, "exact_match_count": 2, "exact_match_rate": 0.6666666666666666, "avg_k": 2.6666666666666665, "success_rate_at_1": 0.6666666666666666, "success_rate_at_3": 0.6666666666666666, "success_rate_at_5": 0.6666666666666666, "success_rate_at_k": 0.6666666666666666, "precision_at_1": 0.6666666666666666, "precision_at_3": 0.5, "precision_at_5": 0.5, "precision_at_k": 0.5, "mrr_at_k": 0.6666666666666666, "ndcg_at_k": 0.6666666666666666, "f1_score": 0.6666666666666666, "avg_query_time_ms": 1649.1199334462483, "max_query_time_ms": 1978.9788722991943, "min_query_time_ms": 1471.1260795593262}, "content_matching": {"total_queries": 3, "exact_match_count": 2, "exact_match_rate": 0.6666666666666666, "avg_k": 2.6666666666666665, "success_rate_at_1": 0.6666666666666666, "success_rate_at_3": 0.6666666666666666, "success_rate_at_5": 0.6666666666666666, "success_rate_at_k": 0.6666666666666666, "precision_at_1": 0.6666666666666666, "precision_at_3": 0.5, "precision_at_5": 0.5, "precision_at_k": 0.5, "mrr_at_k": 0.6666666666666666, "ndcg_at_k": 0.6666666666666666, "f1_score": 0.6666666666666666, "avg_query_time_ms": 1649.1199334462483, "max_query_time_ms": 1978.9788722991943, "min_query_time_ms": 1471.1260795593262}, "total_queries": 3, "exact_match_count": 2, "exact_match_rate": 0.6666666666666666, "avg_k": 2.6666666666666665, "success_rate_at_1": 0.6666666666666666, "success_rate_at_3": 0.6666666666666666, "success_rate_at_5": 0.6666666666666666, "success_rate_at_k": 0.6666666666666666, "precision_at_1": 0.6666666666666666, "precision_at_3": 0.5, "precision_at_5": 0.5, "precision_at_k": 0.5, "mrr_at_k": 0.6666666666666666, "ndcg_at_k": 0.6666666666666666, "f1_score": 0.6666666666666666, "avg_query_time_ms": 1649.1199334462483, "max_query_time_ms": 1978.9788722991943, "min_query_time_ms": 1471.1260795593262}, "implementation": {"path_matching": {"total_queries": 3, "exact_match_count": 2, "exact_match_rate": 0.6666666666666666, "avg_k": 4.333333333333333, "success_rate_at_1": 0.3333333333333333, "success_rate_at_3": 0.6666666666666666, "success_rate_at_5": 0.6666666666666666, "success_rate_at_k": 0.6666666666666666, "precision_at_1": 0.3333333333333333, "precision_at_3": 0.4444444444444444, "precision_at_5": 0.4444444444444444, "precision_at_k": 0.0, "mrr_at_k": 0.4444444444444444, "ndcg_at_k": 0.0, "f1_score": 0.3333333333333333, "avg_query_time_ms": 1602.068583170573, "max_query_time_ms": 1975.8799076080322, "min_query_time_ms": 1225.1720428466797}, "content_matching": {"total_queries": 3, "exact_match_count": 2, "exact_match_rate": 0.6666666666666666, "avg_k": 4.333333333333333, "success_rate_at_1": 0.3333333333333333, "success_rate_at_3": 0.6666666666666666, "success_rate_at_5": 0.6666666666666666, "success_rate_at_k": 0.6666666666666666, "precision_at_1": 0.3333333333333333, "precision_at_3": 0.4444444444444444, "precision_at_5": 0.4444444444444444, "precision_at_k": 0.0, "mrr_at_k": 0.4444444444444444, "ndcg_at_k": 0.0, "f1_score": 0.3333333333333333, "avg_query_time_ms": 1602.068583170573, "max_query_time_ms": 1975.8799076080322, "min_query_time_ms": 1225.1720428466797}, "total_queries": 3, "exact_match_count": 2, "exact_match_rate": 0.6666666666666666, "avg_k": 4.333333333333333, "success_rate_at_1": 0.3333333333333333, "success_rate_at_3": 0.6666666666666666, "success_rate_at_5": 0.6666666666666666, "success_rate_at_k": 0.6666666666666666, "precision_at_1": 0.3333333333333333, "precision_at_3": 0.4444444444444444, "precision_at_5": 0.4444444444444444, "precision_at_k": 0.0, "mrr_at_k": 0.4444444444444444, "ndcg_at_k": 0.0, "f1_score": 0.3333333333333333, "avg_query_time_ms": 1602.068583170573, "max_query_time_ms": 1975.8799076080322, "min_query_time_ms": 1225.1720428466797}, "debugging": {"path_matching": {"total_queries": 3, "exact_match_count": 3, "exact_match_rate": 1.0, "avg_k": 1.6666666666666667, "success_rate_at_1": 1.0, "success_rate_at_3": 1.0, "success_rate_at_5": 1.0, "success_rate_at_k": 1.0, "precision_at_1": 1.0, "precision_at_3": 0.6666666666666666, "precision_at_5": 0.6666666666666666, "precision_at_k": 0.3333333333333333, "mrr_at_k": 1.0, "ndcg_at_k": 0.6666666666666666, "f1_score": 1.0, "avg_query_time_ms": 1716.9926166534424, "max_query_time_ms": 1945.9669589996338, "min_query_time_ms": 1438.1239414215088}, "content_matching": {"total_queries": 3, "exact_match_count": 3, "exact_match_rate": 1.0, "avg_k": 1.6666666666666667, "success_rate_at_1": 1.0, "success_rate_at_3": 1.0, "success_rate_at_5": 1.0, "success_rate_at_k": 1.0, "precision_at_1": 1.0, "precision_at_3": 0.6666666666666666, "precision_at_5": 0.6666666666666666, "precision_at_k": 0.3333333333333333, "mrr_at_k": 1.0, "ndcg_at_k": 0.6666666666666666, "f1_score": 1.0, "avg_query_time_ms": 1716.9926166534424, "max_query_time_ms": 1945.9669589996338, "min_query_time_ms": 1438.1239414215088}, "total_queries": 3, "exact_match_count": 3, "exact_match_rate": 1.0, "avg_k": 1.6666666666666667, "success_rate_at_1": 1.0, "success_rate_at_3": 1.0, "success_rate_at_5": 1.0, "success_rate_at_k": 1.0, "precision_at_1": 1.0, "precision_at_3": 0.6666666666666666, "precision_at_5": 0.6666666666666666, "precision_at_k": 0.3333333333333333, "mrr_at_k": 1.0, "ndcg_at_k": 0.6666666666666666, "f1_score": 1.0, "avg_query_time_ms": 1716.9926166534424, "max_query_time_ms": 1945.9669589996338, "min_query_time_ms": 1438.1239414215088}, "testing": {"path_matching": {"total_queries": 3, "exact_match_count": 2, "exact_match_rate": 0.6666666666666666, "avg_k": 1.3333333333333333, "success_rate_at_1": 0.6666666666666666, "success_rate_at_3": 0.6666666666666666, "success_rate_at_5": 0.6666666666666666, "success_rate_at_k": 0.6666666666666666, "precision_at_1": 0.6666666666666666, "precision_at_3": 0.5, "precision_at_5": 0.5, "precision_at_k": 0.6666666666666666, "mrr_at_k": 0.6666666666666666, "ndcg_at_k": 0.6666666666666666, "f1_score": 0.6666666666666666, "avg_query_time_ms": 2065.161387125651, "max_query_time_ms": 3329.26607131958, "min_query_time_ms": 1381.822109222412}, "content_matching": {"total_queries": 3, "exact_match_count": 2, "exact_match_rate": 0.6666666666666666, "avg_k": 1.3333333333333333, "success_rate_at_1": 0.6666666666666666, "success_rate_at_3": 0.6666666666666666, "success_rate_at_5": 0.6666666666666666, "success_rate_at_k": 0.6666666666666666, "precision_at_1": 0.6666666666666666, "precision_at_3": 0.5, "precision_at_5": 0.5, "precision_at_k": 0.6666666666666666, "mrr_at_k": 0.6666666666666666, "ndcg_at_k": 0.6666666666666666, "f1_score": 0.6666666666666666, "avg_query_time_ms": 2065.161387125651, "max_query_time_ms": 3329.26607131958, "min_query_time_ms": 1381.822109222412}, "total_queries": 3, "exact_match_count": 2, "exact_match_rate": 0.6666666666666666, "avg_k": 1.3333333333333333, "success_rate_at_1": 0.6666666666666666, "success_rate_at_3": 0.6666666666666666, "success_rate_at_5": 0.6666666666666666, "success_rate_at_k": 0.6666666666666666, "precision_at_1": 0.6666666666666666, "precision_at_3": 0.5, "precision_at_5": 0.5, "precision_at_k": 0.6666666666666666, "mrr_at_k": 0.6666666666666666, "ndcg_at_k": 0.6666666666666666, "f1_score": 0.6666666666666666, "avg_query_time_ms": 2065.161387125651, "max_query_time_ms": 3329.26607131958, "min_query_time_ms": 1381.822109222412}, "integration": {"path_matching": {"total_queries": 3, "exact_match_count": 0, "exact_match_rate": 0.0, "avg_k": 1.6666666666666667, "success_rate_at_1": 0.0, "success_rate_at_3": 0.0, "success_rate_at_5": 0.0, "success_rate_at_k": 0.0, "precision_at_1": 0.0, "precision_at_3": 0.0, "precision_at_5": 0.0, "precision_at_k": 0.0, "mrr_at_k": 0.0, "ndcg_at_k": 0.0, "f1_score": 0.0, "avg_query_time_ms": 1897.553284962972, "max_query_time_ms": 2371.967077255249, "min_query_time_ms": 1522.899866104126}, "content_matching": {"total_queries": 3, "exact_match_count": 0, "exact_match_rate": 0.0, "avg_k": 1.6666666666666667, "success_rate_at_1": 0.0, "success_rate_at_3": 0.0, "success_rate_at_5": 0.0, "success_rate_at_k": 0.0, "precision_at_1": 0.0, "precision_at_3": 0.0, "precision_at_5": 0.0, "precision_at_k": 0.0, "mrr_at_k": 0.0, "ndcg_at_k": 0.0, "f1_score": 0.0, "avg_query_time_ms": 1897.553284962972, "max_query_time_ms": 2371.967077255249, "min_query_time_ms": 1522.899866104126}, "total_queries": 3, "exact_match_count": 0, "exact_match_rate": 0.0, "avg_k": 1.6666666666666667, "success_rate_at_1": 0.0, "success_rate_at_3": 0.0, "success_rate_at_5": 0.0, "success_rate_at_k": 0.0, "precision_at_1": 0.0, "precision_at_3": 0.0, "precision_at_5": 0.0, "precision_at_k": 0.0, "mrr_at_k": 0.0, "ndcg_at_k": 0.0, "f1_score": 0.0, "avg_query_time_ms": 1897.553284962972, "max_query_time_ms": 2371.967077255249, "min_query_time_ms": 1522.899866104126}, "optimization": {"path_matching": {"total_queries": 2, "exact_match_count": 2, "exact_match_rate": 1.0, "avg_k": 2.0, "success_rate_at_1": 1.0, "success_rate_at_3": 1.0, "success_rate_at_5": 1.0, "success_rate_at_k": 1.0, "precision_at_1": 1.0, "precision_at_3": 0.6666666666666666, "precision_at_5": 0.6666666666666666, "precision_at_k": 0.0, "mrr_at_k": 1.0, "ndcg_at_k": 0.0, "f1_score": 1.0, "avg_query_time_ms": 2166.1568880081177, "max_query_time_ms": 2443.711996078491, "min_query_time_ms": 1888.6017799377441}, "content_matching": {"total_queries": 2, "exact_match_count": 2, "exact_match_rate": 1.0, "avg_k": 2.0, "success_rate_at_1": 1.0, "success_rate_at_3": 1.0, "success_rate_at_5": 1.0, "success_rate_at_k": 1.0, "precision_at_1": 1.0, "precision_at_3": 0.6666666666666666, "precision_at_5": 0.6666666666666666, "precision_at_k": 0.0, "mrr_at_k": 1.0, "ndcg_at_k": 0.0, "f1_score": 1.0, "avg_query_time_ms": 2166.1568880081177, "max_query_time_ms": 2443.711996078491, "min_query_time_ms": 1888.6017799377441}, "total_queries": 2, "exact_match_count": 2, "exact_match_rate": 1.0, "avg_k": 2.0, "success_rate_at_1": 1.0, "success_rate_at_3": 1.0, "success_rate_at_5": 1.0, "success_rate_at_k": 1.0, "precision_at_1": 1.0, "precision_at_3": 0.6666666666666666, "precision_at_5": 0.6666666666666666, "precision_at_k": 0.0, "mrr_at_k": 1.0, "ndcg_at_k": 0.0, "f1_score": 1.0, "avg_query_time_ms": 2166.1568880081177, "max_query_time_ms": 2443.711996078491, "min_query_time_ms": 1888.6017799377441}}, "music-gathering": {"implementation": {"path_matching": {"total_queries": 4, "exact_match_count": 3, "exact_match_rate": 0.75, "avg_k": 5.5, "success_rate_at_1": 0.25, "success_rate_at_3": 0.75, "success_rate_at_5": 0.75, "success_rate_at_k": 0.75, "precision_at_1": 0.25, "precision_at_3": 0.41666666666666663, "precision_at_5": 0.35, "precision_at_k": 0.0, "mrr_at_k": 0.4583333333333333, "ndcg_at_k": 0.0, "f1_score": 0.25, "avg_query_time_ms": 1714.985966682434, "max_query_time_ms": 2100.040912628174, "min_query_time_ms": 1413.999080657959}, "content_matching": {"total_queries": 4, "exact_match_count": 3, "exact_match_rate": 0.75, "avg_k": 5.5, "success_rate_at_1": 0.25, "success_rate_at_3": 0.75, "success_rate_at_5": 0.75, "success_rate_at_k": 0.75, "precision_at_1": 0.25, "precision_at_3": 0.41666666666666663, "precision_at_5": 0.35, "precision_at_k": 0.0, "mrr_at_k": 0.4583333333333333, "ndcg_at_k": 0.0, "f1_score": 0.25, "avg_query_time_ms": 1714.985966682434, "max_query_time_ms": 2100.040912628174, "min_query_time_ms": 1413.999080657959}, "total_queries": 4, "exact_match_count": 3, "exact_match_rate": 0.75, "avg_k": 5.5, "success_rate_at_1": 0.25, "success_rate_at_3": 0.75, "success_rate_at_5": 0.75, "success_rate_at_k": 0.75, "precision_at_1": 0.25, "precision_at_3": 0.41666666666666663, "precision_at_5": 0.35, "precision_at_k": 0.0, "mrr_at_k": 0.4583333333333333, "ndcg_at_k": 0.0, "f1_score": 0.25, "avg_query_time_ms": 1714.985966682434, "max_query_time_ms": 2100.040912628174, "min_query_time_ms": 1413.999080657959}, "debugging": {"path_matching": {"total_queries": 3, "exact_match_count": 3, "exact_match_rate": 1.0, "avg_k": 6.0, "success_rate_at_1": 0.3333333333333333, "success_rate_at_3": 0.3333333333333333, "success_rate_at_5": 0.6666666666666666, "success_rate_at_k": 0.6666666666666666, "precision_at_1": 0.3333333333333333, "precision_at_3": 0.3333333333333333, "precision_at_5": 0.39999999999999997, "precision_at_k": 0.0, "mrr_at_k": 0.4476190476190476, "ndcg_at_k": 0.0, "f1_score": 0.3333333333333333, "avg_query_time_ms": 1975.64697265625, "max_query_time_ms": 2834.0370655059814, "min_query_time_ms": 1272.8989124298096}, "content_matching": {"total_queries": 3, "exact_match_count": 3, "exact_match_rate": 1.0, "avg_k": 6.0, "success_rate_at_1": 0.3333333333333333, "success_rate_at_3": 0.3333333333333333, "success_rate_at_5": 0.6666666666666666, "success_rate_at_k": 0.6666666666666666, "precision_at_1": 0.3333333333333333, "precision_at_3": 0.3333333333333333, "precision_at_5": 0.39999999999999997, "precision_at_k": 0.0, "mrr_at_k": 0.4476190476190476, "ndcg_at_k": 0.0, "f1_score": 0.3333333333333333, "avg_query_time_ms": 1975.64697265625, "max_query_time_ms": 2834.0370655059814, "min_query_time_ms": 1272.8989124298096}, "total_queries": 3, "exact_match_count": 3, "exact_match_rate": 1.0, "avg_k": 6.0, "success_rate_at_1": 0.3333333333333333, "success_rate_at_3": 0.3333333333333333, "success_rate_at_5": 0.6666666666666666, "success_rate_at_k": 0.6666666666666666, "precision_at_1": 0.3333333333333333, "precision_at_3": 0.3333333333333333, "precision_at_5": 0.39999999999999997, "precision_at_k": 0.0, "mrr_at_k": 0.4476190476190476, "ndcg_at_k": 0.0, "f1_score": 0.3333333333333333, "avg_query_time_ms": 1975.64697265625, "max_query_time_ms": 2834.0370655059814, "min_query_time_ms": 1272.8989124298096}, "optimization": {"path_matching": {"total_queries": 4, "exact_match_count": 3, "exact_match_rate": 0.75, "avg_k": 3.25, "success_rate_at_1": 0.25, "success_rate_at_3": 0.5, "success_rate_at_5": 0.75, "success_rate_at_k": 0.5, "precision_at_1": 0.25, "precision_at_3": 0.20833333333333331, "precision_at_5": 0.2375, "precision_at_k": 0.20833333333333331, "mrr_at_k": 0.4375, "ndcg_at_k": 0.4077324383928644, "f1_score": 0.25, "avg_query_time_ms": 1527.1849036216736, "max_query_time_ms": 1928.4818172454834, "min_query_time_ms": 1226.1531352996826}, "content_matching": {"total_queries": 4, "exact_match_count": 3, "exact_match_rate": 0.75, "avg_k": 3.25, "success_rate_at_1": 0.25, "success_rate_at_3": 0.5, "success_rate_at_5": 0.75, "success_rate_at_k": 0.5, "precision_at_1": 0.25, "precision_at_3": 0.20833333333333331, "precision_at_5": 0.2375, "precision_at_k": 0.20833333333333331, "mrr_at_k": 0.4375, "ndcg_at_k": 0.4077324383928644, "f1_score": 0.25, "avg_query_time_ms": 1527.1849036216736, "max_query_time_ms": 1928.4818172454834, "min_query_time_ms": 1226.1531352996826}, "total_queries": 4, "exact_match_count": 3, "exact_match_rate": 0.75, "avg_k": 3.25, "success_rate_at_1": 0.25, "success_rate_at_3": 0.5, "success_rate_at_5": 0.75, "success_rate_at_k": 0.5, "precision_at_1": 0.25, "precision_at_3": 0.20833333333333331, "precision_at_5": 0.2375, "precision_at_k": 0.20833333333333331, "mrr_at_k": 0.4375, "ndcg_at_k": 0.4077324383928644, "f1_score": 0.25, "avg_query_time_ms": 1527.1849036216736, "max_query_time_ms": 1928.4818172454834, "min_query_time_ms": 1226.1531352996826}, "understanding": {"path_matching": {"total_queries": 3, "exact_match_count": 2, "exact_match_rate": 0.6666666666666666, "avg_k": 4.0, "success_rate_at_1": 0.6666666666666666, "success_rate_at_3": 0.6666666666666666, "success_rate_at_5": 0.6666666666666666, "success_rate_at_k": 0.6666666666666666, "precision_at_1": 0.6666666666666666, "precision_at_3": 0.4444444444444444, "precision_at_5": 0.39999999999999997, "precision_at_k": 0.0, "mrr_at_k": 0.6666666666666666, "ndcg_at_k": 0.0, "f1_score": 0.6666666666666666, "avg_query_time_ms": 1993.731419245402, "max_query_time_ms": 2323.3296871185303, "min_query_time_ms": 1534.4786643981934}, "content_matching": {"total_queries": 3, "exact_match_count": 2, "exact_match_rate": 0.6666666666666666, "avg_k": 4.0, "success_rate_at_1": 0.6666666666666666, "success_rate_at_3": 0.6666666666666666, "success_rate_at_5": 0.6666666666666666, "success_rate_at_k": 0.6666666666666666, "precision_at_1": 0.6666666666666666, "precision_at_3": 0.4444444444444444, "precision_at_5": 0.39999999999999997, "precision_at_k": 0.0, "mrr_at_k": 0.6666666666666666, "ndcg_at_k": 0.0, "f1_score": 0.6666666666666666, "avg_query_time_ms": 1993.731419245402, "max_query_time_ms": 2323.3296871185303, "min_query_time_ms": 1534.4786643981934}, "total_queries": 3, "exact_match_count": 2, "exact_match_rate": 0.6666666666666666, "avg_k": 4.0, "success_rate_at_1": 0.6666666666666666, "success_rate_at_3": 0.6666666666666666, "success_rate_at_5": 0.6666666666666666, "success_rate_at_k": 0.6666666666666666, "precision_at_1": 0.6666666666666666, "precision_at_3": 0.4444444444444444, "precision_at_5": 0.39999999999999997, "precision_at_k": 0.0, "mrr_at_k": 0.6666666666666666, "ndcg_at_k": 0.0, "f1_score": 0.6666666666666666, "avg_query_time_ms": 1993.731419245402, "max_query_time_ms": 2323.3296871185303, "min_query_time_ms": 1534.4786643981934}, "testing": {"path_matching": {"total_queries": 2, "exact_match_count": 2, "exact_match_rate": 1.0, "avg_k": 5.5, "success_rate_at_1": 0.5, "success_rate_at_3": 0.5, "success_rate_at_5": 0.5, "success_rate_at_k": 1.0, "precision_at_1": 0.5, "precision_at_3": 0.5, "precision_at_5": 0.5, "precision_at_k": 0.0, "mrr_at_k": 0.5833333333333334, "ndcg_at_k": 0.0, "f1_score": 0.5, "avg_query_time_ms": 2003.2546520233154, "max_query_time_ms": 2063.1091594696045, "min_query_time_ms": 1943.4001445770264}, "content_matching": {"total_queries": 2, "exact_match_count": 2, "exact_match_rate": 1.0, "avg_k": 5.5, "success_rate_at_1": 0.5, "success_rate_at_3": 0.5, "success_rate_at_5": 0.5, "success_rate_at_k": 1.0, "precision_at_1": 0.5, "precision_at_3": 0.5, "precision_at_5": 0.5, "precision_at_k": 0.0, "mrr_at_k": 0.5833333333333334, "ndcg_at_k": 0.0, "f1_score": 0.5, "avg_query_time_ms": 2003.2546520233154, "max_query_time_ms": 2063.1091594696045, "min_query_time_ms": 1943.4001445770264}, "total_queries": 2, "exact_match_count": 2, "exact_match_rate": 1.0, "avg_k": 5.5, "success_rate_at_1": 0.5, "success_rate_at_3": 0.5, "success_rate_at_5": 0.5, "success_rate_at_k": 1.0, "precision_at_1": 0.5, "precision_at_3": 0.5, "precision_at_5": 0.5, "precision_at_k": 0.0, "mrr_at_k": 0.5833333333333334, "ndcg_at_k": 0.0, "f1_score": 0.5, "avg_query_time_ms": 2003.2546520233154, "max_query_time_ms": 2063.1091594696045, "min_query_time_ms": 1943.4001445770264}, "integration": {"path_matching": {"total_queries": 2, "exact_match_count": 1, "exact_match_rate": 0.5, "avg_k": 7.5, "success_rate_at_1": 0.0, "success_rate_at_3": 0.5, "success_rate_at_5": 0.5, "success_rate_at_k": 0.5, "precision_at_1": 0.0, "precision_at_3": 0.16666666666666666, "precision_at_5": 0.1, "precision_at_k": 0.0, "mrr_at_k": 0.25, "ndcg_at_k": 0.0, "f1_score": 0.0, "avg_query_time_ms": 1832.9434394836426, "max_query_time_ms": 1883.7308883666992, "min_query_time_ms": 1782.155990600586}, "content_matching": {"total_queries": 2, "exact_match_count": 1, "exact_match_rate": 0.5, "avg_k": 7.5, "success_rate_at_1": 0.0, "success_rate_at_3": 0.5, "success_rate_at_5": 0.5, "success_rate_at_k": 0.5, "precision_at_1": 0.0, "precision_at_3": 0.16666666666666666, "precision_at_5": 0.1, "precision_at_k": 0.0, "mrr_at_k": 0.25, "ndcg_at_k": 0.0, "f1_score": 0.0, "avg_query_time_ms": 1832.9434394836426, "max_query_time_ms": 1883.7308883666992, "min_query_time_ms": 1782.155990600586}, "total_queries": 2, "exact_match_count": 1, "exact_match_rate": 0.5, "avg_k": 7.5, "success_rate_at_1": 0.0, "success_rate_at_3": 0.5, "success_rate_at_5": 0.5, "success_rate_at_k": 0.5, "precision_at_1": 0.0, "precision_at_3": 0.16666666666666666, "precision_at_5": 0.1, "precision_at_k": 0.0, "mrr_at_k": 0.25, "ndcg_at_k": 0.0, "f1_score": 0.0, "avg_query_time_ms": 1832.9434394836426, "max_query_time_ms": 1883.7308883666992, "min_query_time_ms": 1782.155990600586}}}, "source": "user", "total_time_seconds": 2202.0655360221863}