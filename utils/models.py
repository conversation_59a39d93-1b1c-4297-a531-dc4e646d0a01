"""
数据模型定义
定义系统中使用的核心数据结构
"""

from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from typing import Optional


@dataclass
class FileInfo:
    """文件信息"""

    file_path: str
    relative_path: str
    language: str
    content: str
    size: int
    crc32: str  # 文件内容的CRC32校验和


@dataclass
class CodeChunk:
    """代码分片"""

    id: str
    file_path: str
    relative_path: str
    start_line: int
    end_line: int
    content: str
    chunk_type: str  # function, class, method, interface, markdown_section
    symbol_name: str
    language: str
    insight: Optional[str] = None
    keywords: Optional[list[str]] = None
    embedding: Optional[list[float]] = None


@dataclass
class SearchResult:
    """搜索结果"""

    file_path: str
    line_number: int
    matched_text: str
    context: str


@dataclass
class QueryRequest:
    """查询请求"""

    query: str
    top_k: int = 10


@dataclass
class RetrievalResult:
    """检索结果"""

    file_path: str
    text: str
    score: float
    chunk_type: str
    symbol_name: str
    start_line: int
    end_line: int


@dataclass
class QueryResponse:
    """查询响应"""

    results: list[RetrievalResult]
    total_time_ms: float
    query: str
    total_chunks: int


@dataclass
class HealthResponse:
    """健康检查响应"""

    status: str
    message: str
    index_status: str
    total_chunks: int
    total_files: int


class ChunkType(Enum):
    """分片类型枚举"""

    FUNCTION = "function"
    CLASS = "class"
    METHOD = "method"
    INTERFACE = "interface"
    MARKDOWN_SECTION = "markdown_section"
    OTHER = "other"


class Language(Enum):
    """支持的编程语言"""

    PYTHON = "python"
    JAVASCRIPT = "javascript"
    TYPESCRIPT = "typescript"
    JAVA = "java"
    C = "c"
    CPP = "cpp"
    RUST = "rust"
    ARKTS = "arkts"
    MARKDOWN = "markdown"


# ============= Symbol Retriever 数据模型 =============


class SymbolType(Enum):
    """符号类型枚举"""

    FUNCTION = "function"
    METHOD = "method"
    CLASS = "class"
    INTERFACE = "interface"
    VARIABLE = "variable"
    CONSTANT = "constant"
    PROPERTY = "property"
    FIELD = "field"
    ENUM = "enum"
    NAMESPACE = "namespace"
    MODULE = "module"
    IMPORT = "import"
    TYPE_ALIAS = "type_alias"


class ReferenceType(Enum):
    """引用类型枚举"""

    CALL = "call"  # 函数调用
    INHERITANCE = "inheritance"  # 继承关系
    IMPLEMENTATION = "implementation"  # 接口实现
    IMPORT = "import"  # 导入引用
    ASSIGNMENT = "assignment"  # 赋值引用
    TYPE_ANNOTATION = "type_annotation"  # 类型注解
    INSTANTIATION = "instantiation"  # 实例化


@dataclass
class Parameter:
    """参数信息"""

    name: str
    param_type: Optional[str] = None
    default_value: Optional[str] = None
    is_optional: bool = False
    is_variadic: bool = False  # 可变参数 (*args, **kwargs)


@dataclass
class Reference:
    """引用信息"""

    file_path: str
    line_number: int
    column_number: int
    context: str  # 引用上下文代码
    reference_type: ReferenceType  # 引用类型


@dataclass
class Symbol:
    """符号数据模型"""

    id: str  # 唯一标识符
    name: str  # 符号名称
    symbol_type: SymbolType  # 符号类型
    file_path: str  # 文件路径
    start_line: int  # 开始行号
    end_line: int  # 结束行号
    start_column: int  # 开始列号
    end_column: int  # 结束列号

    # 上下文信息
    namespace: Optional[str] = None  # 命名空间
    module_path: str = ""  # 模块路径
    parent_symbol: Optional[str] = None  # 父符号ID（如所属类）

    # 符号详细信息
    signature: Optional[str] = None  # 函数签名或类型声明
    parameters: list[Parameter] = None  # 参数列表
    return_type: Optional[str] = None  # 返回值类型
    modifiers: list[str] = None  # 修饰符（public, private, static等）

    # 文档和注释
    docstring: Optional[str] = None  # 文档字符串
    comments: list[str] = None  # 相关注释

    # 关系信息
    dependencies: list[str] = None  # 依赖的符号ID列表
    references: list[Reference] = None  # 引用位置列表

    # 元数据
    language: str = ""  # 编程语言
    created_at: Optional[datetime] = None  # 创建时间
    updated_at: Optional[datetime] = None  # 更新时间

    def __post_init__(self):
        """初始化默认值"""
        if self.parameters is None:
            self.parameters = []
        if self.modifiers is None:
            self.modifiers = []
        if self.comments is None:
            self.comments = []
        if self.dependencies is None:
            self.dependencies = []
        if self.references is None:
            self.references = []
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.updated_at is None:
            self.updated_at = datetime.now()

    def get_qualified_name(self) -> str:
        """获取完全限定名称"""
        from .symbol_helpers import get_symbol_qualified_name

        return get_symbol_qualified_name(self)

    def get_context_path(self) -> str:
        """获取上下文路径"""
        from .symbol_helpers import get_symbol_context_path

        return get_symbol_context_path(self)

    def get_hierarchy_info(self) -> dict:
        """获取层级结构信息"""
        from .symbol_helpers import extract_symbol_hierarchy_info

        return extract_symbol_hierarchy_info(self)

    def is_in_namespace(self, namespace: str) -> bool:
        """检查符号是否在指定命名空间中"""
        if not self.namespace:
            return False
        return namespace.lower() in self.namespace.lower()

    def is_child_of(self, parent_name: str) -> bool:
        """检查符号是否是指定符号的子符号"""
        if not self.parent_symbol:
            return False
        # 这里需要通过索引来获取父符号信息，简化处理
        return parent_name.lower() in (self.namespace or "").lower()


@dataclass
class SymbolQuery:
    """符号查询请求"""

    query: str  # 查询字符串
    symbol_types: Optional[list[SymbolType]] = None  # 限制符号类型
    languages: Optional[list[str]] = None  # 限制编程语言
    context_path: Optional[str] = None  # 上下文路径（类名、模块名等）
    exact_match: bool = False  # 是否精确匹配
    include_references: bool = False  # 是否包含引用信息
    max_results: int = 50  # 最大结果数


@dataclass
class SymbolResult:
    """符号检索结果"""

    symbol: Symbol  # 符号信息
    score: float  # 匹配分数
    match_reason: str  # 匹配原因说明
    context_snippet: Optional[str] = None  # 上下文代码片段


@dataclass
class SymbolQueryResponse:
    """符号查询响应"""

    results: list[SymbolResult]
    total_time_ms: float
    query: SymbolQuery
    total_symbols: int


# ============= Symbol Retriever 异常类 =============


class SymbolExtractionError(Exception):
    """符号提取错误"""

    def __init__(self, message: str, file_path: str = None, line_number: int = None):
        self.message = message
        self.file_path = file_path
        self.line_number = line_number
        super().__init__(self.message)


class SymbolIndexError(Exception):
    """符号索引错误"""

    def __init__(self, message: str, symbol_id: str = None):
        self.message = message
        self.symbol_id = symbol_id
        super().__init__(self.message)


class SymbolRetrievalError(Exception):
    """符号检索错误"""

    def __init__(self, message: str, query: str = None):
        self.message = message
        self.query = query
        super().__init__(self.message)
