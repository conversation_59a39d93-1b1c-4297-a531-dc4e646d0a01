def ClearCommentsForC(
    code: str,
    clearComments=True,
    keepCommentsLoc=False,
    clearText=False,
    keepTextLoc=False,
) -> str:
    if not clearComments:
        keepCommentsLoc = False
    if not clearText:
        keepTextLoc = False
    if not (clearComments or keepCommentsLoc or clearText or keepTextLoc):
        return code

    斜杠 = 0  # /
    注释 = 0  # //
    花注释 = 0  # /* */
    星号 = 0  # *
    单引号 = 0  # '
    双引号 = 0  # " 进入引号模式后只判断是否有引号
    output = ""
    for char in code:
        if char == "\r":
            continue  # 有一些隐藏符号是不占列宽但是在字符串中占长度的, 忽略这些符号 TODO 后续补充
        if 注释 or 花注释:  # 进入注释模式后, 计数+内容全部屏蔽, 直到模式闭环
            if 注释:
                if char == "\n":
                    注释 = 0
                    output += "\n"
                    continue
                else:
                    if not clearComments:
                        output += char
                    elif keepCommentsLoc:
                        output += " "
                    continue
            elif 花注释:
                if char == "\n":
                    星号 = 0
                    output += "\n"
                    continue
                if 星号 and char == "/":
                    花注释 = 0
                    星号 = 0
                    if not clearComments:
                        output += char
                    elif keepCommentsLoc:
                        output += " "
                    continue
                if char == "*":
                    星号 = 1
                    if not clearComments:
                        output += char
                    elif keepCommentsLoc:
                        output += " "
                    continue
                else:
                    星号 = 0
                    if not clearComments:
                        output += char
                    elif keepCommentsLoc:
                        output += " "
                    continue

        elif (
            单引号 or 双引号
        ):  # 进入引号模式后, 计数全部屏蔽, 内容全部记录, 直到模式闭环
            if 单引号:
                if char == "'":
                    单引号 = 0
                if not clearText:
                    output += char
                elif keepTextLoc:
                    output += "\n" if char == "\n" else " "
                continue
            elif 双引号:
                if char == '"':
                    双引号 = 0
                if not clearText:
                    output += char
                elif keepTextLoc:
                    output += "\n" if char == "\n" else " "
                continue

        elif 斜杠:  # 斜杠还不知道是//还是/**/还是其他, 校验模式
            斜杠 = 0
            if char == "/":
                注释 = 1
                if not clearComments:
                    output += "//"
                elif keepCommentsLoc:
                    output += "  "  # 上个横线是没有+的, 这里补上两个然后continue
                continue
            elif char == "*":
                花注释 = 1
                if not clearComments:
                    output += "/*"
                elif keepCommentsLoc:
                    output += "  "  # 上个横线是没有+的, 这里补上两个然后continue
                continue
            else:
                output += "/"  # 上个斜杠是没有+的, 这里补上
                pass  # 啥也不是, pass吧

        # 以下校验均为模式入口, 建议统一continue
        elif char == "/":  # 如果是斜杠, 不+, 下个回合校验模式走完再说
            斜杠 = 1
            continue

        elif char == "'":  # 如果是引号, +, 并进入引号模式
            单引号 = 1
            if not clearText:
                output += char
            elif keepTextLoc:
                output += "\n" if char == "\n" else " "
            continue

        elif char == '"':  # 如果是引号, +, 并进入引号模式
            双引号 = 1
            if not clearText:
                output += char
            elif keepTextLoc:
                output += "\n" if char == "\n" else " "
            continue

        # 走到这儿说明不在注释或引号模式下, 正常+
        output += char

    return output
