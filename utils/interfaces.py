"""
接口定义
定义系统中各模块的抽象接口
"""

from abc import ABC, abstractmethod
from typing import Any, Optional

from .models import (
    CodeChunk,
    FileInfo,
    Reference,
    SearchResult,
    Symbol,
    SymbolQuery,
    SymbolResult,
    SymbolType,
)


class ICodebaseLoader(ABC):
    """代码库加载器接口"""

    @abstractmethod
    def load_files(self, repo_path: str) -> list[FileInfo]:
        """加载指定目录下的所有支持的文件"""
        pass

    @abstractmethod
    def filter_supported_files(self, files: list[str]) -> list[str]:
        """过滤出支持的文件类型"""
        pass


class ITreeSitterParser(ABC):
    """Tree-sitter解析器接口"""

    @abstractmethod
    def parse_file(self, file_info: FileInfo) -> Optional[object]:
        """解析文件并返回解析树"""
        pass

    @abstractmethod
    def get_supported_languages(self) -> list[str]:
        """获取支持的编程语言列表"""
        pass


class ICodeChunker(ABC):
    """代码分片器接口"""

    @abstractmethod
    def chunk_by_symbols(
        self, parse_tree: object, file_info: FileInfo
    ) -> list[CodeChunk]:
        """按符号级粒度分片代码"""
        pass

    @abstractmethod
    def chunk_markdown(self, file_info: FileInfo) -> list[CodeChunk]:
        """分片Markdown文件"""
        pass


class ICodeInsightGenerator(ABC):
    """代码洞察生成器接口"""

    @abstractmethod
    async def generate_insight(self, chunk: CodeChunk) -> str:
        """为代码分片生成洞察描述"""
        pass

    @abstractmethod
    async def batch_generate_insights(self, chunks: list[CodeChunk]) -> list[str]:
        """批量生成洞察描述"""
        pass


class IKeywordExtractor(ABC):
    """关键词提取器接口"""

    @abstractmethod
    def extract_keywords(self, chunk: CodeChunk) -> list[str]:
        """从代码分片中提取关键词"""
        pass

    @abstractmethod
    def extract_file_path_keywords(self, file_path: str) -> list[str]:
        """从文件路径中提取关键词"""
        pass


class IEmbeddingCalculator(ABC):
    """嵌入计算器接口"""

    @abstractmethod
    async def calculate_embedding(self, text: str) -> list[float]:
        """计算文本的嵌入向量"""
        pass

    @abstractmethod
    async def batch_calculate_embeddings(self, texts: list[str]) -> list[list[float]]:
        """批量计算嵌入向量"""
        pass


class IRepoIndex(ABC):
    """仓库索引接口"""

    @abstractmethod
    def add_chunk(self, chunk: CodeChunk):
        """添加代码分片到索引"""
        pass

    @abstractmethod
    def remove_chunks_by_file(self, file_path: str):
        """删除指定文件的所有分片"""
        pass

    @abstractmethod
    def get_repo_insight(self) -> str:
        """获取仓库整体洞察"""
        pass

    @abstractmethod
    def save_metadata(self, index_dir: str):
        """保存索引元数据"""
        pass

    @abstractmethod
    def save_file_index(self, index_dir: str, file_path: str):
        """保存单个文件的索引"""
        pass

    @abstractmethod
    def load_index(self, index_dir: str) -> bool:
        """从目录加载索引"""
        pass


class IQueryRewriter(ABC):
    """查询重写器接口"""

    @abstractmethod
    async def rewrite_to_ripgrep(self, query: str, repo_insight: str) -> list[str]:
        """将查询重写为ripgrep命令"""
        pass

    @abstractmethod
    def rewrite_to_embedding_query(self, query: str) -> str:
        """将查询重写为嵌入检索查询"""
        pass


class IRipgrepExecutor(ABC):
    """Ripgrep执行器接口"""

    @abstractmethod
    def execute_search(
        self,
        commands: list[str],
        repo_path: str,
        include_globs: list[str] | None = None,
        exclude_globs: list[str] | None = None,
    ) -> list[SearchResult]:
        """执行ripgrep搜索"""
        pass


class IChunkMatcher(ABC):
    """分片匹配器接口"""

    @abstractmethod
    def match_chunks(
        self, search_results: list[SearchResult], index: IRepoIndex
    ) -> list[CodeChunk]:
        """将搜索结果匹配到代码分片"""
        pass


class IEmbeddingRetriever(ABC):
    """嵌入检索器接口"""

    @abstractmethod
    async def retrieve_similar_chunks(
        self, query_embedding: list[float], index: IRepoIndex, top_k: int
    ) -> list[tuple[CodeChunk, float]]:
        """基于嵌入向量检索相似分片"""
        pass


class ILLMFilter(ABC):
    """LLM筛选器接口"""

    @abstractmethod
    async def filter_chunks(
        self, query: str, chunks: list[CodeChunk]
    ) -> list[tuple[CodeChunk, float]]:
        """使用LLM筛选相关分片"""
        pass


# ============= Symbol Retriever 接口定义 =============


class ISymbolExtractor(ABC):
    """符号提取器接口"""

    @abstractmethod
    def extract_symbols(self, parse_tree: Any, file_info: FileInfo) -> list[Symbol]:
        """从解析树中提取符号"""
        pass

    @abstractmethod
    def extract_symbol_references(
        self, parse_tree: Any, file_info: FileInfo
    ) -> list[Reference]:
        """提取符号引用关系"""
        pass

    @abstractmethod
    def get_supported_symbol_types(self, language: str) -> list[SymbolType]:
        """获取指定语言支持的符号类型"""
        pass


class ISymbolIndex(ABC):
    """符号索引接口"""

    @abstractmethod
    def add_symbol(self, symbol: Symbol):
        """添加符号到索引"""
        pass

    @abstractmethod
    def remove_symbols_by_file(self, file_path: str):
        """删除指定文件的所有符号"""
        pass

    @abstractmethod
    def find_symbols_by_name(self, name: str, exact_match: bool = True) -> list[Symbol]:
        """按名称查找符号"""
        pass

    @abstractmethod
    def find_symbols_by_type(self, symbol_type: SymbolType) -> list[Symbol]:
        """按类型查找符号"""
        pass

    @abstractmethod
    def find_symbols_in_context(self, context_path: str) -> list[Symbol]:
        """在指定上下文中查找符号"""
        pass

    @abstractmethod
    def get_symbol(self, symbol_id: str) -> Optional[Symbol]:
        """获取指定符号"""
        pass

    @abstractmethod
    def get_all_symbols(self) -> list[Symbol]:
        """获取所有符号"""
        pass

    @abstractmethod
    def save_index(self, file_path: str):
        """保存符号索引到文件"""
        pass

    @abstractmethod
    def load_index(self, file_path: str) -> bool:
        """从文件加载符号索引"""
        pass


class ISymbolRetriever(ABC):
    """符号检索器接口"""

    @abstractmethod
    async def search_symbols(self, query: SymbolQuery) -> list[SymbolResult]:
        """符号检索"""
        pass

    @abstractmethod
    async def get_symbol_dependencies(self, symbol_id: str) -> list[Symbol]:
        """获取符号依赖"""
        pass

    @abstractmethod
    async def get_symbol_references(self, symbol_id: str) -> list[Reference]:
        """获取符号引用"""
        pass

    @abstractmethod
    async def find_symbol_definition(
        self, symbol_name: str, context_path: str = None
    ) -> Optional[Symbol]:
        """查找符号定义"""
        pass
