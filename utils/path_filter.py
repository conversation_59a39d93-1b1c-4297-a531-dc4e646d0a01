"""
路径筛选工具
提供include/exclude glob模式的路径过滤功能
"""

from pathlib import PurePosixPath
from typing import List, Optional
import logging

logger = logging.getLogger(__name__)


class PathFilter:
    """路径筛选器，支持include/exclude glob模式"""
    
    def __init__(self, include_patterns: Optional[List[str]] = None, exclude_patterns: Optional[List[str]] = None):
        """
        初始化路径筛选器
        
        Args:
            include_patterns: 包含模式列表，支持glob通配符
            exclude_patterns: 排除模式列表，支持glob通配符
        """
        self.include_patterns = self._normalize_patterns(include_patterns or [])
        self.exclude_patterns = self._normalize_patterns(exclude_patterns or [])
    
    def _normalize_patterns(self, patterns: List[str]) -> List[str]:
        """标准化模式列表"""
        return [p.strip() for p in patterns if isinstance(p, str) and p.strip()]
    
    def matches_any(self, path_str: str, patterns: List[str]) -> bool:
        """检查路径是否匹配任一模式"""
        if not patterns:
            return False
        
        normalized = path_str.replace("\\", "/")
        for pattern in patterns:
            try:
                # 使用PurePosixPath以支持 ** 递归匹配
                if PurePosixPath(normalized).match(pattern):
                    return True
                    
                # 修复**/模式匹配问题
                if "**/" in pattern:
                    # 尝试多种简化模式
                    # 1. 将 dir/**/*.ext 替换为 dir/*.ext
                    simplified1 = pattern.replace("**/", "*/")
                    if PurePosixPath(normalized).match(simplified1):
                        return True
                    
                    # 2. 将 dir/**/*.ext 替换为 dir/*/*.ext（允许一层子目录）
                    simplified2 = pattern.replace("**/", "*/")
                    if PurePosixPath(normalized).match(simplified2):
                        return True
                        
                    # 3. 对于 dir/**/*.ext 模式，检查是否路径以dir/开头且以.ext结尾
                    if "/" in pattern:
                        parts = pattern.split("/")
                        if len(parts) >= 3 and parts[-2] == "**":
                            dir_prefix = "/".join(parts[:-2])
                            file_suffix = parts[-1]
                            if normalized.startswith(dir_prefix + "/") and PurePosixPath(normalized).match(file_suffix):
                                return True
                                
            except Exception as e:
                logger.debug(f"路径模式匹配失败: {pattern} -> {e}")
                continue
        return False
    
    def should_include(self, file_path: str) -> bool:
        """判断文件是否应该包含在结果中"""
        # 如果有include模式，文件必须匹配至少一个include模式
        if self.include_patterns and not self.matches_any(file_path, self.include_patterns):
            return False
        
        # 如果文件匹配任何exclude模式，则排除
        if self.exclude_patterns and self.matches_any(file_path, self.exclude_patterns):
            return False
        
        return True
    
    def filter_file_paths(self, file_paths: List[str]) -> List[str]:
        """过滤文件路径列表"""
        return [path for path in file_paths if self.should_include(path)]
    
    def get_stats(self) -> dict:
        """获取筛选器统计信息"""
        return {
            "include_patterns": self.include_patterns,
            "exclude_patterns": self.exclude_patterns,
            "include_count": len(self.include_patterns),
            "exclude_count": len(self.exclude_patterns)
        }


def create_common_path_filters():
    """创建常用的路径筛选器预设"""
    return {
        "source_only": PathFilter(
            include_patterns=["**/*.py", "**/*.js", "**/*.ts", "**/*.java", "**/*.cpp", "**/*.c", "**/*.ets"],
            exclude_patterns=["**/test/**", "**/tests/**", "**/__pycache__/**", "**/node_modules/**"]
        ),
        "python_only": PathFilter(
            include_patterns=["**/*.py"],
            exclude_patterns=["**/test/**", "**/tests/**", "**/__pycache__/**", "**/venv/**"]
        ),
        "frontend_only": PathFilter(
            include_patterns=["**/*.js", "**/*.ts", "**/*.jsx", "**/*.tsx", "**/*.vue"],
            exclude_patterns=["**/node_modules/**", "**/dist/**", "**/build/**"]
        ),
        "arkts_only": PathFilter(
            include_patterns=["**/*.ets", "**/*.ts"],
            exclude_patterns=["**/oh_modules/**", "**/build/**", "**/dist/**"]
        ),
        "config_only": PathFilter(
            include_patterns=["**/*.json", "**/*.yaml", "**/*.yml", "**/*.toml", "**/*.ini", "**/*.cfg"],
            exclude_patterns=["**/node_modules/**", "**/.git/**"]
        ),
        "docs_only": PathFilter(
            include_patterns=["**/*.md", "**/*.rst", "**/*.txt", "**/README*", "**/CHANGELOG*"],
            exclude_patterns=["**/node_modules/**", "**/.git/**"]
        ),
        "no_tests": PathFilter(
            exclude_patterns=["**/test/**", "**/tests/**", "**/*_test.*", "**/test_*.*", "**/*.test.*"]
        )
    }
