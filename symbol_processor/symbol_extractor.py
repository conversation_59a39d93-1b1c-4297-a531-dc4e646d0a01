"""
符号提取器
负责从代码中提取各种类型的符号信息，包括函数、类、方法、变量等
"""

import re
from abc import ABC, abstractmethod
from typing import Any, Optional

from config import SYMBOL_CONFIG
from utils.interfaces import ISymbolExtractor
from utils.models import (
    FileInfo,
    Parameter,
    Reference,
    Symbol,
    SymbolExtractionError,
    SymbolType,
)
from utils.symbol_helpers import generate_symbol_id


class BaseSymbolExtractor(ISymbolExtractor):
    """符号提取器基类"""

    def __init__(self):
        self.config = SYMBOL_CONFIG
        self.supported_languages = self.config["supported_languages"]
        self.symbol_types_by_language = self.config["symbol_types_by_language"]

    def extract_symbols(self, parse_tree: Any, file_info: FileInfo) -> list[Symbol]:
        """从解析树中提取符号"""
        try:
            if isinstance(parse_tree, dict):
                return self._extract_from_parse_result(parse_tree, file_info)
            else:
                return self._extract_from_ast(parse_tree, file_info)
        except Exception as e:
            raise SymbolExtractionError(
                f"符号提取失败: {str(e)}", file_path=file_info.file_path
            ) from e

    def extract_symbol_references(
        self, parse_tree: Any, file_info: FileInfo
    ) -> list[Reference]:
        """提取符号引用关系"""
        try:
            if isinstance(parse_tree, dict):
                return self._extract_references_from_parse_result(parse_tree, file_info)
            else:
                return self._extract_references_from_ast(parse_tree, file_info)
        except Exception as e:
            raise SymbolExtractionError(
                f"引用提取失败: {str(e)}", file_path=file_info.file_path
            ) from e

    def get_supported_symbol_types(self, language: str) -> list[SymbolType]:
        """获取指定语言支持的符号类型"""
        if language not in self.symbol_types_by_language:
            return []

        type_names = self.symbol_types_by_language[language]
        return [
            SymbolType(name) for name in type_names if hasattr(SymbolType, name.upper())
        ]

    def _extract_from_parse_result(
        self, parse_result: dict[str, Any], file_info: FileInfo
    ) -> list[Symbol]:
        """从解析结果中提取符号"""
        symbols = []

        if parse_result.get("type") == "simple_parse":
            symbols = self._extract_from_simple_parse(parse_result, file_info)
        elif parse_result.get("type") == "tree_sitter_parse":
            symbols = self._extract_from_tree_sitter(parse_result, file_info)
        elif parse_result.get("type") == "arkts_parse":
            symbols = self._extract_from_arkts_parse(parse_result, file_info)

        # 构建符号层级关系
        symbols = self._build_symbol_hierarchy(symbols, file_info)

        return symbols

    def _extract_from_ast(self, ast: Any, file_info: FileInfo) -> list[Symbol]:
        """从AST中提取符号（抽象方法，由子类实现）"""
        return []

    def _extract_references_from_parse_result(
        self, parse_result: dict[str, Any], file_info: FileInfo
    ) -> list[Reference]:
        """从解析结果中提取引用关系"""
        references = []

        if parse_result.get("type") == "simple_parse":
            references = self._extract_references_from_simple_parse(
                parse_result, file_info
            )
        elif parse_result.get("type") == "tree_sitter_parse":
            references = self._extract_references_from_tree_sitter(
                parse_result, file_info
            )
        elif parse_result.get("type") == "arkts_parse":
            references = self._extract_references_from_arkts_parse(
                parse_result, file_info
            )

        return references

    def _extract_references_from_ast(
        self, ast: Any, file_info: FileInfo
    ) -> list[Reference]:
        """从AST中提取引用关系（抽象方法，由子类实现）"""
        return []

    def _extract_from_simple_parse(
        self, parse_result: dict[str, Any], file_info: FileInfo
    ) -> list[Symbol]:
        """从简单解析结果中提取符号"""
        symbols = []
        lines = parse_result.get("lines", [])
        raw_symbols = parse_result.get("symbols", [])

        for raw_symbol in raw_symbols:
            symbol = self._convert_raw_symbol_to_symbol(raw_symbol, file_info, lines)
            if symbol:
                symbols.append(symbol)

        return symbols

    def _extract_from_tree_sitter(
        self, parse_result: dict[str, Any], file_info: FileInfo
    ) -> list[Symbol]:
        """从Tree-sitter解析结果中提取符号"""
        symbols = []
        lines = parse_result.get("lines", [])
        raw_symbols = parse_result.get("symbols", [])

        for raw_symbol in raw_symbols:
            symbol = self._convert_raw_symbol_to_symbol(raw_symbol, file_info, lines)
            if symbol:
                symbols.append(symbol)

        return symbols

    def _extract_from_arkts_parse(
        self, parse_result: dict[str, Any], file_info: FileInfo
    ) -> list[Symbol]:
        """从ArkTS解析结果中提取符号"""
        symbols = []
        lines = parse_result.get("lines", [])
        raw_symbols = parse_result.get("symbols", [])

        for raw_symbol in raw_symbols:
            symbol = self._convert_raw_symbol_to_symbol(raw_symbol, file_info, lines)
            if symbol:
                symbols.append(symbol)

        return symbols

    def _convert_raw_symbol_to_symbol(
        self, raw_symbol: dict[str, Any], file_info: FileInfo, lines: list[str]
    ) -> Optional[Symbol]:
        """将原始符号信息转换为Symbol对象"""
        try:
            # 提取基本信息
            symbol_type = self._map_symbol_type(
                raw_symbol.get("type", ""), file_info.language
            )
            if not symbol_type:
                return None

            # 生成临时ID（稍后会在构建层级关系时重新生成）
            symbol_id = generate_symbol_id(
                raw_symbol.get("name", ""),
                file_info.file_path,
                raw_symbol.get("start_line", 1),
                symbol_type,
            )

            # 保存原始符号信息，用于后续层级构建
            raw_symbol_info = raw_symbol.copy()

            # 提取符号内容
            start_line = raw_symbol.get("start_line", 1)
            end_line = raw_symbol.get("end_line", start_line)
            content = self._extract_symbol_content(lines, start_line, end_line)

            # 提取签名和参数
            signature, parameters, return_type = self._extract_signature_info(
                raw_symbol, content, file_info.language
            )

            # 提取文档和注释
            docstring, comments = self._extract_documentation(
                lines, start_line, end_line, file_info.language
            )

            # 构建Symbol对象
            symbol = Symbol(
                id=symbol_id,
                name=raw_symbol.get("name", ""),
                symbol_type=symbol_type,
                file_path=file_info.file_path,
                start_line=start_line,
                end_line=end_line,
                start_column=raw_symbol.get("start_column", 0),
                end_column=raw_symbol.get("end_column", 0),
                module_path=file_info.relative_path,
                signature=signature,
                parameters=parameters,
                return_type=return_type,
                modifiers=self._extract_modifiers(
                    raw_symbol, content, file_info.language
                ),
                docstring=docstring,
                comments=comments,
                language=file_info.language,
            )

            # 保存原始符号信息到symbol对象，用于层级构建
            symbol._raw_symbol_info = raw_symbol_info

            return symbol

        except Exception as e:
            print(f"转换符号失败: {e}")
            return None

    def _map_symbol_type(self, raw_type: str, language: str) -> Optional[SymbolType]:
        """映射原始符号类型到SymbolType枚举"""
        type_mapping = {
            "function_definition": SymbolType.FUNCTION,
            "method_definition": SymbolType.METHOD,
            "class_definition": SymbolType.CLASS,
            "interface_definition": SymbolType.INTERFACE,
            "variable_definition": SymbolType.VARIABLE,
            "constant_definition": SymbolType.CONSTANT,
            "property_definition": SymbolType.PROPERTY,
            "field_definition": SymbolType.FIELD,
            "enum_definition": SymbolType.ENUM,
            "namespace_definition": SymbolType.NAMESPACE,
            "module_definition": SymbolType.MODULE,
            "import_definition": SymbolType.IMPORT,
            "type_alias_definition": SymbolType.TYPE_ALIAS,
        }

        return type_mapping.get(raw_type)

    def _extract_symbol_content(
        self, lines: list[str], start_line: int, end_line: int
    ) -> str:
        """提取符号的代码内容"""
        if start_line > len(lines) or end_line > len(lines):
            return ""

        content_lines = lines[start_line - 1 : end_line]
        return "\n".join(content_lines)

    def _extract_signature_info(
        self, raw_symbol: dict[str, Any], content: str, language: str
    ) -> tuple[Optional[str], list[Parameter], Optional[str]]:
        """提取函数签名、参数和返回值类型"""
        signature = None
        parameters = []
        return_type = None

        # 根据语言类型提取签名信息
        if language == "python":
            signature, parameters, return_type = self._extract_python_signature(content)
        elif language in ["javascript", "typescript"]:
            signature, parameters, return_type = self._extract_js_signature(content)
        elif language == "java":
            signature, parameters, return_type = self._extract_java_signature(content)
        elif language in ["c", "cpp"]:
            signature, parameters, return_type = self._extract_c_signature(content)
        elif language == "rust":
            signature, parameters, return_type = self._extract_rust_signature(content)
        elif language == "arkts":
            signature, parameters, return_type = self._extract_arkts_signature(content)

        return signature, parameters, return_type

    def _extract_python_signature(
        self, content: str
    ) -> tuple[Optional[str], list[Parameter], Optional[str]]:
        """提取Python函数签名"""
        signature = None
        parameters = []
        return_type = None

        # 匹配函数定义
        func_pattern = r"def\s+(\w+)\s*\(([^)]*)\)(?:\s*->\s*([^:]+))?"
        match = re.search(func_pattern, content)

        if match:
            func_name = match.group(1)
            params_str = match.group(2)
            return_type = match.group(3).strip() if match.group(3) else None

            # 构建签名
            signature = f"def {func_name}({params_str})"
            if return_type:
                signature += f" -> {return_type}"

            # 解析参数
            parameters = self._parse_python_parameters(params_str)

        return signature, parameters, return_type

    def _extract_js_signature(
        self, content: str
    ) -> tuple[Optional[str], list[Parameter], Optional[str]]:
        """提取JavaScript/TypeScript函数签名"""
        signature = None
        parameters = []
        return_type = None

        # 匹配函数定义
        patterns = [
            r"function\s+(\w+)\s*\(([^)]*)\)(?:\s*:\s*([^{]+))?",
            r"(\w+)\s*[:=]\s*(?:async\s+)?function\s*\(([^)]*)\)(?:\s*:\s*([^{]+))?",
        ]

        for pattern in patterns:
            match = re.search(pattern, content)
            if match:
                func_name = match.group(1)
                params_str = match.group(2)
                return_type = match.group(3).strip() if match.group(3) else None

                signature = f"{func_name}({params_str})"
                if return_type:
                    signature += f": {return_type}"

                parameters = self._parse_js_parameters(params_str)
                break

        return signature, parameters, return_type

    def _extract_java_signature(
        self, content: str
    ) -> tuple[Optional[str], list[Parameter], Optional[str]]:
        """提取Java方法签名"""
        signature = None
        parameters = []
        return_type = None

        # 匹配方法定义
        method_pattern = r"(?:public|private|protected|static|\s) +[\w\<\>\[\]]+\s+(\w+) *\([^\)]*\) *\{?[^\{\}]*\{?"
        match = re.search(method_pattern, content)

        if match:
            method_name = match.group(1)

            # 提取返回类型
            return_type_match = re.search(
                r"(?:public|private|protected|static|\s) +([\w\<\>\[\]]+)\s+"
                + method_name,
                content,
            )
            if return_type_match:
                return_type = return_type_match.group(1).strip()

            # 提取参数
            params_match = re.search(r"\(([^)]*)\)", content)
            if params_match:
                params_str = params_match.group(1)
                parameters = self._parse_java_parameters(params_str)
                signature = f"{return_type} {method_name}({params_str})"

        return signature, parameters, return_type

    def _extract_c_signature(
        self, content: str
    ) -> tuple[Optional[str], list[Parameter], Optional[str]]:
        """提取C/C++函数签名"""
        signature = None
        parameters = []
        return_type = None

        # 匹配函数定义
        func_pattern = r"([\w\*]+)\s+(\w+)\s*\(([^)]*)\)"
        match = re.search(func_pattern, content)

        if match:
            return_type = match.group(1).strip()
            func_name = match.group(2)
            params_str = match.group(3)

            signature = f"{return_type} {func_name}({params_str})"
            parameters = self._parse_c_parameters(params_str)

        return signature, parameters, return_type

    def _extract_rust_signature(
        self, content: str
    ) -> tuple[Optional[str], list[Parameter], Optional[str]]:
        """提取Rust函数签名"""
        signature = None
        parameters = []
        return_type = None

        # 匹配函数定义
        func_pattern = r"fn\s+(\w+)\s*\(([^)]*)\)(?:\s*->\s*([^{]+))?"
        match = re.search(func_pattern, content)

        if match:
            func_name = match.group(1)
            params_str = match.group(2)
            return_type = match.group(3).strip() if match.group(3) else None

            signature = f"fn {func_name}({params_str})"
            if return_type:
                signature += f" -> {return_type}"

            parameters = self._parse_rust_parameters(params_str)

        return signature, parameters, return_type

    def _extract_arkts_signature(
        self, content: str
    ) -> tuple[Optional[str], list[Parameter], Optional[str]]:
        """提取ArkTS函数签名"""
        signature = None
        parameters = []
        return_type = None

        # 匹配函数定义
        patterns = [
            r"function\s+(\w+)\s*\(([^)]*)\)(?:\s*:\s*([^{]+))?",
            r"(\w+)\s*[:=]\s*function\s*\(([^)]*)\)(?:\s*:\s*([^{]+))?",
        ]

        for pattern in patterns:
            match = re.search(pattern, content)
            if match:
                func_name = match.group(1)
                params_str = match.group(2)
                return_type = match.group(3).strip() if match.group(3) else None

                signature = f"{func_name}({params_str})"
                if return_type:
                    signature += f": {return_type}"

                parameters = self._parse_arkts_parameters(params_str)
                break

        return signature, parameters, return_type

    def _parse_python_parameters(self, params_str: str) -> list[Parameter]:
        """解析Python参数"""
        parameters = []
        if not params_str.strip():
            return parameters

        for param in params_str.split(","):
            param = param.strip()
            if not param:
                continue

            # 处理默认值
            if "=" in param:
                name, default = param.split("=", 1)
                name = name.strip()
                default_value = default.strip()
                is_optional = True
            else:
                name = param
                default_value = None
                is_optional = False

            # 处理类型注解
            if ":" in name:
                param_name, param_type = name.split(":", 1)
                param_name = param_name.strip()
                param_type = param_type.strip()
            else:
                param_name = name
                param_type = None

            # 处理可变参数
            is_variadic = param_name.startswith("*")

            parameters.append(
                Parameter(
                    name=param_name,
                    param_type=param_type,
                    default_value=default_value,
                    is_optional=is_optional,
                    is_variadic=is_variadic,
                )
            )

        return parameters

    def _parse_js_parameters(self, params_str: str) -> list[Parameter]:
        """解析JavaScript/TypeScript参数"""
        parameters = []
        if not params_str.strip():
            return parameters

        for param in params_str.split(","):
            param = param.strip()
            if not param:
                continue

            # 处理默认值
            if "=" in param:
                name, default = param.split("=", 1)
                name = name.strip()
                default_value = default.strip()
                is_optional = True
            else:
                name = param
                default_value = None
                is_optional = False

            # 处理类型注解
            if ":" in name:
                param_name, param_type = name.split(":", 1)
                param_name = param_name.strip()
                param_type = param_type.strip()
            else:
                param_name = name
                param_type = None

            # 处理可选参数
            if param_name.endswith("?"):
                param_name = param_name[:-1]
                is_optional = True

            # 处理可变参数
            is_variadic = param_name.startswith("...")

            parameters.append(
                Parameter(
                    name=param_name,
                    param_type=param_type,
                    default_value=default_value,
                    is_optional=is_optional,
                    is_variadic=is_variadic,
                )
            )

        return parameters

    def _parse_java_parameters(self, params_str: str) -> list[Parameter]:
        """解析Java参数"""
        parameters = []
        if not params_str.strip():
            return parameters

        for param in params_str.split(","):
            param = param.strip()
            if not param:
                continue

            # Java参数格式: type name
            parts = param.split()
            if len(parts) >= 2:
                param_type = parts[0]
                param_name = parts[1]

                parameters.append(Parameter(name=param_name, param_type=param_type))

        return parameters

    def _parse_c_parameters(self, params_str: str) -> list[Parameter]:
        """解析C/C++参数"""
        parameters = []
        if not params_str.strip():
            return parameters

        for param in params_str.split(","):
            param = param.strip()
            if not param:
                continue

            # C参数格式: type name
            parts = param.split()
            if len(parts) >= 2:
                param_type = parts[0]
                param_name = parts[1]

                parameters.append(Parameter(name=param_name, param_type=param_type))

        return parameters

    def _parse_rust_parameters(self, params_str: str) -> list[Parameter]:
        """解析Rust参数"""
        parameters = []
        if not params_str.strip():
            return parameters

        for param in params_str.split(","):
            param = param.strip()
            if not param:
                continue

            # Rust参数格式: name: type
            if ":" in param:
                name, param_type = param.split(":", 1)
                name = name.strip()
                param_type = param_type.strip()

                parameters.append(Parameter(name=name, param_type=param_type))

        return parameters

    def _parse_arkts_parameters(self, params_str: str) -> list[Parameter]:
        """解析ArkTS参数"""
        parameters = []
        if not params_str.strip():
            return parameters

        for param in params_str.split(","):
            param = param.strip()
            if not param:
                continue

            # 处理默认值
            if "=" in param:
                name, default = param.split("=", 1)
                name = name.strip()
                default_value = default.strip()
                is_optional = True
            else:
                name = param
                default_value = None
                is_optional = False

            # 处理类型注解
            if ":" in name:
                param_name, param_type = name.split(":", 1)
                param_name = param_name.strip()
                param_type = param_type.strip()
            else:
                param_name = name
                param_type = None

            # 处理可选参数
            if param_name.endswith("?"):
                param_name = param_name[:-1]
                is_optional = True

            parameters.append(
                Parameter(
                    name=param_name,
                    param_type=param_type,
                    default_value=default_value,
                    is_optional=is_optional,
                )
            )

        return parameters

    def _extract_modifiers(
        self, raw_symbol: dict[str, Any], content: str, language: str
    ) -> list[str]:
        """提取修饰符"""
        modifiers = []

        # 根据语言类型提取修饰符
        if language == "python":
            modifiers = self._extract_python_modifiers(content)
        elif language in ["javascript", "typescript"]:
            modifiers = self._extract_js_modifiers(content)
        elif language == "java":
            modifiers = self._extract_java_modifiers(content)
        elif language in ["c", "cpp"]:
            modifiers = self._extract_c_modifiers(content)
        elif language == "rust":
            modifiers = self._extract_rust_modifiers(content)
        elif language == "arkts":
            modifiers = self._extract_arkts_modifiers(content)

        return modifiers

    def _extract_python_modifiers(self, content: str) -> list[str]:
        """提取Python修饰符"""
        modifiers = []

        # 检查装饰器
        decorator_pattern = r"@(\w+)"
        decorators = re.findall(decorator_pattern, content)
        modifiers.extend(decorators)

        # 检查async
        if "async def" in content:
            modifiers.append("async")

        return modifiers

    def _extract_js_modifiers(self, content: str) -> list[str]:
        """提取JavaScript/TypeScript修饰符"""
        modifiers = []

        # 检查访问修饰符
        access_modifiers = ["public", "private", "protected"]
        for modifier in access_modifiers:
            if modifier in content:
                modifiers.append(modifier)

        # 检查其他修饰符
        other_modifiers = ["static", "async", "const", "let", "var"]
        for modifier in other_modifiers:
            if modifier in content:
                modifiers.append(modifier)

        return modifiers

    def _extract_java_modifiers(self, content: str) -> list[str]:
        """提取Java修饰符"""
        modifiers = []

        # Java修饰符
        java_modifiers = [
            "public",
            "private",
            "protected",
            "static",
            "final",
            "abstract",
            "synchronized",
            "native",
            "transient",
            "volatile",
        ]

        for modifier in java_modifiers:
            if modifier in content:
                modifiers.append(modifier)

        return modifiers

    def _extract_c_modifiers(self, content: str) -> list[str]:
        """提取C/C++修饰符"""
        modifiers = []

        # C/C++修饰符
        c_modifiers = ["static", "extern", "const", "volatile", "inline"]

        for modifier in c_modifiers:
            if modifier in content:
                modifiers.append(modifier)

        return modifiers

    def _extract_rust_modifiers(self, content: str) -> list[str]:
        """提取Rust修饰符"""
        modifiers = []

        # Rust修饰符
        rust_modifiers = ["pub", "const", "static", "unsafe", "async"]

        for modifier in rust_modifiers:
            if modifier in content:
                modifiers.append(modifier)

        return modifiers

    def _extract_arkts_modifiers(self, content: str) -> list[str]:
        """提取ArkTS修饰符"""
        modifiers = []

        # ArkTS修饰符
        arkts_modifiers = ["public", "private", "protected", "static", "const"]

        for modifier in arkts_modifiers:
            if modifier in content:
                modifiers.append(modifier)

        return modifiers

    def _extract_documentation(
        self, lines: list[str], start_line: int, end_line: int, language: str
    ) -> tuple[Optional[str], list[str]]:
        """提取文档字符串和注释"""
        docstring = None
        comments = []

        # 提取符号前的文档字符串
        if language == "python":
            docstring, comments = self._extract_python_docs(lines, start_line)
        elif language in ["javascript", "typescript"]:
            docstring, comments = self._extract_js_docs(lines, start_line)
        elif language == "java":
            docstring, comments = self._extract_java_docs(lines, start_line)
        elif language in ["c", "cpp"]:
            docstring, comments = self._extract_c_docs(lines, start_line)
        elif language == "rust":
            docstring, comments = self._extract_rust_docs(lines, start_line)
        elif language == "arkts":
            docstring, comments = self._extract_arkts_docs(lines, start_line)

        return docstring, comments

    def _extract_python_docs(
        self, lines: list[str], start_line: int
    ) -> tuple[Optional[str], list[str]]:
        """提取Python文档字符串"""
        docstring = None
        comments = []

        # 查找docstring
        for i in range(start_line - 2, max(0, start_line - 10), -1):
            line = lines[i].strip()
            if line.startswith('"""') or line.startswith("'''"):
                # 找到docstring开始
                doc_lines = []
                j = i
                while j < len(lines):
                    doc_lines.append(lines[j])
                    if lines[j].strip().endswith('"""') or lines[j].strip().endswith(
                        "'''"
                    ):
                        break
                    j += 1
                docstring = "\n".join(doc_lines)
                break
            elif line.startswith("#"):
                comments.append(line)

        return docstring, comments

    def _extract_js_docs(
        self, lines: list[str], start_line: int
    ) -> tuple[Optional[str], list[str]]:
        """提取JavaScript/TypeScript文档"""
        docstring = None
        comments = []

        # 查找JSDoc注释
        for i in range(start_line - 2, max(0, start_line - 10), -1):
            line = lines[i].strip()
            if line.startswith("/**"):
                # 找到JSDoc开始
                doc_lines = []
                j = i
                while j < len(lines):
                    doc_lines.append(lines[j])
                    if lines[j].strip().endswith("*/"):
                        break
                    j += 1
                docstring = "\n".join(doc_lines)
                break
            elif line.startswith("//"):
                comments.append(line)

        return docstring, comments

    def _extract_java_docs(
        self, lines: list[str], start_line: int
    ) -> tuple[Optional[str], list[str]]:
        """提取Java文档"""
        docstring = None
        comments = []

        # 查找JavaDoc注释
        for i in range(start_line - 2, max(0, start_line - 10), -1):
            line = lines[i].strip()
            if line.startswith("/**"):
                # 找到JavaDoc开始
                doc_lines = []
                j = i
                while j < len(lines):
                    doc_lines.append(lines[j])
                    if lines[j].strip().endswith("*/"):
                        break
                    j += 1
                docstring = "\n".join(doc_lines)
                break
            elif line.startswith("//"):
                comments.append(line)

        return docstring, comments

    def _extract_c_docs(
        self, lines: list[str], start_line: int
    ) -> tuple[Optional[str], list[str]]:
        """提取C/C++文档"""
        docstring = None
        comments = []

        # 查找注释
        for i in range(start_line - 2, max(0, start_line - 10), -1):
            line = lines[i].strip()
            if line.startswith("/*"):
                # 找到注释开始
                doc_lines = []
                j = i
                while j < len(lines):
                    doc_lines.append(lines[j])
                    if lines[j].strip().endswith("*/"):
                        break
                    j += 1
                docstring = "\n".join(doc_lines)
                break
            elif line.startswith("//"):
                comments.append(line)

        return docstring, comments

    def _extract_rust_docs(
        self, lines: list[str], start_line: int
    ) -> tuple[Optional[str], list[str]]:
        """提取Rust文档"""
        docstring = None
        comments = []

        # 查找文档注释
        for i in range(start_line - 2, max(0, start_line - 10), -1):
            line = lines[i].strip()
            if line.startswith("///"):
                # 找到文档注释
                doc_lines = []
                j = i
                while j >= 0 and lines[j].strip().startswith("///"):
                    doc_lines.insert(0, lines[j])
                    j -= 1
                docstring = "\n".join(doc_lines)
                break
            elif line.startswith("//"):
                comments.append(line)

        return docstring, comments

    def _extract_arkts_docs(
        self, lines: list[str], start_line: int
    ) -> tuple[Optional[str], list[str]]:
        """提取ArkTS文档"""
        docstring = None
        comments = []

        # 查找注释
        for i in range(start_line - 2, max(0, start_line - 10), -1):
            line = lines[i].strip()
            if line.startswith("/*"):
                # 找到注释开始
                doc_lines = []
                j = i
                while j < len(lines):
                    doc_lines.append(lines[j])
                    if lines[j].strip().endswith("*/"):
                        break
                    j += 1
                docstring = "\n".join(doc_lines)
                break
            elif line.startswith("//"):
                comments.append(line)

        return docstring, comments

    def _build_symbol_hierarchy(
        self, symbols: list[Symbol], file_info: FileInfo
    ) -> list[Symbol]:
        """构建符号的层级关系"""
        # 按行号排序
        symbols.sort(key=lambda s: s.start_line)

        # 第一遍：使用Tree-sitter的层级信息构建父子关系
        for i, symbol in enumerate(symbols):
            parent_symbol = None

            # 检查是否有Tree-sitter的层级信息
            if hasattr(symbol, "_raw_symbol_info"):
                raw_info = symbol._raw_symbol_info
                parent_type = raw_info.get("parent_type")

                if parent_type in [
                    "class_definition",
                    "interface_definition",
                    "namespace_definition",
                ]:
                    # 优先使用Tree-sitter的父类型信息
                    # 查找最近的匹配类型的父符号
                    for j in range(i - 1, -1, -1):
                        potential_parent = symbols[j]
                        if potential_parent.symbol_type in [
                            SymbolType.CLASS,
                            SymbolType.INTERFACE,
                            SymbolType.NAMESPACE,
                        ]:
                            # 检查是否是合理的父子关系（父符号应该在子符号之前）
                            if potential_parent.start_line < symbol.start_line:
                                parent_symbol = potential_parent
                                break

            # 如果没有Tree-sitter信息，回退到基于位置的推断
            if not parent_symbol:
                for j in range(i - 1, -1, -1):
                    potential_parent = symbols[j]
                    if (
                        potential_parent.start_line <= symbol.start_line
                        and potential_parent.end_line >= symbol.end_line
                        and potential_parent.symbol_type
                        in [
                            SymbolType.CLASS,
                            SymbolType.INTERFACE,
                            SymbolType.NAMESPACE,
                        ]
                    ):
                        parent_symbol = potential_parent
                        break

            if parent_symbol:
                # 记录父符号ID
                symbol.parent_symbol = parent_symbol.id
                # 组装命名空间链：父的 namespace（若有） + 父的名称
                if parent_symbol.namespace:
                    symbol.namespace = f"{parent_symbol.namespace}.{parent_symbol.name}"
                else:
                    symbol.namespace = parent_symbol.name

        # 第二遍：重新生成包含层级信息的符号ID
        for symbol in symbols:
            # 获取父符号名称用于ID生成
            parent_name = None
            if symbol.parent_symbol:
                # 从已处理的符号中找到父符号
                for parent in symbols:
                    if parent.id == symbol.parent_symbol:
                        parent_name = parent.name
                        break

            # 重新生成包含层级信息的ID
            new_id = generate_symbol_id(
                symbol.name,
                file_info.file_path,
                symbol.start_line,
                symbol.symbol_type,
                symbol.namespace,
                parent_name,
            )

            # 更新符号ID
            old_id = symbol.id
            symbol.id = new_id

            # 更新其他符号中对此符号的引用
            for other_symbol in symbols:
                if other_symbol.parent_symbol == old_id:
                    other_symbol.parent_symbol = new_id

        # 修复符号边界
        symbols = self._fix_symbol_boundaries(symbols, file_info)

        # 清理临时信息
        for symbol in symbols:
            if hasattr(symbol, "_raw_symbol_info"):
                delattr(symbol, "_raw_symbol_info")

        return symbols

    def _fix_symbol_boundaries(
        self, symbols: list[Symbol], file_info: FileInfo
    ) -> list[Symbol]:
        """修复符号边界问题"""
        if not symbols:
            return symbols

        lines = file_info.content.split("\n")

        # 按开始行排序
        symbols.sort(key=lambda s: s.start_line)

        for i, symbol in enumerate(symbols):
            if symbol.symbol_type in [SymbolType.CLASS, SymbolType.INTERFACE]:
                # 对于类，需要计算正确的结束行
                correct_end_line = self._calculate_class_end_line(
                    symbol, symbols, lines
                )
                if correct_end_line != symbol.end_line:
                    symbol.end_line = correct_end_line

            elif symbol.symbol_type in [SymbolType.FUNCTION, SymbolType.METHOD]:
                # 对于函数/方法，也可能需要修正
                correct_end_line = self._calculate_function_end_line(
                    symbol, symbols, lines, i
                )
                if correct_end_line != symbol.end_line:
                    symbol.end_line = correct_end_line

        return symbols

    def _calculate_class_end_line(
        self, class_symbol: Symbol, all_symbols: list[Symbol], lines: list[str]
    ) -> int:
        """计算类的正确结束行"""

        class_indent = self._get_line_indent(lines[class_symbol.start_line - 1])

        # 找到下一个同级的类或函数定义
        for line_num in range(class_symbol.start_line + 1, len(lines)):
            if line_num >= len(lines):
                break

            line = lines[line_num]
            line_indent = self._get_line_indent(line)

            # 如果遇到同级或更外层的类/函数定义，类结束
            if (
                line.strip()
                and line_indent <= class_indent
                and (
                    line.strip().startswith("class ")
                    or line.strip().startswith("def ")
                    or line.strip().startswith("async def ")
                )
            ):
                return line_num - 1  # 返回前一行（包括空行）

        # 如果到文件末尾都没有找到，返回文件末尾
        return len(lines)

    def _calculate_function_end_line(
        self,
        func_symbol: Symbol,
        all_symbols: list[Symbol],
        lines: list[str],
        symbol_index: int,
    ) -> int:
        """计算函数/方法的正确结束行"""

        if func_symbol.start_line > len(lines):
            return func_symbol.end_line

        func_indent = self._get_line_indent(lines[func_symbol.start_line - 1])

        # 从函数开始行往下找，直到找到同级或更外层的代码
        for line_num in range(func_symbol.start_line, len(lines)):
            if line_num >= len(lines):
                break

            line = lines[line_num]

            # 跳过空行和注释
            if not line.strip() or line.strip().startswith("#"):
                continue

            # 如果遇到同级或更外层的代码，函数结束
            line_indent = self._get_line_indent(line)
            if line_indent <= func_indent and line.strip():
                return line_num  # 返回前一行

        # 检查是否有下一个符号
        if symbol_index + 1 < len(all_symbols):
            next_symbol = all_symbols[symbol_index + 1]
            return min(next_symbol.start_line - 1, len(lines))

        # 如果到文件末尾都没有找到，返回文件末尾
        return len(lines)

    def _is_class_member(
        self, class_symbol: Symbol, potential_member: Symbol, lines: list[str]
    ) -> bool:
        """判断一个符号是否是类的成员"""

        if class_symbol.start_line > len(lines) or potential_member.start_line > len(
            lines
        ):
            return False

        # 检查缩进级别
        class_indent = self._get_line_indent(lines[class_symbol.start_line - 1])
        member_indent = self._get_line_indent(lines[potential_member.start_line - 1])

        # 成员的缩进应该比类的缩进更深
        return member_indent > class_indent

    def _get_line_indent(self, line: str) -> int:
        """获取行的缩进级别"""
        return len(line) - len(line.lstrip())

    def _extract_references_from_simple_parse(
        self, parse_result: dict[str, Any], file_info: FileInfo
    ) -> list[Reference]:
        """从简单解析结果中提取引用关系"""
        # 简单实现，可以根据需要扩展
        return []

    def _extract_references_from_tree_sitter(
        self, parse_result: dict[str, Any], file_info: FileInfo
    ) -> list[Reference]:
        """从Tree-sitter解析结果中提取引用关系"""
        # 简单实现，可以根据需要扩展
        return []

    def _extract_references_from_arkts_parse(
        self, parse_result: dict[str, Any], file_info: FileInfo
    ) -> list[Reference]:
        """从ArkTS解析结果中提取引用关系"""
        # 简单实现，可以根据需要扩展
        return []


class SymbolExtractor(BaseSymbolExtractor):
    """符号提取器主类"""

    def __init__(self):
        super().__init__()
        self.language_extractors = {
            "python": PythonSymbolExtractor(),
            "javascript": JavaScriptSymbolExtractor(),
            "typescript": TypeScriptSymbolExtractor(),
            "java": JavaSymbolExtractor(),
            "c": CSymbolExtractor(),
            "cpp": CppSymbolExtractor(),
            "rust": RustSymbolExtractor(),
            "arkts": ArkTSSymbolExtractor(),
        }

    def extract_symbols(self, parse_tree: Any, file_info: FileInfo) -> list[Symbol]:
        """从解析树中提取符号"""
        # 使用语言特定的提取器
        if file_info.language in self.language_extractors:
            return self.language_extractors[file_info.language].extract_symbols(
                parse_tree, file_info
            )
        else:
            # 使用通用提取器
            return super().extract_symbols(parse_tree, file_info)


class LanguageSpecificExtractor(ABC):
    """语言特定符号提取器基类"""

    @abstractmethod
    def extract_symbols(self, parse_tree: Any, file_info: FileInfo) -> list[Symbol]:
        """提取符号"""
        pass


class PythonSymbolExtractor(LanguageSpecificExtractor):
    """Python符号提取器"""

    def extract_symbols(self, parse_tree: Any, file_info: FileInfo) -> list[Symbol]:
        """提取Python符号"""
        # 使用基类的实现，可以根据需要扩展
        extractor = BaseSymbolExtractor()
        return extractor.extract_symbols(parse_tree, file_info)


class JavaScriptSymbolExtractor(LanguageSpecificExtractor):
    """JavaScript符号提取器"""

    def extract_symbols(self, parse_tree: Any, file_info: FileInfo) -> list[Symbol]:
        """提取JavaScript符号"""
        extractor = BaseSymbolExtractor()
        return extractor.extract_symbols(parse_tree, file_info)


class TypeScriptSymbolExtractor(LanguageSpecificExtractor):
    """TypeScript符号提取器"""

    def extract_symbols(self, parse_tree: Any, file_info: FileInfo) -> list[Symbol]:
        """提取TypeScript符号"""
        extractor = BaseSymbolExtractor()
        return extractor.extract_symbols(parse_tree, file_info)


class JavaSymbolExtractor(LanguageSpecificExtractor):
    """Java符号提取器"""

    def extract_symbols(self, parse_tree: Any, file_info: FileInfo) -> list[Symbol]:
        """提取Java符号"""
        extractor = BaseSymbolExtractor()
        return extractor.extract_symbols(parse_tree, file_info)


class CSymbolExtractor(LanguageSpecificExtractor):
    """C符号提取器"""

    def extract_symbols(self, parse_tree: Any, file_info: FileInfo) -> list[Symbol]:
        """提取C符号"""
        extractor = BaseSymbolExtractor()
        return extractor.extract_symbols(parse_tree, file_info)


class CppSymbolExtractor(LanguageSpecificExtractor):
    """C++符号提取器"""

    def extract_symbols(self, parse_tree: Any, file_info: FileInfo) -> list[Symbol]:
        """提取C++符号"""
        extractor = BaseSymbolExtractor()
        return extractor.extract_symbols(parse_tree, file_info)


class RustSymbolExtractor(LanguageSpecificExtractor):
    """Rust符号提取器"""

    def extract_symbols(self, parse_tree: Any, file_info: FileInfo) -> list[Symbol]:
        """提取Rust符号"""
        extractor = BaseSymbolExtractor()
        return extractor.extract_symbols(parse_tree, file_info)


class ArkTSSymbolExtractor(LanguageSpecificExtractor):
    """ArkTS符号提取器"""

    def extract_symbols(self, parse_tree: Any, file_info: FileInfo) -> list[Symbol]:
        """提取ArkTS符号"""
        extractor = BaseSymbolExtractor()
        return extractor.extract_symbols(parse_tree, file_info)
