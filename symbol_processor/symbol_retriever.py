"""
Symbol Retriever
实现 ISymbolRetriever：基于 SymbolIndex 的符号检索
"""

from __future__ import annotations

import difflib

from config import SYMBOL_RETRIEVAL_CONFIG
from utils.interfaces import ISymbolRetriever
from utils.models import Reference, Symbol, SymbolQuery, SymbolResult


class SymbolRetriever(ISymbolRetriever):
    def __init__(self, symbol_index):
        self.index = symbol_index
        self.name_weight = SYMBOL_RETRIEVAL_CONFIG.get("name_match_weight", 0.7)
        self.context_weight = SYMBOL_RETRIEVAL_CONFIG.get("context_weight", 0.3)
        self.type_bonus = SYMBOL_RETRIEVAL_CONFIG.get("type_match_bonus", 0.1)

    async def search_symbols(self, query: SymbolQuery) -> list[SymbolResult]:
        # start = time.time()  # Unused variable

        # 1) 先按名称候选
        candidates: list[Symbol] = []
        if query.query:
            # 先走精确
            candidates = self.index.find_symbols_by_name(query.query, exact_match=True)
            if not candidates:
                # 再走模糊
                candidates = self.index.find_symbols_by_name(
                    query.query, exact_match=False
                )
        else:
            candidates = self.index.get_all_symbols()

        # 2) 过滤语言（支持 '*' 作为全局搜索通配符）
        if query.languages:
            # 允许 languages 为字符串或列表
            langs = query.languages
            if isinstance(langs, str):
                langs = [langs]
            # 如果包含 '*', 则不进行过滤
            if not any(val == "*" for val in langs):
                lang_set = set(langs)
                candidates = [s for s in candidates if s.language in lang_set]

        # 3) 过滤类型（支持 '*' 作为全局搜索通配符）
        if query.symbol_types:
            types = query.symbol_types

            # 兼容传入字符串列表的情况
            def as_value(t):
                return getattr(t, "value", t)

            if not any(as_value(t) == "*" for t in types):
                type_set = {as_value(t) for t in types}
                candidates = [s for s in candidates if s.symbol_type.value in type_set]

        # 4) 过滤上下文
        if query.context_path:
            in_ctx = {
                x.id for x in self.index.find_symbols_in_context(query.context_path)
            }
            candidates = [s for s in candidates if s.id in in_ctx]

        # 5) 评分
        results: list[tuple[Symbol, float, str]] = []
        for s in candidates:
            score, reason = self._score_symbol(s, query)
            results.append((s, score, reason))

        # 排序并截断
        results.sort(key=lambda x: x[1], reverse=True)
        maxn = min(
            query.max_results, SYMBOL_RETRIEVAL_CONFIG.get("default_max_results", 50)
        )
        top = results[:maxn]

        # total_ms = (time.time() - start) * 1000  # Unused variable
        return [
            SymbolResult(symbol=s, score=sc, match_reason=why) for (s, sc, why) in top
        ]

    async def get_symbol_dependencies(self, symbol_id: str) -> list[Symbol]:
        sym = self.index.get_symbol(symbol_id)
        if not sym:
            return []
        deps = []
        for dep_id in sym.dependencies or []:
            dep = self.index.get_symbol(dep_id)
            if dep:
                deps.append(dep)
        return deps

    async def get_symbol_references(self, symbol_id: str) -> list[Reference]:
        sym = self.index.get_symbol(symbol_id)
        return sym.references if sym and sym.references else []

    async def find_symbol_definition(
        self, symbol_name: str, context_path: str = None
    ) -> Symbol | None:
        # 基于名称优先
        candidates = self.index.find_symbols_by_name(symbol_name, exact_match=True)
        if not candidates:
            candidates = self.index.find_symbols_by_name(symbol_name, exact_match=False)
        if context_path:
            in_ctx = {x.id for x in self.index.find_symbols_in_context(context_path)}
            candidates = [s for s in candidates if s.id in in_ctx]
        # 取分数最高的
        best = None
        best_score = -1.0
        dummy = SymbolQuery(
            query=symbol_name, exact_match=False, context_path=context_path
        )
        for s in candidates:
            sc, _ = self._score_symbol(s, dummy)
            if sc > best_score:
                best_score = sc
                best = s
        return best

    async def find_symbols_by_qualified_name(self, qualified_name: str) -> list[Symbol]:
        """根据完全限定名称查找符号，支持层级结构"""
        from utils.symbol_helpers import get_symbol_qualified_name

        candidates = []
        all_symbols = self.index.get_all_symbols()

        qualified_lower = qualified_name.lower()

        for symbol in all_symbols:
            symbol_qualified = get_symbol_qualified_name(symbol).lower()

            # 精确匹配
            if symbol_qualified == qualified_lower:
                candidates.append(symbol)
            # 后缀匹配（如查找 "Class.method" 能匹配 "package.Class.method"）
            elif symbol_qualified.endswith("." + qualified_lower):
                candidates.append(symbol)
            # 部分匹配（如查找 "method" 能在 "Class.method" 中找到）
            elif qualified_lower in symbol_qualified:
                candidates.append(symbol)

        return candidates

    async def find_symbols_in_hierarchy(
        self, parent_name: str, symbol_name: str = None
    ) -> list[Symbol]:
        """在指定的层级结构中查找符号"""
        candidates = []
        all_symbols = self.index.get_all_symbols()

        for symbol in all_symbols:
            # 检查是否在指定的父级中
            if symbol.namespace and parent_name.lower() in symbol.namespace.lower():
                if symbol_name is None or symbol_name.lower() in symbol.name.lower():
                    candidates.append(symbol)
            # 检查父符号
            elif symbol.parent_symbol:
                try:
                    parent = self.index.get_symbol(symbol.parent_symbol)
                    if parent and parent_name.lower() in parent.name.lower():
                        if (
                            symbol_name is None
                            or symbol_name.lower() in symbol.name.lower()
                        ):
                            candidates.append(symbol)
                except Exception:
                    pass

        return candidates

    # ---------------- scoring ----------------
    def _score_symbol(self, s: Symbol, q: SymbolQuery) -> tuple[float, str]:
        reasons = []
        score = 0.0

        # 导入层级信息提取函数
        from utils.symbol_helpers import (
            get_symbol_context_path,
            get_symbol_qualified_name,
        )

        # 名称匹配 - 支持层级匹配
        if q.query:
            target = q.query.lower()
            name = (s.name or "").lower()
            qualified_name = get_symbol_qualified_name(s).lower()
            context_path = get_symbol_context_path(s).lower()

            # 1. 精确名称匹配
            if target == name:
                score += self.name_weight
                reasons.append("exact-name")
            # 2. 完全限定名称匹配（最高优先级）
            elif target == qualified_name:
                score += self.name_weight * 1.2  # 给予更高权重
                reasons.append("qualified-name")
            # 3. 上下文路径匹配
            elif target == context_path:
                score += self.name_weight * 1.1
                reasons.append("context-path")
            # 4. 部分限定名称匹配（如 Class.method）
            elif target in qualified_name and "." in target:
                # 检查是否是有意义的部分匹配
                if qualified_name.endswith(target) or target in qualified_name.split(
                    "."
                ):
                    score += self.name_weight * 0.9
                    reasons.append("partial-qualified")
            # 5. 模糊名称匹配
            else:
                sim = difflib.SequenceMatcher(None, target, name).ratio()
                # 也检查与限定名称的相似度
                qualified_sim = difflib.SequenceMatcher(
                    None, target, qualified_name
                ).ratio()
                best_sim = max(sim, qualified_sim)
                score += best_sim * self.name_weight
                reasons.append(f"fuzzy-name:{best_sim:.2f}")

        # 上下文匹配 - 改进的层级上下文匹配
        if q.context_path:
            ctx_match = 0.0
            context_lower = q.context_path.lower()

            # 1. 检查完全限定名称
            qualified_name = get_symbol_qualified_name(s).lower()
            if context_lower in qualified_name:
                ctx_match = 1.0
                reasons.append("qualified-context")

            # 2. 检查命名空间
            elif s.namespace and context_lower in s.namespace.lower():
                ctx_match = 0.9
                reasons.append("namespace-context")

            # 3. 检查模块路径
            elif s.module_path and context_lower in s.module_path.lower():
                ctx_match = 0.8
                reasons.append("module-context")

            # 4. 检查父符号
            elif s.parent_symbol:
                try:
                    parent = self.index.get_symbol(s.parent_symbol)
                    if parent:
                        parent_qualified = get_symbol_qualified_name(parent).lower()
                        if context_lower in parent_qualified:
                            ctx_match = 0.7
                            reasons.append("parent-context")
                except Exception:
                    pass

            score += ctx_match * self.context_weight

        # 类型匹配加分
        if q.symbol_types and s.symbol_type in q.symbol_types:
            score += self.type_bonus
            reasons.append("type")

        return score, ",".join(reasons) or "name-only"
