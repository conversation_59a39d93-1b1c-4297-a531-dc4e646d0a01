"""
Symbol Index
实现 ISymbolIndex 接口：管理符号的存储、索引与持久化
"""

from __future__ import annotations

import difflib
import gzip
import json
import os
from dataclasses import asdict
from datetime import datetime
from pathlib import Path

from config import SYMBOL_INDEX_CONFIG
from utils.interfaces import ISymbolIndex
from utils.models import Reference, Symbol, SymbolType


class SymbolIndex(ISymbolIndex):
    """符号索引实现（内存型，支持持久化）"""

    def __init__(self):
        # 主存储
        self.symbols: dict[str, Symbol] = {}  # id -> Symbol

        # 二级索引
        self.name_index: dict[str, set[str]] = {}  # lower(name) -> set(symbol_id)
        self.type_index: dict[str, set[str]] = {}  # symbol_type(str) -> set(symbol_id)
        self.context_index: dict[
            str, set[str]
        ] = {}  # context_path (module/class/namespace) -> set(symbol_id)
        self.language_index: dict[str, set[str]] = {}  # language -> set(symbol_id)
        self.file_index: dict[
            str, set[str]
        ] = {}  # file_path (as provided in Symbol) -> set(symbol_id)

        # 统计
        self.created_at: str | None = None
        self.updated_at: str | None = None

    # ---------------- ISymbolIndex API ----------------
    def add_symbol(self, symbol: Symbol):
        self.symbols[symbol.id] = symbol
        # name index
        key = symbol.name.lower() if symbol.name else ""
        if key:
            self.name_index.setdefault(key, set()).add(symbol.id)
        # type index
        t = (
            symbol.symbol_type.value
            if isinstance(symbol.symbol_type, SymbolType)
            else str(symbol.symbol_type)
        )
        self.type_index.setdefault(t, set()).add(symbol.id)
        # context index
        contexts: list[str] = []
        if symbol.module_path:
            contexts.append(symbol.module_path)
        if symbol.namespace:
            contexts.append(symbol.namespace)
        # 也将 parent_symbol 的占位加入上下文索引（以便按类名/父符号名检索）
        if symbol.parent_symbol:
            contexts.append(f"parent:{symbol.parent_symbol}")
        for ctx in contexts:
            self.context_index.setdefault(ctx, set()).add(symbol.id)
        # language index
        if symbol.language:
            self.language_index.setdefault(symbol.language, set()).add(symbol.id)
        # file index
        if symbol.file_path:
            self.file_index.setdefault(symbol.file_path, set()).add(symbol.id)
        # 更新时间
        self._touch()

    def remove_symbols_by_file(self, file_path: str):
        """按文件路径删除符号。
        支持传入相对路径；内部将对比 Symbol.file_path 的结尾进行匹配。
        """
        to_remove: set[str] = set()
        # 先尝试精确匹配
        if file_path in self.file_index:
            to_remove |= set(self.file_index[file_path])
        else:
            # 结尾匹配（用于相对路径）
            for fp, ids in list(self.file_index.items()):
                if fp.endswith(file_path):
                    to_remove |= set(ids)
        # 实际删除
        for sid in to_remove:
            sym = self.symbols.pop(sid, None)
            if not sym:
                continue
            # 清理各索引
            key = sym.name.lower() if sym.name else ""
            if key and key in self.name_index:
                self.name_index[key].discard(sid)
                if not self.name_index[key]:
                    self.name_index.pop(key, None)
            t = (
                sym.symbol_type.value
                if isinstance(sym.symbol_type, SymbolType)
                else str(sym.symbol_type)
            )
            if t in self.type_index:
                self.type_index[t].discard(sid)
                if not self.type_index[t]:
                    self.type_index.pop(t, None)
            if sym.module_path and sym.module_path in self.context_index:
                self.context_index[sym.module_path].discard(sid)
                if not self.context_index[sym.module_path]:
                    self.context_index.pop(sym.module_path, None)
            if sym.namespace and sym.namespace in self.context_index:
                self.context_index[sym.namespace].discard(sid)
                if not self.context_index[sym.namespace]:
                    self.context_index.pop(sym.namespace, None)
            if sym.parent_symbol:
                ps = f"parent:{sym.parent_symbol}"
                if ps in self.context_index:
                    self.context_index[ps].discard(sid)
                    if not self.context_index[ps]:
                        self.context_index.pop(ps, None)
            if sym.language and sym.language in self.language_index:
                self.language_index[sym.language].discard(sid)
                if not self.language_index[sym.language]:
                    self.language_index.pop(sym.language, None)
            if sym.file_path and sym.file_path in self.file_index:
                self.file_index[sym.file_path].discard(sid)
                if not self.file_index[sym.file_path]:
                    self.file_index.pop(sym.file_path, None)
        if to_remove:
            self._touch()

    def find_symbols_by_name(self, name: str, exact_match: bool = True) -> list[Symbol]:
        if not name:
            return []
        name_l = name.lower()
        results: list[Symbol] = []
        # exact
        if name_l in self.name_index:
            results.extend(self._symbols_from_ids(self.name_index[name_l]))
        if exact_match:
            return results

        # fuzzy: 在 name_index 的键上做相似匹配
        all_names = list(self.name_index.keys())
        # 使用较高阈值，减少噪声
        close = difflib.get_close_matches(
            name_l,
            all_names,
            n=SYMBOL_INDEX_CONFIG.get("max_search_results", 100),
            cutoff=SYMBOL_INDEX_CONFIG.get("fuzzy_threshold", 0.8),
        )
        for n in close:
            if n == name_l:
                continue
            results.extend(self._symbols_from_ids(self.name_index[n]))
        # 如果 fuzzy 没有命中，降级为子串匹配（适合短query如 "auth" 命中 "authenticate"）
        if not results and len(name_l) >= 2:
            for n in all_names:
                if name_l in n:
                    results.extend(self._symbols_from_ids(self.name_index[n]))
        # 去重，保持相对稳定的顺序（按名称接近度排序可在检索层做）
        seen = set()
        deduped: list[Symbol] = []
        for s in results:
            if s.id not in seen:
                deduped.append(s)
                seen.add(s.id)
        return deduped

    def find_symbols_by_type(self, symbol_type: SymbolType) -> list[Symbol]:
        t = (
            symbol_type.value
            if isinstance(symbol_type, SymbolType)
            else str(symbol_type)
        )
        return self._symbols_from_ids(self.type_index.get(t, set()))

    def find_symbols_in_context(self, context_path: str) -> list[Symbol]:
        if not context_path:
            return []
        # 直接命中
        if context_path in self.context_index:
            return self._symbols_from_ids(self.context_index[context_path])
        # 子串匹配（例如传入模块/类前缀）
        hits: set[str] = set()
        for ctx, ids in self.context_index.items():
            if context_path in ctx:
                hits |= ids
        return self._symbols_from_ids(hits)

    def get_symbol(self, symbol_id: str) -> Symbol | None:
        return self.symbols.get(symbol_id)

    def get_all_symbols(self) -> list[Symbol]:
        return list(self.symbols.values())

    def save_index(self, file_path: str):
        """保存符号索引到文件（JSON 或 gzip JSON）"""
        # 组织可序列化数据
        data = {
            "symbols": {
                sid: self._serialize_symbol(sym) for sid, sym in self.symbols.items()
            },
            "name_index": {k: list(v) for k, v in self.name_index.items()},
            "type_index": {k: list(v) for k, v in self.type_index.items()},
            "context_index": {k: list(v) for k, v in self.context_index.items()},
            "language_index": {k: list(v) for k, v in self.language_index.items()},
            "file_index": {k: list(v) for k, v in self.file_index.items()},
            "created_at": self.created_at or datetime.now().isoformat(),
            "updated_at": self.updated_at or datetime.now().isoformat(),
            "version": "1.0",
        }
        # 确保目录存在
        Path(file_path).parent.mkdir(parents=True, exist_ok=True)
        # 是否写 gzip，依据扩展名
        if str(file_path).endswith(".gz"):
            with gzip.open(file_path, "wt", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False)
        else:
            with open(file_path, "w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

    def load_index(self, file_path: str) -> bool:
        try:
            if not os.path.exists(file_path) and not os.path.exists(file_path + ".gz"):
                return False
            if os.path.exists(file_path + ".gz"):
                with gzip.open(file_path + ".gz", "rt", encoding="utf-8") as f:
                    data = json.load(f)
            else:
                with open(file_path, encoding="utf-8") as f:
                    data = json.load(f)
            # 恢复数据
            self.symbols = {
                sid: self._deserialize_symbol(d)
                for sid, d in data.get("symbols", {}).items()
            }
            self.name_index = {k: set(v) for k, v in data.get("name_index", {}).items()}
            self.type_index = {k: set(v) for k, v in data.get("type_index", {}).items()}
            self.context_index = {
                k: set(v) for k, v in data.get("context_index", {}).items()
            }
            self.language_index = {
                k: set(v) for k, v in data.get("language_index", {}).items()
            }
            self.file_index = {k: set(v) for k, v in data.get("file_index", {}).items()}
            self.created_at = data.get("created_at")
            self.updated_at = data.get("updated_at")
            return True
        except Exception:
            return False

    # ---------------- helpers ----------------
    def _touch(self):
        now = datetime.now().isoformat()
        if not self.created_at:
            self.created_at = now
        self.updated_at = now

    def _symbols_from_ids(self, ids: set[str]) -> list[Symbol]:
        return [self.symbols[i] for i in ids if i in self.symbols]

    def _serialize_symbol(self, sym: Symbol) -> dict:
        # 将 Enum 与 datetime 等不可直接序列化的字段做转换
        d = asdict(sym)
        d["symbol_type"] = (
            sym.symbol_type.value
            if isinstance(sym.symbol_type, SymbolType)
            else str(sym.symbol_type)
        )
        # references
        refs: list[Reference] = sym.references or []
        d["references"] = [
            {
                "file_path": r.file_path,
                "line_number": r.line_number,
                "column_number": r.column_number,
                "context": r.context,
                "reference_type": r.reference_type.value,
            }
            for r in refs
        ]
        d["created_at"] = sym.created_at.isoformat() if sym.created_at else None
        d["updated_at"] = sym.updated_at.isoformat() if sym.updated_at else None
        return d

    def _deserialize_symbol(self, d: dict) -> Symbol:
        # 反序列化 references
        refs = []
        for r in d.get("references", []) or []:
            from utils.models import ReferenceType as RT

            refs.append(
                Reference(
                    file_path=r.get("file_path", ""),
                    line_number=r.get("line_number", 0),
                    column_number=r.get("column_number", 0),
                    context=r.get("context", ""),
                    reference_type=RT(r.get("reference_type")),
                )
            )
        # 参数
        params = []
        for p in d.get("parameters", []) or []:
            from utils.models import Parameter as P

            params.append(
                P(
                    name=p.get("name", ""),
                    param_type=p.get("param_type"),
                    default_value=p.get("default_value"),
                    is_optional=p.get("is_optional", False),
                    is_variadic=p.get("is_variadic", False),
                )
            )
        # 构造 Symbol
        sym = Symbol(
            id=d["id"],
            name=d.get("name", ""),
            symbol_type=SymbolType(d.get("symbol_type", "function")),
            file_path=d.get("file_path", ""),
            start_line=d.get("start_line", 1),
            end_line=d.get("end_line", 1),
            start_column=d.get("start_column", 0),
            end_column=d.get("end_column", 0),
            namespace=d.get("namespace"),
            module_path=d.get("module_path", ""),
            parent_symbol=d.get("parent_symbol"),
            signature=d.get("signature"),
            parameters=params,
            return_type=d.get("return_type"),
            modifiers=d.get("modifiers") or [],
            docstring=d.get("docstring"),
            comments=d.get("comments") or [],
            dependencies=d.get("dependencies") or [],
            references=refs,
            language=d.get("language", ""),
            created_at=datetime.fromisoformat(d["created_at"])
            if d.get("created_at")
            else None,
            updated_at=datetime.fromisoformat(d["updated_at"])
            if d.get("updated_at")
            else None,
        )
        return sym
