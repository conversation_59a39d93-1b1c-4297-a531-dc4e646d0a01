"""
代码洞察生成器
使用LLM为代码分片生成功能描述和洞察
"""

import asyncio
import logging
import os
import re

from config import INSIGHT_CONFIG, ModelType, model_manager
from utils.helpers import truncate_text
from utils.interfaces import ICodeInsightGenerator
from utils.models import CodeChunk

logger = logging.getLogger(__name__)


class CodeInsightGenerator(ICodeInsightGenerator):
    """代码洞察生成器实现"""

    def __init__(self):
        self.batch_size = INSIGHT_CONFIG["batch_size"]
        self.max_insight_length = INSIGHT_CONFIG["max_insight_length"]
        self.prompt_template = INSIGHT_CONFIG["insight_prompt_template"]

    async def generate_insight(self, chunk: CodeChunk) -> str:
        """为代码分片生成洞察描述"""
        try:
            # 构建提示词
            prompt = self._build_prompt(chunk)

            # 调用LLM生成洞察
            insight = await model_manager.chat_completion(
                prompt,
                model_type=ModelType.NORMAL,
            )

            # 清理和截断结果
            cleaned_insight = self._clean_insight(insight)
            return truncate_text(cleaned_insight, self.max_insight_length)

        except Exception as e:
            logger.error(f"生成洞察失败 {chunk.id}: {e}")
            # 返回基础描述作为降级
            return self._generate_fallback_insight(chunk)

    async def batch_generate_insights(self, chunks: list[CodeChunk]) -> list[str]:
        """批量生成洞察描述"""
        insights = []
        total_chunks = len(chunks)

        # 分批处理
        for i in range(0, len(chunks), self.batch_size):
            batch = chunks[i : i + self.batch_size]

            # 显示进度
            progress = min(i + self.batch_size, total_chunks)
            logger.debug(
                f"  洞察生成进度: {progress}/{total_chunks} ({progress / total_chunks * 100:.1f}%)"
            )

            # 并发生成洞察
            batch_tasks = [self.generate_insight(chunk) for chunk in batch]
            batch_insights = await asyncio.gather(*batch_tasks, return_exceptions=True)

            # 处理异常结果
            for j, insight in enumerate(batch_insights):
                if isinstance(insight, Exception):
                    logger.warning(f"批量生成洞察异常 {batch[j].id}: {insight}")
                    insight = self._generate_fallback_insight(batch[j])

                insights.append(insight)

            # 避免API调用过于频繁
            if i + self.batch_size < len(chunks):
                await asyncio.sleep(0.1)  # 减少等待时间

        return insights

    def _build_prompt(self, chunk: CodeChunk) -> str:
        """构建LLM提示词"""
        # 截断过长的代码内容
        code_content = truncate_text(chunk.content, 30_000)

        return self.prompt_template.format(
            file_path=chunk.relative_path,
            chunk_type=chunk.chunk_type,
            symbol_name=chunk.symbol_name,
            language=chunk.language,
            code_content=code_content,
        )

    def _clean_insight(self, insight: str) -> str:
        """清理LLM生成的洞察文本：去除代码块、元数据复述与冗余前缀，保证简洁。"""
        if not insight:
            return ""

        text = insight

        # 1) 去除 Markdown 代码块与反引号
        try:
            text = re.sub(r"```[\s\S]*?```", " ", text)
        except re.error:
            pass
        text = text.replace("`", "")

        # 2) 移除输入元数据的复述行（文件路径/语言/符号/代码等标签）
        meta_prefixes = [
            "文件路径", "路径", "代码类型", "符号名称", "编程语言", "代表性符号",
            "代码内容", "代码节选", "上下文", "【上下文】", "【代码】", "【代码节选】",
            "请输出", "只输出",
        ]
        lines = text.splitlines()
        filtered_lines: list[str] = []
        for line in lines:
            line_stripped = line.strip()
            if not line_stripped:
                continue
            # 如果是元数据提示/标题行，丢弃
            if any(line_stripped.startswith(p) for p in meta_prefixes):
                continue
            # 过滤形如 "标签: 内容" 的元数据行
            if re.match(r"^(文件路径|路径|代码类型|符号名称|编程语言|代表性符号|代码内容|代码节选)[\s：:]+", line_stripped):
                continue
            filtered_lines.append(line)
        text = " ".join(l.strip() for l in filtered_lines if l.strip())

        # 3) 去除路径与“位于…文件中”等模板化措辞
        # 去除形如 a/b/c.py 或 Windows 路径
        text = re.sub(r"(?:^|\s)(?:[A-Za-z]:\\\\)?(?:[\w.\-]+[\\/])+[\w.\-]+\.[A-Za-z0-9]{1,6}", " ", text)
        # 去除“位于 … 文件中/在 … 文件中”的描述
        text = re.sub(r"(位于|在)[^。！？!?]*?文件中", "", text)

        # 4) 去掉冗余主语前缀（这段代码/该函数/本文件 等）
        text = re.sub(
            r"^(这段代码|该函数|此函数|这个函数|本函数|该方法|此方法|该类|这个类|该文件|本文件|该模块|此模块|该组件|该服务|该脚本|该程序|该工具|该接口|该API)[的是用于用来主要，,:：\s]*",
            "",
            text,
        )

        # 5) 收敛空白并移除包围引号
        cleaned = " ".join(text.split())
        if cleaned.startswith('"') and cleaned.endswith('"') and len(cleaned) >= 2:
            cleaned = cleaned[1:-1]
        elif cleaned.startswith("'") and cleaned.endswith("'") and len(cleaned) >= 2:
            cleaned = cleaned[1:-1]

        # 6) 确保以中文或英文句号/感叹/问号结尾
        if cleaned and not cleaned.endswith((".", "。", "!", "！", "?", "？")):
            cleaned += "。"

        return cleaned

    def _generate_fallback_insight(self, chunk: CodeChunk) -> str:
        """生成降级洞察描述"""
        if chunk.chunk_type == "function":
            return f"这是一个名为{chunk.symbol_name}的函数，位于{chunk.relative_path}文件中。"
        elif chunk.chunk_type == "class":
            return f"这是一个名为{chunk.symbol_name}的类定义，位于{chunk.relative_path}文件中。"
        elif chunk.chunk_type == "method":
            return f"这是一个名为{chunk.symbol_name}的方法，位于{chunk.relative_path}文件中。"
        elif chunk.chunk_type == "markdown_section":
            return f"这是{chunk.relative_path}文档中的{chunk.symbol_name}章节。"
        else:
            return f"这是{chunk.relative_path}文件中的一段{chunk.language}代码。"

    def generate_repo_insight(self, chunks: list[CodeChunk]) -> str:
        """生成仓库整体洞察"""
        if not chunks:
            return "这是一个空的代码仓库。"

        # 统计信息
        total_chunks = len(chunks)
        languages = {chunk.language for chunk in chunks}
        files = {chunk.relative_path for chunk in chunks}

        # 按类型统计
        type_counts = {}
        for chunk in chunks:
            chunk_type = chunk.chunk_type
            type_counts[chunk_type] = type_counts.get(chunk_type, 0) + 1

        # 构建洞察描述
        insight_parts = [
            f"这个代码仓库包含{len(files)}个文件，共{total_chunks}个代码片段。",
            f"支持的编程语言包括：{', '.join(sorted(languages))}。",
        ]

        # 添加类型统计
        if type_counts:
            type_desc = []
            for chunk_type, count in sorted(type_counts.items()):
                if chunk_type == "function":
                    type_desc.append(f"{count}个函数")
                elif chunk_type == "class":
                    type_desc.append(f"{count}个类")
                elif chunk_type == "method":
                    type_desc.append(f"{count}个方法")
                elif chunk_type == "markdown_section":
                    type_desc.append(f"{count}个文档章节")
                else:
                    type_desc.append(f"{count}个其他代码片段")

            if type_desc:
                insight_parts.append(f"包含{', '.join(type_desc)}。")

        # 添加主要文件信息
        main_files = [
            f
            for f in files
            if any(
                keyword in f.lower() for keyword in ["main", "index", "app", "server"]
            )
        ]
        if main_files:
            insight_parts.append(f"主要入口文件可能包括：{', '.join(main_files[:3])}。")

        return " ".join(insight_parts)

    async def generate_file_insight(self, file_path: str, chunks: list[CodeChunk]) -> str:
        """使用 LLM 生成文件级洞察：输出中文功能总结（不超过300字）。"""
        try:
            if not chunks:
                return ""

            # 组装输入：语言、代表性符号、代码内容节选
            language = getattr(chunks[0], "language", "") or ""

            # 代表性符号（去重保序，最多8个，过滤占位符，给LLM更多上下文）
            symbol_names: list[str] = []
            for c in chunks:
                sname = getattr(c, "symbol_name", "")
                if sname:
                    symbol_names.append(sname)
            seen = set()
            unique_symbols: list[str] = []
            def is_placeholder(name: str) -> bool:
                if not name:
                    return True
                low = name.lower()
                # 常见占位符/自动生成符号名样式
                placeholders = [
                    "chunk_", "section_", "markdown_section", "part_", "block_",
                    "tmp", "temp", "example", "sample", "demo",
                ]
                return any(p in low for p in placeholders)
            for s in symbol_names:
                if s not in seen and not is_placeholder(s):
                    seen.add(s)
                    unique_symbols.append(s)
                if len(unique_symbols) >= 8:
                    break
            # top_symbols = ", ".join(unique_symbols)

            # 拼接文件内容节选（限制长度）
            # 选取每个分片的前若干字符，聚合到最大长度
            max_chars = 256_000
            parts: list[str] = []
            current = 0
            for c in chunks:
                snippet = c.content or ""
                if not snippet:
                    continue
                remaining = max_chars - current
                if remaining <= 0:
                    break
                snippet = snippet[: min(len(snippet), remaining)]
                parts.append(snippet)
                current += len(snippet)
            code_content = "\n\n".join(parts)

            # 构建提示词
            prompt = INSIGHT_CONFIG["file_insight_prompt_template"].format(
                file_path=file_path,
                language=language or "",
                # top_symbols=top_symbols or "无",
                code_content=code_content,
            )

            # 调用 LLM
            insight = await model_manager.chat_completion(
                prompt,
                model_type=ModelType.NORMAL,
                max_tokens=2048,
            )

            cleaned = self._clean_insight(insight)
            # 严格限制在配置中定义的最大长度（按字符）
            max_len = INSIGHT_CONFIG.get("max_file_insight_length", 800)
            if cleaned and len(cleaned) > max_len:
                cleaned = cleaned[:max_len]
                # 若截断到中间，尽量以句号结尾
                if not cleaned.endswith(("。", ".", "!", "！", "?", "？")):
                    cleaned += "。"
            return cleaned
        except Exception as e:
            logger.error(f"生成文件洞察失败 {file_path}: {e}")
            return ""

    async def update_chunk_insights(self, chunks: list[CodeChunk]) -> list[CodeChunk]:
        """更新代码分片的洞察信息"""
        logger.debug(f"开始生成{len(chunks)}个代码分片的洞察...")

        # 使用信号量控制并发数
        from config import INSIGHT_CONFIG

        max_concurrent = INSIGHT_CONFIG.get("max_concurrent_requests", 50)
        semaphore = asyncio.Semaphore(max_concurrent)

        async def generate_with_semaphore(chunk):
            async with semaphore:
                return await self.generate_insight(chunk)

        # 创建所有任务
        tasks = [generate_with_semaphore(chunk) for chunk in chunks]

        # 批量执行，显示进度
        batch_size = 50  # 进度显示的批次大小
        insights = []

        for i in range(0, len(tasks), batch_size):
            batch_tasks = tasks[i : i + batch_size]
            progress = min(i + batch_size, len(tasks))
            logger.debug(
                f"  洞察生成进度: {progress}/{len(tasks)} ({progress / len(tasks) * 100:.1f}%)"
            )

            batch_insights = await asyncio.gather(*batch_tasks, return_exceptions=True)

            # 处理异常结果
            for j, insight in enumerate(batch_insights):
                if isinstance(insight, Exception):
                    chunk_idx = i + j
                    logger.warning(f"生成洞察异常 {chunks[chunk_idx].id}: {insight}")
                    insight = self._generate_fallback_insight(chunks[chunk_idx])
                insights.append(insight)

        # 更新分片的洞察信息
        for chunk, insight in zip(chunks, insights):
            chunk.insight = insight

        logger.debug(f"完成洞察生成，成功处理{len(chunks)}个分片")
        return chunks
