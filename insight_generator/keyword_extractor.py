"""
关键词提取器
从代码分片中提取关键词，包括函数名、类名、变量名等
"""

import logging
import re
from pathlib import Path

from config import KEYWORD_CONFIG
from utils.helpers import extract_function_name
from utils.interfaces import IKeywordExtractor
from utils.models import CodeChunk

logger = logging.getLogger(__name__)


class KeywordExtractor(IKeywordExtractor):
    """关键词提取器实现"""

    def __init__(self):
        self.min_keyword_length = KEYWORD_CONFIG["min_keyword_length"]
        self.max_keywords_per_chunk = KEYWORD_CONFIG["max_keywords_per_chunk"]
        self.include_file_path_keywords = KEYWORD_CONFIG["include_file_path_keywords"]
        self.keyword_weights = KEYWORD_CONFIG["keyword_weight"]

        # 编程语言关键字（需要过滤掉）
        self.language_keywords = {
            "python": {
                "def",
                "class",
                "if",
                "else",
                "elif",
                "for",
                "while",
                "try",
                "except",
                "import",
                "from",
                "return",
                "yield",
                "with",
                "as",
                "pass",
                "break",
                "continue",
                "and",
                "or",
                "not",
                "in",
                "is",
                "None",
                "True",
                "False",
            },
            "javascript": {
                "function",
                "var",
                "let",
                "const",
                "if",
                "else",
                "for",
                "while",
                "do",
                "switch",
                "case",
                "default",
                "try",
                "catch",
                "finally",
                "return",
                "break",
                "continue",
                "this",
                "new",
                "typeof",
                "instanceof",
                "true",
                "false",
                "null",
                "undefined",
            },
            "typescript": {
                "function",
                "var",
                "let",
                "const",
                "if",
                "else",
                "for",
                "while",
                "do",
                "switch",
                "case",
                "default",
                "try",
                "catch",
                "finally",
                "return",
                "break",
                "continue",
                "this",
                "new",
                "typeof",
                "instanceof",
                "true",
                "false",
                "null",
                "undefined",
                "interface",
                "type",
                "enum",
                "namespace",
            },
            "arkts": {
                "let",
                "const",
                "var",
                "type",
                "interface",
                "enum",
                "public",
                "private",
                "protected",
                "class",
                "extends",
                "super",
                "abstract",
                "function",
                "return",
                "async",
                "await",
                "import",
                "export",
                "readonly",
                "this",
                "null",
                "undefined",
                "true",
                "false",
                "if",
                "else",
                "switch",
                "case",
                "default",
                "for",
                "while",
                "do",
            },
            "java": {
                "public",
                "private",
                "protected",
                "static",
                "final",
                "abstract",
                "class",
                "interface",
                "extends",
                "implements",
                "if",
                "else",
                "for",
                "while",
                "do",
                "switch",
                "case",
                "default",
                "try",
                "catch",
                "finally",
                "return",
                "break",
                "continue",
                "this",
                "super",
                "new",
                "instanceof",
                "true",
                "false",
                "null",
            },
            "c": {
                "int",
                "char",
                "float",
                "double",
                "void",
                "if",
                "else",
                "for",
                "while",
                "do",
                "switch",
                "case",
                "default",
                "break",
                "continue",
                "return",
                "sizeof",
                "typedef",
                "struct",
                "union",
                "enum",
                "static",
                "extern",
                "const",
                "volatile",
            },
            "cpp": {
                "int",
                "char",
                "float",
                "double",
                "void",
                "bool",
                "if",
                "else",
                "for",
                "while",
                "do",
                "switch",
                "case",
                "default",
                "break",
                "continue",
                "return",
                "sizeof",
                "typedef",
                "struct",
                "union",
                "enum",
                "class",
                "public",
                "private",
                "protected",
                "virtual",
                "static",
                "const",
                "namespace",
                "using",
                "template",
                "typename",
            },
            "rust": {
                "fn",
                "let",
                "mut",
                "const",
                "static",
                "if",
                "else",
                "match",
                "for",
                "while",
                "loop",
                "break",
                "continue",
                "return",
                "struct",
                "enum",
                "impl",
                "trait",
                "pub",
                "mod",
                "use",
                "crate",
                "self",
                "super",
                "true",
                "false",
            },
        }

    def extract_keywords(self, chunk: CodeChunk) -> list[str]:
        """从代码分片中提取关键词"""
        keywords = set()

        # 1. 提取符号名称关键词
        symbol_keywords = self._extract_symbol_keywords(chunk)
        keywords.update(symbol_keywords)

        # 2. 提取代码内容关键词
        content_keywords = self._extract_content_keywords(chunk)
        keywords.update(content_keywords)

        # 3. 提取文件路径关键词
        if self.include_file_path_keywords:
            path_keywords = self.extract_file_path_keywords(chunk.relative_path)
            keywords.update(path_keywords)

        # 4. 过滤和排序
        filtered_keywords = self._filter_keywords(keywords, chunk.language)
        sorted_keywords = self._sort_keywords_by_importance(filtered_keywords, chunk)

        # 5. 限制数量
        return sorted_keywords[: self.max_keywords_per_chunk]

    def extract_file_path_keywords(self, file_path: str) -> list[str]:
        """从文件路径中提取关键词"""
        keywords = set()

        path_obj = Path(file_path)

        # 文件名（不含扩展名）
        stem = path_obj.stem
        if stem and len(stem) >= self.min_keyword_length:
            keywords.add(stem.lower())

            # 分割驼峰命名和下划线命名
            keywords.update(self._split_identifier(stem))

        # 目录名
        for part in path_obj.parts[:-1]:  # 排除文件名
            if part and len(part) >= self.min_keyword_length:
                keywords.add(part.lower())
                keywords.update(self._split_identifier(part))

        return list(keywords)

    def _extract_symbol_keywords(self, chunk: CodeChunk) -> set[str]:
        """提取符号名称关键词"""
        keywords = set()

        if chunk.symbol_name:
            # 添加完整符号名
            symbol_name = extract_function_name(chunk.symbol_name)
            if len(symbol_name) >= self.min_keyword_length:
                keywords.add(symbol_name.lower())

                # 分割复合标识符
                keywords.update(self._split_identifier(symbol_name))

        return keywords

    def _extract_content_keywords(self, chunk: CodeChunk) -> set[str]:
        """从代码内容中提取关键词"""
        keywords = set()

        if chunk.language == "markdown":
            return self._extract_markdown_keywords(chunk.content)

        # 使用正则表达式提取标识符
        identifier_pattern = r"\b[a-zA-Z_][a-zA-Z0-9_]*\b"
        identifiers = re.findall(identifier_pattern, chunk.content)

        for identifier in identifiers:
            if len(identifier) >= self.min_keyword_length:
                keywords.add(identifier.lower())

                # 分割复合标识符
                keywords.update(self._split_identifier(identifier))

        # 提取字符串字面量中的关键词
        string_keywords = self._extract_string_literals(chunk.content, chunk.language)
        keywords.update(string_keywords)

        # 提取注释中的关键词
        comment_keywords = self._extract_comment_keywords(chunk.content, chunk.language)
        keywords.update(comment_keywords)

        return keywords

    def _extract_markdown_keywords(self, content: str) -> set[str]:
        """从Markdown内容中提取关键词"""
        keywords = set()

        # 提取标题
        title_pattern = r"^#+\s+(.+)$"
        titles = re.findall(title_pattern, content, re.MULTILINE)
        for title in titles:
            words = re.findall(r"\b\w+\b", title.lower())
            keywords.update(
                word for word in words if len(word) >= self.min_keyword_length
            )

        # 提取代码块中的标识符
        code_block_pattern = r"```[\w]*\n(.*?)\n```"
        code_blocks = re.findall(code_block_pattern, content, re.DOTALL)
        for code_block in code_blocks:
            identifiers = re.findall(r"\b[a-zA-Z_][a-zA-Z0-9_]*\b", code_block)
            keywords.update(
                id.lower() for id in identifiers if len(id) >= self.min_keyword_length
            )

        # 提取普通文本中的关键词
        # 移除代码块后的内容
        text_content = re.sub(r"```.*?```", "", content, flags=re.DOTALL)
        words = re.findall(r"\b[a-zA-Z]+\b", text_content.lower())
        keywords.update(word for word in words if len(word) >= self.min_keyword_length)

        return keywords

    def _extract_string_literals(self, content: str, language: str) -> set[str]:
        """提取字符串字面量中的关键词"""
        keywords = set()

        # 提取字符串字面量
        string_patterns = [
            r'"([^"\\]|\\.)*"',  # 双引号字符串
            r"'([^'\\]|\\.)*'",  # 单引号字符串
        ]

        if language in ["python"]:
            string_patterns.extend(
                [
                    r'""".*?"""',  # Python三引号字符串
                    r"'''.*?'''",
                ]
            )

        for pattern in string_patterns:
            strings = re.findall(pattern, content, re.DOTALL)
            for string_content in strings:
                if isinstance(string_content, str):
                    # 提取字符串中的单词
                    words = re.findall(r"\b[a-zA-Z]+\b", string_content.lower())
                    keywords.update(
                        word for word in words if len(word) >= self.min_keyword_length
                    )

        return keywords

    def _extract_comment_keywords(self, content: str, language: str) -> set[str]:
        """提取注释中的关键词"""
        keywords = set()

        comment_patterns = []

        if language in ["python"]:
            comment_patterns.append(r"#.*$")
        elif language in ["javascript", "typescript", "java", "c", "cpp", "rust"]:
            comment_patterns.extend(
                [
                    r"//.*$",  # 单行注释
                    r"/\*.*?\*/",  # 多行注释
                ]
            )

        for pattern in comment_patterns:
            comments = re.findall(pattern, content, re.MULTILINE | re.DOTALL)
            for comment in comments:
                # 移除注释符号
                clean_comment = re.sub(r"[#/\*]", " ", comment)
                words = re.findall(r"\b[a-zA-Z]+\b", clean_comment.lower())
                keywords.update(
                    word for word in words if len(word) >= self.min_keyword_length
                )

        return keywords

    def _split_identifier(self, identifier: str) -> set[str]:
        """分割复合标识符（驼峰命名、下划线命名等）"""
        keywords = set()

        # 分割下划线命名
        parts = identifier.split("_")
        for part in parts:
            if part and len(part) >= self.min_keyword_length:
                keywords.add(part.lower())

        # 分割驼峰命名
        camel_parts = re.findall(r"[A-Z]?[a-z]+|[A-Z]+(?=[A-Z][a-z]|\b)", identifier)
        for part in camel_parts:
            if len(part) >= self.min_keyword_length:
                keywords.add(part.lower())

        return keywords

    def _filter_keywords(self, keywords: set[str], language: str) -> list[str]:
        """过滤关键词"""
        filtered = set()

        # 获取语言关键字
        lang_keywords = self.language_keywords.get(language, set())

        for keyword in keywords:
            # 过滤条件
            if (
                len(keyword) >= self.min_keyword_length
                and keyword not in lang_keywords
                and not keyword.isdigit()
                and not self._is_common_word(keyword)
            ):
                filtered.add(keyword)

        return list(filtered)

    def _is_common_word(self, word: str) -> bool:
        """判断是否为常见单词（需要过滤）"""
        common_words = {
            "the",
            "and",
            "or",
            "but",
            "in",
            "on",
            "at",
            "to",
            "for",
            "of",
            "with",
            "by",
            "from",
            "up",
            "about",
            "into",
            "through",
            "during",
            "before",
            "after",
            "above",
            "below",
            "between",
            "among",
            "this",
            "that",
            "these",
            "those",
            "get",
            "set",
            "add",
            "remove",
            "delete",
            "create",
            "make",
            "do",
            "go",
            "come",
            "take",
            "give",
            "put",
            "use",
            "find",
            "know",
            "think",
            "say",
            "tell",
            "ask",
            "work",
            "seem",
            "feel",
            "try",
            "leave",
            "call",
        }
        return word.lower() in common_words

    def _sort_keywords_by_importance(
        self, keywords: list[str], chunk: CodeChunk
    ) -> list[str]:
        """按重要性排序关键词"""
        keyword_scores = {}

        for keyword in keywords:
            score = 0.0

            # 符号名称中的关键词权重更高
            if chunk.symbol_name and keyword in chunk.symbol_name.lower():
                score += self.keyword_weights["function_name"]

            # 文件路径中的关键词
            if keyword in chunk.relative_path.lower():
                score += self.keyword_weights["file_path"]

            # 在内容中出现的频率
            content_lower = chunk.content.lower()
            frequency = content_lower.count(keyword)
            score += frequency * 0.1

            # 关键词长度（较长的关键词通常更有意义）
            score += len(keyword) * 0.05

            keyword_scores[keyword] = score

        # 按分数排序
        return sorted(keywords, key=lambda k: keyword_scores.get(k, 0), reverse=True)

    def update_chunk_keywords(self, chunks: list[CodeChunk]) -> list[CodeChunk]:
        """更新代码分片的关键词信息"""
        logger.debug(f"开始提取{len(chunks)}个代码分片的关键词...")

        total_chunks = len(chunks)
        for i, chunk in enumerate(chunks):
            chunk.keywords = self.extract_keywords(chunk)
            # print(f"chunk.keywords: {chunk.keywords}")

            # 显示进度
            if (i + 1) % 50 == 0 or i + 1 == total_chunks:
                logger.debug(
                    f"  关键词提取进度: {i + 1}/{total_chunks} ({(i + 1) / total_chunks * 100:.1f}%)"
                )

        logger.debug(f"完成关键词提取，成功处理{len(chunks)}个分片")
        return chunks
