#!/usr/bin/env python3
"""
CodebaseQA 评估工具主启动脚本
"""

import logging
import sys
import time
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from evaluation.config import (  # noqa: E402
    create_evaluation_config,
    print_config,
    validate_config,
)
from evaluation.test_runner import TestRunner  # noqa: E402


def setup_logging(log_level: str):
    """设置日志"""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )

    # 屏蔽httpx的日志输出
    logging.getLogger("httpx").setLevel(logging.WARNING)


def main():
    """主函数"""
    logging.getLogger(__name__)
    logging.info("=" * 60)
    logging.info("CodebaseQA 评估工具")
    logging.info("=" * 60)

    try:
        # 1. 创建和验证配置
        config = create_evaluation_config()

        if not validate_config(config):
            sys.exit(1)

        # 2. 设置日志
        setup_logging(config.log_level)
        logger = logging.getLogger(__name__)

        # 3. 打印配置
        print_config(config)
        logging.info("")

        # 4. 创建输出目录
        Path(config.output_dir).mkdir(parents=True, exist_ok=True)

        # 5. 初始化测试运行器
        logger.info("初始化测试运行器...")
        runner = TestRunner(
            test_data_file=config.test_data_file,
            base_repo_path=config.base_repo_path,
            persist_path=config.persist_path,
            k_values=config.k_values,
        )

        # 6. 运行评估
        logger.info("开始运行评估...")
        start_time = time.time()

        metrics = runner.run_full_evaluation(
            max_queries_per_repo=config.max_queries_per_repo,
            force_rebuild_index=config.force_rebuild_index,
            repos_to_test=config.repos_to_test,
        )

        total_time = time.time() - start_time

        # 7. 保存结果
        if config.save_detailed_results:
            logger.info("保存详细结果...")
            runner.save_detailed_results(config.output_dir)

        # 8. 输出最终结果
        logging.info("\n" + "=" * 60)
        logging.info("评估完成!")
        logging.info("=" * 60)
        logging.info(f"总耗时: {total_time:.2f}秒")
        logging.info(f"总查询数: {metrics.total_queries}")
        logging.info(f"精确匹配率: {metrics.exact_match_rate:.3f}")
        logging.info(f"平均MRR: {metrics.avg_mrr:.3f}")
        logging.info(f"平均查询时延: {metrics.avg_query_time_ms:.2f}ms")
        logging.info(f"最大查询时延: {metrics.max_query_time_ms:.2f}ms")

        logging.info("\n主要指标:")
        for k in sorted(metrics.avg_precision_at_k.keys()):
            logging.info(f"  P@{k}: {metrics.avg_precision_at_k[k]:.3f}")
            logging.info(f"  R@{k}: {metrics.avg_recall_at_k[k]:.3f}")
            logging.info(f"  F1@{k}: {metrics.avg_f1_at_k[k]:.3f}")
            logging.info(f"  nDCG@{k}: {metrics.avg_ndcg_at_k[k]:.3f}")

        if config.save_detailed_results:
            logging.info(f"\n详细结果已保存到: {config.output_dir}")

        logging.info("\n评估成功完成!")

    except KeyboardInterrupt:
        logging.warning("\n评估被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"评估失败: {e}")
        import traceback

        logger.debug(traceback.format_exc())
        sys.exit(1)


if __name__ == "__main__":
    main()
