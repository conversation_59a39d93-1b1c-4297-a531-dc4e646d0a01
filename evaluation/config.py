"""
评估配置文件
"""

import argparse
import logging
from pathlib import Path
from typing import Optional

from pydantic import BaseModel

logger = logging.getLogger(__name__)


class EvaluationConfig(BaseModel):
    """评估配置"""

    # 数据路径
    test_data_file: str = "dataset/test/arkts_gitee_funCode_summary_highScores.jsonl"
    base_repo_path: str = "/Users/<USER>/ide_dev/data/arkts_from_yyq/active_repos_gitee_exclude_HarmonyOS_Codelabs"
    persist_path: str = "./temp/evaluation"
    output_dir: str = "./temp/evaluation_results"

    # 测试参数
    max_queries_per_repo: Optional[int] = None  # 每个仓库最大查询数量，None表示不限制
    force_rebuild_index: bool = False  # 是否强制重建索引
    repos_to_test: Optional[list[str]] = None  # 要测试的仓库列表，None表示测试所有
    k_values: list[int] = [1, 3, 5, 10]  # 要计算的K值

    # 并发参数
    max_concurrent_repos: int = 1  # 最大并发仓库数（暂时不支持并发）

    # 输出参数
    save_detailed_results: bool = True  # 是否保存详细结果
    generate_html_report: bool = True  # 是否生成HTML报告
    log_level: str = "INFO"  # 日志级别


def create_evaluation_config() -> EvaluationConfig:
    """创建评估配置"""
    parser = argparse.ArgumentParser(description="CodebaseQA 评估工具")

    # 数据路径参数
    parser.add_argument("--test-data-file", help="测试数据文件路径")
    parser.add_argument("--base-repo-path", help="仓库基础路径")
    parser.add_argument("--persist-path", help="索引持久化路径")
    parser.add_argument("--output-dir", help="结果输出目录")

    # 测试参数
    parser.add_argument("--max-queries-per-repo", type=int, help="每个仓库最大查询数量")
    parser.add_argument(
        "--force-rebuild-index", action="store_true", help="强制重建索引"
    )
    parser.add_argument("--repos-to-test", nargs="+", help="要测试的仓库列表")
    parser.add_argument(
        "--k-values", nargs="+", type=int, default=[1, 3, 5, 10], help="要计算的K值列表"
    )

    # 输出参数
    parser.add_argument(
        "--no-detailed-results", action="store_true", help="不保存详细结果"
    )
    parser.add_argument("--no-html-report", action="store_true", help="不生成HTML报告")
    parser.add_argument(
        "--log-level", choices=["DEBUG", "INFO", "WARNING", "ERROR"], help="日志级别"
    )

    args = parser.parse_args()

    # 创建基础配置
    config = EvaluationConfig()

    # 应用命令行参数
    if args.test_data_file:
        config.test_data_file = args.test_data_file
    if args.base_repo_path:
        config.base_repo_path = args.base_repo_path
    if args.persist_path:
        config.persist_path = args.persist_path
    if args.output_dir:
        config.output_dir = args.output_dir

    if args.max_queries_per_repo:
        config.max_queries_per_repo = args.max_queries_per_repo
    if args.force_rebuild_index:
        config.force_rebuild_index = True
    if args.repos_to_test:
        config.repos_to_test = args.repos_to_test
    if args.k_values:
        config.k_values = args.k_values

    if args.no_detailed_results:
        config.save_detailed_results = False
    if args.no_html_report:
        config.generate_html_report = False
    if args.log_level:
        config.log_level = args.log_level

    return config


def validate_config(config: EvaluationConfig) -> bool:
    """
    验证配置

    Args:
        config: 评估配置

    Returns:
        是否有效
    """
    errors = []

    # 检查测试数据文件
    if not Path(config.test_data_file).exists():
        errors.append(f"测试数据文件不存在: {config.test_data_file}")

    # 检查仓库基础路径
    if not Path(config.base_repo_path).exists():
        errors.append(f"仓库基础路径不存在: {config.base_repo_path}")

    # 检查K值
    if not config.k_values or any(k <= 0 for k in config.k_values):
        errors.append("K值必须为正整数")

    # 检查查询数量限制
    if config.max_queries_per_repo is not None and config.max_queries_per_repo <= 0:
        errors.append("每个仓库最大查询数量必须为正整数")

    if errors:
        logger.error("配置验证失败:")
        for error in errors:
            logger.error(f"  - {error}")
        return False

    return True


def print_config(config: EvaluationConfig):
    """打印配置信息"""
    logger.info("=== 评估配置 ===")
    logger.info(f"测试数据文件: {config.test_data_file}")
    logger.info(f"仓库基础路径: {config.base_repo_path}")
    logger.info(f"持久化路径: {config.persist_path}")
    logger.info(f"输出目录: {config.output_dir}")
    logger.info(f"K值: {config.k_values}")
    logger.info(f"日志级别: {config.log_level}")

    if config.max_queries_per_repo:
        logger.info(f"每个仓库最大查询数: {config.max_queries_per_repo}")

    if config.repos_to_test:
        logger.info(f"指定测试仓库: {config.repos_to_test}")

    if config.force_rebuild_index:
        logger.info("强制重建索引: 是")

    logger.info(f"保存详细结果: {'是' if config.save_detailed_results else '否'}")
    logger.info(f"生成HTML报告: {'是' if config.generate_html_report else '否'}")


if __name__ == "__main__":
    # 测试配置创建
    config = create_evaluation_config()

    if validate_config(config):
        print_config(config)
    else:
        logger.error("配置无效，请检查参数")
