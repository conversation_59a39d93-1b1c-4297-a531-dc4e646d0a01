#!/usr/bin/env python3
"""
端到端评测器
通过启动实际的检索服务来进行完整的评测流程
"""

import json
import logging
import os
import signal
import subprocess
import sys
import time
from collections import defaultdict
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass, field
from pathlib import Path
from typing import Optional

import requests

current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from metrics_calculator import (
    MetricsCalculator as ImprovedMetricsCalculator,
)

# from utils.code_format import ClearCommentsForC

logger = logging.getLogger(__name__)


@dataclass
class TestCase:
    """测试用例"""

    query: str  # comment_zh
    ground_truth_file: str  # filepath (相对路径)
    ground_truth_content: str  # 新增：ground truth的内容
    repo: str
    original_filepath: str  # 原始完整路径
    intent: str  # 新增：查询意图
    tool_query: Optional[str] = None  # 新增：主流程转换后的query
    # 位置信息，用于将查询结果写回到原始JSON的正确位置
    func_index: int = 0  # 第几个function
    query_index: int = 0  # 第几个query
    query_source_type: str = "generated_queries"  # query来源类型：'generated_queries', 'intents', 'queries', 'comment_zh'
    intent_name: Optional[str] = None  # 如果来自intents，记录intent名称


@dataclass
class QueryResult:
    """查询结果"""

    query: str
    ground_truth_file: str
    ground_truth_content: str  # 新增：ground truth的内容
    predicted_files: list[str]
    predicted_contents: list[str]  # 新增：预测结果的内容
    scores: list[float]
    execution_time_ms: float
    is_match: bool
    match_rank: Optional[int]
    intent: str  # 新增：查询意图
    query_source: str  # 'user' 或 'tool'
    raw_result: dict  # 新增：原始查询结果JSON


@dataclass 
class QueryResultCollector:
    """查询结果收集器 - 用于管理查询结果与原始JSON位置的映射"""
    # 存储结构：{file_data_id: {func_index: {query_source_type: {query_index/intent_name: {"query_result": {}, "tool_query_result": {}}}}}}
    results: dict = field(default_factory=dict)
    
    def add_result(self, test_case: TestCase, query_type: str, result: dict):
        """添加查询结果"""
        # 使用文件路径作为唯一标识（因为同一个文件只有一条JSON记录）
        file_key = f"{test_case.repo}_{test_case.original_filepath}"
        
        if file_key not in self.results:
            self.results[file_key] = {}
        if test_case.func_index not in self.results[file_key]:
            self.results[file_key][test_case.func_index] = {}
        if test_case.query_source_type not in self.results[file_key][test_case.func_index]:
            self.results[file_key][test_case.func_index][test_case.query_source_type] = {}
        
        # 确定查询在数组中的键
        if test_case.query_source_type == "intents":
            query_key = test_case.intent_name or test_case.intent
        else:
            query_key = test_case.query_index
            
        if query_key not in self.results[file_key][test_case.func_index][test_case.query_source_type]:
            self.results[file_key][test_case.func_index][test_case.query_source_type][query_key] = {}
            
        # 保存结果
        self.results[file_key][test_case.func_index][test_case.query_source_type][query_key][f"{query_type}_result"] = result
    
    def get_results_for_file(self, repo: str, filepath: str) -> dict:
        """获取指定文件的所有查询结果"""
        file_key = f"{repo}_{filepath}"
        return self.results.get(file_key, {})


class ServiceManager:
    """服务管理器"""

    def __init__(self, venv_path: str = ".venv"):
        self.venv_path = venv_path
        self.current_process = None
        self.current_port = None

    def start_service(self, repo_path: str, repo_name: str, port: int = 5001) -> bool:
        """启动检索服务"""
        try:
            # 停止当前服务
            logger.info("准备启动新服务，先停止当前服务...")
            self.stop_service()

            # 检查端口是否可用，如果不可用则尝试清理
            if self._is_port_in_use(port):
                logger.info(f"端口 {port} 被占用，尝试清理...")
                self._force_clear_port(port)

                # # 再次检查端口
                # if self._is_port_in_use(port):
                #     logger.error(f"端口 {port} 仍被占用，无法启动服务")
                #     return False

            # 构建命令 - 直接使用python，不通过shell
            python_path = f"{self.venv_path}/bin/python"
            timestamp = time.strftime("%Y%m%d%H%M%S")
            cmd = [
                python_path,
                "main.py",
                "--repo-path", repo_path,
                "--port", str(port),
                "--log-level", "DEBUG",
                "--no-scheduler",
                "--log-file", f"eval_log__{repo_name}_{timestamp}.log",
            ]

            logger.info(f"启动新服务: {repo_name} -> {repo_path} -> 端口 {port}")
            logger.info(f"执行命令: {' '.join(cmd)}")

            # 直接启动python进程，不使用shell
            self.current_process = subprocess.Popen(
                cmd,
                stdout=sys.stdout,
                stderr=sys.stderr,
                preexec_fn=os.setsid,  # 创建新的进程组
            )
            self.current_port = port

            logger.info(f"新进程已启动，PID: {self.current_process.pid}")

            # 等待服务启动
            success = self._wait_for_service(port)

            if not success:
                # 如果启动失败，输出错误日志
                if self.current_process and self.current_process.poll() is not None:
                    stdout, stderr = self.current_process.communicate()
                    if stdout:
                        logger.error(
                            f"服务stdout: {stdout.decode('utf-8', errors='ignore')}"
                        )
                    if stderr:
                        logger.error(
                            f"服务stderr: {stderr.decode('utf-8', errors='ignore')}"
                        )

            return success

        except Exception as e:
            logger.error(f"启动服务失败: {e}")
            return False

    def _is_port_in_use(self, port: int) -> bool:
        """检查端口是否被占用"""
        try:
            result = subprocess.run(
                f"lsof -ti:{port}", shell=True, capture_output=True, timeout=5
            )
            return result.returncode == 0 and result.stdout.strip()
        except Exception:
            return False

    def _wait_for_service(self, port: int, timeout: int = 3600) -> bool:
        """等待服务启动并确保索引构建完成"""
        health_url = f"http://127.0.0.1:{port}/health"
        query_url = f"http://127.0.0.1:{port}/query"
        start_time = time.time()

        logger.info("等待服务启动和索引构建完成...")

        while time.time() - start_time < timeout:
            try:
                # 先尝试health端点
                response = requests.get(health_url, timeout=5)
                if response.status_code == 200:
                    health_data = response.json()
                    # 检查索引状态
                    if health_data.get("status") == "healthy":
                        logger.info(f"服务已启动且索引构建完成: 端口 {port}")
                        # 额外等待2秒确保索引完全就绪
                        time.sleep(2)
                        return True
                    else:
                        logger.debug(
                            f"服务启动中，索引状态: {health_data.get('status', 'unknown')}"
                        )
            except requests.exceptions.RequestException:
                pass

            try:
                # 如果health端点不存在，尝试query端点
                response = requests.post(
                    query_url, json={"query": "test", "top_k": 1}, timeout=120
                )
                if response.status_code in [200, 400]:  # 400也表示服务在运行
                    logger.info(f"服务已启动: 端口 {port}")
                    # 额外等待2秒确保索引完全就绪
                    time.sleep(2)
                    return True
            except requests.exceptions.RequestException:
                pass

            time.sleep(3)  # 增加等待间隔

        logger.error(f"服务启动超时: 端口 {port}")
        return False

    def stop_service(self):
        """停止当前服务"""
        if self.current_process:
            try:
                logger.info(f"正在停止服务 (PID: {self.current_process.pid})...")

                # 先尝试温和终止
                self.current_process.terminate()
                try:
                    self.current_process.wait(timeout=10)
                    logger.info("服务已正常停止")
                except subprocess.TimeoutExpired:
                    logger.warning("服务未在10秒内停止，尝试强制终止...")
                    # 强制终止整个进程组
                    try:
                        os.killpg(os.getpgid(self.current_process.pid), signal.SIGKILL)
                        self.current_process.wait(timeout=5)
                        logger.info("服务已强制停止")
                    except Exception as kill_e:
                        logger.error(f"强制停止服务失败: {kill_e}")

            except Exception as e:
                logger.warning(f"停止服务时出现异常: {e}")

            self.current_process = None

        if self.current_port:
            # 等待端口释放，但不强制清理（避免误杀）
            logger.info(f"等待端口 {self.current_port} 释放...")
            time.sleep(3)
            self.current_port = None
            logger.info("端口已释放")

        if not self.current_process and not self.current_port:
            logger.info("没有运行中的服务需要停止")

    def _force_clear_port(self, port: int):
        """强制清理指定端口上的所有进程"""
        try:
            logger.info(f"强制清理端口 {port}...")
            current_pid = os.getpid()

            # 多次尝试清理端口
            for attempt in range(3):
                # 查找端口上的进程
                result = subprocess.run(
                    f"lsof -ti:{port}", shell=True, capture_output=True, timeout=5
                )
                if result.returncode == 0 and result.stdout.strip():
                    pids = result.stdout.decode().strip().split("\n")
                    # 过滤掉当前进程，避免自杀
                    pids_to_kill = [
                        pid
                        for pid in pids
                        if pid.strip() and int(pid.strip()) != current_pid
                    ]

                    if pids_to_kill:
                        logger.info(
                            f"第{attempt + 1}次尝试: 发现端口 {port} 上的进程: {pids_to_kill}"
                        )

                        # 逐个终止进程，避免误杀当前进程
                        for pid in pids_to_kill:
                            try:
                                # 先尝试温和终止
                                subprocess.run(
                                    f"kill -TERM {pid}",
                                    shell=True,
                                    capture_output=True,
                                    timeout=2,
                                )
                                time.sleep(1)

                                # 检查进程是否还存在
                                check_result = subprocess.run(
                                    f"kill -0 {pid}", shell=True, capture_output=True
                                )
                                if check_result.returncode == 0:
                                    # 进程还存在，强制终止
                                    subprocess.run(
                                        f"kill -9 {pid}",
                                        shell=True,
                                        capture_output=True,
                                        timeout=2,
                                    )
                                    logger.info(f"强制终止进程 {pid}")
                                else:
                                    logger.info(f"进程 {pid} 已正常终止")
                            except Exception as e:
                                logger.warning(f"终止进程 {pid} 时出现异常: {e}")

                        # 等待端口释放
                        time.sleep(2)
                    else:
                        logger.info(f"端口 {port} 上没有需要清理的外部进程")
                        break
                else:
                    logger.info(f"端口 {port} 已清空")
                    break

            # 最终等待
            time.sleep(1)
            logger.info(f"端口 {port} 清理完成")

        except Exception as e:
            logger.warning(f"清理端口 {port} 时出现异常: {e}")


class MetricsCalculator:
    """评估指标计算器 - 使用改进的对比评估功能"""

    def __init__(self):
        self.improved_calculator = ImprovedMetricsCalculator([1, 3, 5, 10])

    def calculate_metrics(self, results: list[QueryResult]) -> dict:
        """计算评估指标 - 同时返回路径匹配和内容匹配的结果"""
        if not results:
            return self._empty_metrics()

        # 转换为改进评估器需要的格式
        evaluation_results_path = []
        evaluation_results_content = []
        query_times = []

        for result in results:
            query_times.append(result.execution_time_ms)

            # 路径匹配评估
            path_eval = self.improved_calculator.calculate_single_query_metrics(
                query=result.query,
                ground_truth_file=result.ground_truth_file,
                predicted_files=result.predicted_files,
                scores=result.scores,
            )
            evaluation_results_path.append(path_eval)

            # 内容匹配评估 - 现在使用真实的内容数据
            content_eval = self.improved_calculator.calculate_single_query_metrics(
                query=result.query,
                ground_truth_file=result.ground_truth_file,
                predicted_files=result.predicted_files,
                scores=result.scores,
                ground_truth_content=result.ground_truth_content,
                predicted_contents=result.predicted_contents,
            )
            evaluation_results_content.append(content_eval)

        # 聚合指标
        path_metrics = self.improved_calculator.aggregate_metrics(
            evaluation_results_path, query_times
        )
        content_metrics = self.improved_calculator.aggregate_metrics(
            evaluation_results_content, query_times
        )

        # 获取实际的K值
        avg_k_int = int(round(path_metrics.avg_k))

        # 转换为原有格式，但包含对比信息
        return {
            "path_matching": {
                "total_queries": path_metrics.total_queries,
                "exact_match_count": path_metrics.exact_match_count,
                "exact_match_rate": path_metrics.exact_match_rate,
                "avg_k": path_metrics.avg_k,
                "success_rate_at_1": path_metrics.success_rate_at_k.get(1, 0.0),
                "success_rate_at_3": path_metrics.success_rate_at_k.get(3, 0.0),
                "success_rate_at_5": path_metrics.success_rate_at_k.get(5, 0.0),
                "success_rate_at_k": path_metrics.success_rate_at_k.get(avg_k_int, 0.0),
                "precision_at_1": path_metrics.avg_precision_at_k.get(1, 0.0),
                "precision_at_3": path_metrics.avg_precision_at_k.get(3, 0.0),
                "precision_at_5": path_metrics.avg_precision_at_k.get(5, 0.0),
                "precision_at_k": path_metrics.avg_precision_at_k.get(avg_k_int, 0.0),
                "mrr_at_k": path_metrics.avg_mrr,
                "ndcg_at_k": path_metrics.avg_ndcg_at_k.get(avg_k_int, 0.0),
                "f1_score": path_metrics.avg_f1_at_k.get(
                    1, 0.0
                ),  # 使用F1@1作为F1 score
                "avg_query_time_ms": path_metrics.avg_query_time_ms,
                "max_query_time_ms": path_metrics.max_query_time_ms,
                "min_query_time_ms": path_metrics.min_query_time_ms,
            },
            "content_matching": {
                "total_queries": content_metrics.total_queries,
                "exact_match_count": content_metrics.exact_match_count,
                "exact_match_rate": content_metrics.exact_match_rate,
                "avg_k": content_metrics.avg_k,
                "success_rate_at_1": content_metrics.success_rate_at_k.get(1, 0.0),
                "success_rate_at_3": content_metrics.success_rate_at_k.get(3, 0.0),
                "success_rate_at_5": content_metrics.success_rate_at_k.get(5, 0.0),
                "success_rate_at_k": content_metrics.success_rate_at_k.get(
                    avg_k_int, 0.0
                ),
                "precision_at_1": content_metrics.avg_precision_at_k.get(1, 0.0),
                "precision_at_3": content_metrics.avg_precision_at_k.get(3, 0.0),
                "precision_at_5": content_metrics.avg_precision_at_k.get(5, 0.0),
                "precision_at_k": content_metrics.avg_precision_at_k.get(
                    avg_k_int, 0.0
                ),
                "mrr_at_k": content_metrics.avg_mrr,
                "ndcg_at_k": content_metrics.avg_ndcg_at_k.get(avg_k_int, 0.0),
                "f1_score": content_metrics.avg_f1_at_k.get(1, 0.0),
                "avg_query_time_ms": content_metrics.avg_query_time_ms,
                "max_query_time_ms": content_metrics.max_query_time_ms,
                "min_query_time_ms": content_metrics.min_query_time_ms,
            },
            # 保持向后兼容，使用路径匹配作为默认结果
            "total_queries": path_metrics.total_queries,
            "exact_match_count": path_metrics.exact_match_count,
            "exact_match_rate": path_metrics.exact_match_rate,
            "avg_k": path_metrics.avg_k,
            "success_rate_at_1": path_metrics.success_rate_at_k.get(1, 0.0),
            "success_rate_at_3": path_metrics.success_rate_at_k.get(3, 0.0),
            "success_rate_at_5": path_metrics.success_rate_at_k.get(5, 0.0),
            "success_rate_at_k": path_metrics.success_rate_at_k.get(avg_k_int, 0.0),
            "precision_at_1": path_metrics.avg_precision_at_k.get(1, 0.0),
            "precision_at_3": path_metrics.avg_precision_at_k.get(3, 0.0),
            "precision_at_5": path_metrics.avg_precision_at_k.get(5, 0.0),
            "precision_at_k": path_metrics.avg_precision_at_k.get(avg_k_int, 0.0),
            "mrr_at_k": path_metrics.avg_mrr,
            "ndcg_at_k": path_metrics.avg_ndcg_at_k.get(avg_k_int, 0.0),
            "f1_score": path_metrics.avg_f1_at_k.get(1, 0.0),
            "avg_query_time_ms": path_metrics.avg_query_time_ms,
            "max_query_time_ms": path_metrics.max_query_time_ms,
            "min_query_time_ms": path_metrics.min_query_time_ms,
        }

    def _empty_metrics(self) -> dict:
        """空指标"""
        empty_single = {
            "total_queries": 0,
            "exact_match_count": 0,
            "exact_match_rate": 0.0,
            "avg_k": 0.0,
            "success_rate_at_1": 0.0,
            "success_rate_at_3": 0.0,
            "success_rate_at_5": 0.0,
            "success_rate_at_k": 0.0,
            "precision_at_1": 0.0,
            "precision_at_3": 0.0,
            "precision_at_5": 0.0,
            "precision_at_k": 0.0,
            "mrr_at_k": 0.0,
            "ndcg_at_k": 0.0,
            "f1_score": 0.0,
            "avg_query_time_ms": 0.0,
            "max_query_time_ms": 0.0,
            "min_query_time_ms": 0.0,
        }

        return {
            "path_matching": empty_single.copy(),
            "content_matching": empty_single.copy(),
            **empty_single,  # 向后兼容
        }


class EndToEndEvaluator:
    """端到端评测器"""

    def __init__(self, venv_path: str = ".venv"):
        self.service_manager = ServiceManager(venv_path)
        self.metrics_calculator = MetricsCalculator()
        self.repo_paths = {}  # 缓存解析出的仓库路径
        self.result_collector = QueryResultCollector()  # 查询结果收集器

    def load_test_data(self, jsonl_file: str) -> dict[str, list[TestCase]]:
        """加载测试数据并按repo分组"""
        logger.info(f"加载测试数据: {jsonl_file}")

        repo_test_cases = defaultdict(list)

        with open(jsonl_file, encoding="utf-8") as f:
            for line_num, line in enumerate(f, 1):
                try:
                    data = json.loads(line.strip())

                    # 从filepath解析出仓库路径
                    original_filepath = data.get("filepath", "")
                    repo_name = data.get("repo", "")
                    if not original_filepath or not repo_name:
                        # 非法数据行，跳过
                        continue

                    repo_path = self._extract_repo_path(original_filepath, repo_name)
                    if not os.path.exists(repo_path):
                        raise ValueError(f"仓库路径不存在: {repo_path}")

                    # 缓存仓库路径
                    if repo_name not in self.repo_paths:
                        self.repo_paths[repo_name] = repo_path

                    # 提取相对路径
                    relative_path = self._extract_relative_path(
                        original_filepath, repo_name
                    )

                    # 解析每个函数的测试用例：兼容多种schema
                    functions = data.get("functions", [])
                    for func_index, func in enumerate(functions):
                        # ground truth 始终来自 body
                        ground_truth_content = (func.get("body", "")) # ClearCommentsForC
                        if not ground_truth_content:
                            continue

                        added_any = False

                        # 1) 新版：按intent组织
                        #   - func.intents: { intent_name: [query_str | {query|text:..., ...}] }
                        #   - 或 func.queries: [{ intent: "...", query|text: "..." }, ...]
                        intents_obj = func.get("intents")
                        if isinstance(intents_obj, dict):
                            for intent_name, intent_queries in intents_obj.items():
                                if isinstance(intent_queries, list):
                                    for query_index, q in enumerate(intent_queries):
                                        # 支持字符串或对象形式
                                        if isinstance(q, str):
                                            query_text = q.strip()
                                            tool_q = None
                                        elif isinstance(q, dict):
                                            query_text = (
                                                q.get("query")
                                                or q.get("text")
                                                or q.get("q")
                                                or ""
                                            ).strip()
                                            tool_q = (
                                                q.get("tool_query") or q.get("toolQuery") or None
                                            )
                                        else:
                                            query_text = ""
                                            tool_q = None
                                        if not query_text:
                                            continue
                                        test_case = TestCase(
                                            query=query_text,
                                            ground_truth_file=relative_path,
                                            ground_truth_content=ground_truth_content,
                                            repo=repo_name,
                                            original_filepath=original_filepath,
                                            intent=str(intent_name),
                                            tool_query=tool_q,
                                            func_index=func_index,
                                            query_index=query_index,
                                            query_source_type="intents",
                                            intent_name=intent_name,
                                        )
                                        repo_test_cases[repo_name].append(test_case)
                                        added_any = True

                        # 2) 新版：generated_queries 数组（每个元素包含 intent + query）
                        if not added_any:
                            gen_queries = func.get("generated_queries")
                            if isinstance(gen_queries, list):
                                for query_index, q in enumerate(gen_queries):
                                    if isinstance(q, dict):
                                        query_text = (
                                            q.get("query") or q.get("text") or q.get("q") or ""
                                        ).strip()
                                        intent_name = q.get("intent") or q.get("type") or "default"
                                        tool_q = q.get("tool_query") or q.get("toolQuery") or None
                                    elif isinstance(q, str):
                                        query_text = q.strip()
                                        intent_name = "default"
                                        tool_q = None
                                    else:
                                        query_text = ""
                                        intent_name = "default"
                                        tool_q = None
                                    if not query_text:
                                        continue
                                    test_case = TestCase(
                                        query=query_text,
                                        ground_truth_file=relative_path,
                                        ground_truth_content=ground_truth_content,
                                        repo=repo_name,
                                        original_filepath=original_filepath,
                                        intent=str(intent_name),
                                        tool_query=tool_q,
                                        func_index=func_index,
                                        query_index=query_index,
                                        query_source_type="generated_queries",
                                        intent_name=None,
                                    )
                                    repo_test_cases[repo_name].append(test_case)
                                    added_any = True

                        # 3) 兼容 queries 数组（每个元素包含 intent + query/text）
                        if not added_any:
                            queries_arr = func.get("queries")
                            if isinstance(queries_arr, list):
                                for query_index, q in enumerate(queries_arr):
                                    if isinstance(q, dict):
                                        query_text = (
                                            q.get("query") or q.get("text") or q.get("q") or ""
                                        ).strip()
                                        intent_name = q.get("intent") or q.get("type") or "default"
                                        tool_q = q.get("tool_query") or q.get("toolQuery") or None
                                    elif isinstance(q, str):
                                        query_text = q.strip()
                                        intent_name = "default"
                                        tool_q = None
                                    else:
                                        query_text = ""
                                        intent_name = "default"
                                        tool_q = None
                                    if not query_text:
                                        continue
                                    test_case = TestCase(
                                        query=query_text,
                                        ground_truth_file=relative_path,
                                        ground_truth_content=ground_truth_content,
                                        repo=repo_name,
                                        original_filepath=original_filepath,
                                        intent=str(intent_name),
                                        tool_query=tool_q,
                                        func_index=func_index,
                                        query_index=query_index,
                                        query_source_type="queries",
                                        intent_name=None,
                                    )
                                    repo_test_cases[repo_name].append(test_case)
                                    added_any = True

                        # 4) 兼容老版：单一 comment_zh
                        if not added_any:
                            comment = (func.get("comment_zh") or "").strip()
                            if comment:
                                test_case = TestCase(
                                    query=comment,
                                    ground_truth_file=relative_path,
                                    ground_truth_content=ground_truth_content,
                                    repo=repo_name,
                                    original_filepath=original_filepath,
                                    intent="default",
                                    tool_query=None,
                                    func_index=func_index,
                                    query_index=0,  # comment_zh只有一个query
                                    query_source_type="comment_zh",
                                    intent_name=None,
                                )
                                repo_test_cases[repo_name].append(test_case)

                except Exception as e:
                    logger.warning(f"解析第{line_num}行失败: {e}")

        logger.info(f"加载完成: {len(repo_test_cases)} 个仓库")
        for repo, cases in repo_test_cases.items():
            logger.info(f"  - {repo}: {len(cases)} 个测试用例")
            logger.info(f"    仓库路径: {self.repo_paths.get(repo, 'Unknown')}")

        return dict(repo_test_cases)

    def _extract_repo_path(self, filepath: str, repo_name: str) -> str:
        """从完整文件路径中提取仓库路径"""
        # 找到repo名称在路径中的位置
        parts = filepath.split("/")
        try:
            repo_index = parts.index(repo_name)
            # 返回包含repo名称的完整路径
            repo_path_parts = parts[: repo_index + 1]
            return "/".join(repo_path_parts)
        except ValueError:
            logger.error(f"无法从路径 {filepath} 中找到仓库名称 {repo_name}")
            # 如果找不到，尝试从路径中推断
            # 假设仓库名称是路径中的某个目录
            for i, part in enumerate(parts):
                if repo_name in part:
                    return "/".join(parts[: i + 1])
            # 最后的fallback，返回文件所在目录
            return str(Path(filepath).parent)

    def _extract_relative_path(self, filepath: str, repo: str) -> str:
        """提取相对于仓库根目录的路径"""
        # 找到repo名称在路径中的位置
        parts = filepath.split("/")
        try:
            repo_index = parts.index(repo)
            # 返回repo之后的路径部分
            relative_parts = parts[repo_index + 1 :]
            return "/".join(relative_parts)
        except ValueError:
            # 如果找不到repo名称，返回文件名
            return Path(filepath).name

    def query_service(
        self, query: str, port: int = 5001, top_k: int = 10
    ) -> tuple[list[str], list[str], list[float], float, dict]:
        """查询检索服务"""
        url = f"http://127.0.0.1:{port}/query"

        payload = {"query": query, "top_k": top_k}

        start_time = time.time()
        try:
            response = requests.post(url, json=payload, timeout=120)
            execution_time = (time.time() - start_time) * 1000

            if response.status_code == 200:
                data = response.json()
                results = data.get("results", [])

                file_paths = []
                contents = []
                scores = []

                for result in results:
                    file_path = result.get("file_path", "")
                    content = result.get(
                        "text", ""
                    )  # 获取代码片段内容（字段名是text不是content）
                    score = result.get("score", 0.0)
                    file_paths.append(file_path)
                    contents.append(content)
                    scores.append(score)

                return file_paths, contents, scores, execution_time, data
            else:
                logger.error(
                    f"查询失败: {response.status_code} - {query} - {response.text}"
                )
                error_data = {
                    "error": f"HTTP {response.status_code}",
                    "message": response.text,
                    "query": query
                }
                return [], [], [], execution_time, error_data

        except Exception as e:
            execution_time = (time.time() - start_time) * 1000
            logger.error(f"查询异常: {e} - {query}")
            error_data = {
                "error": str(e),
                "query": query
            }
            return [], [], [], execution_time, error_data

    def _execute_query_variant(
        self, 
        test_case: TestCase, 
        query_text: str,
        query_source: str,
        port: int, 
        case_index: int, 
        total_cases: int
    ) -> Optional[QueryResult]:
        """执行单个查询任务（user/tool 两种来源）"""
        if not query_text or not query_text.strip():
            return None

        logger.debug(f"查询 {case_index}/{total_cases} [{query_source}]: {query_text[:50]}...")

        # 执行查询
        file_paths, contents, scores, exec_time, raw_result = self.query_service(
            query_text, port=port
        )

        # 如果查询失败，跳过这个测试用例
        if not file_paths:
            logger.warning(f"查询 {case_index} 可能失败，跳过 - {query_text}")
            return None

        # 检查路径匹配
        is_path_match = test_case.ground_truth_file in file_paths
        path_match_rank = None
        if is_path_match:
            path_match_rank = file_paths.index(test_case.ground_truth_file) + 1

        # BadCase Debug: 调试没找到或排名不佳的情况
        self._debug_badcase(
            test_case, file_paths, contents, scores, is_path_match, path_match_rank, case_index, query_source
        )

        return QueryResult(
            query=query_text,
            ground_truth_file=test_case.ground_truth_file,
            ground_truth_content=test_case.ground_truth_content,
            predicted_files=file_paths,
            predicted_contents=contents,
            scores=scores,
            execution_time_ms=exec_time,
            is_match=is_path_match,
            match_rank=path_match_rank,
            intent=test_case.intent,
            query_source=query_source,
            raw_result=raw_result,
        )

    def _debug_badcase(
        self,
        test_case: TestCase,
        file_paths: list[str],
        contents: list[str],
        scores: list[float],
        is_path_match: bool,
        path_match_rank: Optional[int],
        case_index: int,
        query_source: str,
    ):
        """调试bad case：没找到或排名不佳的情况"""
        
        # 只有在DEBUG级别下才输出badcase调试信息
        if logger.level > logging.DEBUG:
            return
        
        # Case 1: 没有找到ground truth文件
        if not is_path_match:
            logger.debug(f"🔍 BadCase #{case_index} [{query_source}] - 未找到目标文件:")
            logger.debug(f"  Query: {test_case.query}")
            logger.debug(f"  Ground Truth File: {test_case.ground_truth_file}")
            logger.debug(f"  Ground Truth Content Preview: {test_case.ground_truth_content[:200]}...")
            logger.debug(f"  找到的文件数量: {len(file_paths)}")
            
            for i, (file_path, content, score) in enumerate(zip(file_paths, contents, scores)):
                logger.debug(f"    #{i+1} (score: {score:.4f}): {file_path}")
                logger.debug(f"        Content Preview: \n{content}")
                logger.debug("  " + "─" * 80)
            logger.debug("  " + "#" * 80)
        
        # Case 2: 找到了但不在第一位
        elif path_match_rank and path_match_rank > 1:
            logger.debug(f"📍 BadCase #{case_index} [{query_source}] - 目标文件排名较低:")
            logger.debug(f"  Query: {test_case.query}")
            logger.debug(f"  Ground Truth File: {test_case.ground_truth_file}")
            logger.debug(f"  实际排名: 第 {path_match_rank} 位 (score: {scores[path_match_rank-1]:.4f})")
            
            # 打印排在前面的结果
            logger.debug(f"  排在前面的结果:")
            for i in range(path_match_rank - 1):
                logger.debug(f"    #{i+1} (score: {scores[i]:.4f}): {file_paths[i]}")
                logger.debug(f"        Content Preview: \n{contents[i]}")
                logger.debug("  " + "─" * 80)
            logger.debug("  " + "#" * 80)

    def evaluate_repo(self, repo: str, test_cases: list[TestCase]) -> list[QueryResult]:
        """评测单个仓库"""
        logger.info(f"开始评测仓库: {repo} ({len(test_cases)} 个测试用例)")

        # 获取仓库路径
        repo_path = self.repo_paths.get(repo)
        if not repo_path:
            logger.error(f"未找到仓库 {repo} 的路径信息")
            return []

        # 检查仓库路径是否存在
        if not Path(repo_path).exists():
            logger.error(f"仓库路径不存在: {repo_path}")
            return []

        # 启动服务
        port = 5001
        if not self.service_manager.start_service(repo_path, repo, port):
            logger.error(f"无法启动服务: {repo}")
            return []

        results = []

        try:
            # 先进行健康检查确保服务正常
            try:
                health_response = requests.get(
                    f"http://127.0.0.1:{port}/health", timeout=5
                )
                if health_response.status_code != 200:
                    logger.error(f"服务健康检查失败: {health_response.status_code}")
                    return []
            except requests.exceptions.RequestException as e:
                logger.error(f"服务健康检查异常: {e}")
                return []

            # 执行并发查询
            logger.info(f"开始执行并发查询，并发数: 5, 共{len(test_cases)}条测试数据")

            with ThreadPoolExecutor(max_workers=5) as executor:
                # 提交所有查询任务
                future_to_task = {}
                submit_count = 0
                for i, test_case in enumerate(test_cases):
                    # 用户原始query
                    fu = executor.submit(
                        self._execute_query_variant,
                        test_case,
                        test_case.query,
                        "user",
                        port,
                        i + 1,
                        len(test_cases),
                    )
                    future_to_task[fu] = (test_case, "user")
                    submit_count += 1

                    # tool_query（如果有）
                    if test_case.tool_query:
                        ft = executor.submit(
                            self._execute_query_variant,
                            test_case,
                            test_case.tool_query,
                            "tool",
                            port,
                            i + 1,
                            len(test_cases),
                        )
                        future_to_task[ft] = (test_case, "tool")
                        submit_count += 1

                # 收集结果
                completed_count = 0
                for future in as_completed(future_to_task):
                    result = future.result()
                    if result is not None:
                        results.append(result)
                        # 将查询结果保存到结果收集器中
                        test_case, query_type = future_to_task[future]
                        self.result_collector.add_result(test_case, query_type, result.raw_result)

                    completed_count += 1
                    if completed_count % 5 == 0:
                        matches = sum(1 for r in results if r.is_match)
                        logger.info(
                            f"  进度: {completed_count}/{submit_count}, 匹配率: {matches / max(len(results),1) * 100:.1f}% (已完成查询数: {len(results)})"
                        )

        finally:
            # 停止服务
            self.service_manager.stop_service()

        matches = sum(1 for r in results if r.is_match)
        logger.info(f"仓库 {repo} 评测完成: {matches}/{len(results)} 匹配")

        return results

    def run_evaluation(self, jsonl_file: str) -> dict:
        """运行完整评测"""
        logger.info("开始端到端评测")
        start_time = time.time()

        # 0. 创建结果文件路径
        result_file = jsonl_file.replace('.jsonl', '_result.jsonl')
        logger.info(f"查询结果将保存到: {result_file}")

        # 1. 加载测试数据并汇总
        logger.info("=== 步骤1: 加载和汇总测试数据 ===")
        repo_test_cases = self.load_test_data(jsonl_file)

        # 打印汇总信息
        total_test_cases = sum(len(cases) for cases in repo_test_cases.values())
        logger.info("数据汇总完成:")
        logger.info(f"  总仓库数: {len(repo_test_cases)}")
        logger.info(f"  总测试用例数: {total_test_cases}")
        for repo, cases in repo_test_cases.items():
            logger.info(f"  - {repo}: {len(cases)} 个测试用例")

        all_results = []
        repo_metrics = {}
        repo_metrics_by_source: dict[str, dict[str, dict]] = {}
        repo_intent_results: dict[str, dict[str, list[QueryResult]]] = {}

        # 2. 逐个仓库评测
        logger.info("=== 步骤2: 逐个仓库评测 ===")
        for i, (repo, test_cases) in enumerate(repo_test_cases.items(), 1):
            logger.info(f"\n--- 评测仓库 {i}/{len(repo_test_cases)}: {repo} ---")
            try:
                results = self.evaluate_repo(repo, test_cases)
                all_results.extend(results)

                # 计算单个仓库的指标（整体）
                repo_metrics[repo] = self.metrics_calculator.calculate_metrics(results)

                # 按intent分组存储仓库级结果
                intent_grouped: dict[str, list[QueryResult]] = {}
                for r in results:
                    intent_grouped.setdefault(r.intent or "default", []).append(r)
                repo_intent_results[repo] = intent_grouped

                # 计算单个仓库按来源的指标
                repo_metrics_by_source.setdefault(repo, {})
                tmp_by_src: dict[str, list[QueryResult]] = {"user": [], "tool": []}
                for r in results:
                    src = getattr(r, "query_source", "user") or "user"
                    tmp_by_src.setdefault(src, []).append(r)
                for src, rs in tmp_by_src.items():
                    if rs:
                        repo_metrics_by_source[repo][src] = self.metrics_calculator.calculate_metrics(rs)

                # 输出仓库评测结果
                matches = sum(1 for r in results if r.is_match)
                if len(results) != 0:
                    logger.info(
                        f"仓库 {repo} 评测完成: {matches}/{len(results)} 匹配 ({matches / len(results) * 100:.1f}%)"
                    )

            except Exception as e:
                logger.error(f"评测仓库 {repo} 失败: {e}")
                import traceback

                logger.error(traceback.format_exc())
                continue

        # 计算总体指标（按来源区分）
        results_by_source: dict[str, list[QueryResult]] = {"user": [], "tool": []}
        for r in all_results:
            src = getattr(r, "query_source", "user") or "user"
            results_by_source.setdefault(src, []).append(r)
        overall_metrics_by_source: dict[str, dict] = {}
        for src, rs in results_by_source.items():
            overall_metrics_by_source[src] = self.metrics_calculator.calculate_metrics(rs)

        # 保持向后兼容：默认展示user来源（若不存在则任意一个）
        overall_metrics = overall_metrics_by_source.get("user") or next(
            iter(overall_metrics_by_source.values()), self.metrics_calculator._empty_metrics()
        )

        # 计算总体intent维度指标与诊断
        overall_intent_metrics: dict[str, dict] = {}
        overall_intent_outcomes: dict[str, dict] = {}
        overall_intent_metrics_by_source: dict[str, dict[str, dict]] = {"user": {}, "tool": {}}
        intent_to_results: dict[str, list[QueryResult]] = {}
        for r in all_results:
            intent_to_results.setdefault(r.intent or "default", []).append(r)
        for intent_name, results in intent_to_results.items():
            overall_intent_metrics[intent_name] = self.metrics_calculator.calculate_metrics(results)
            overall_intent_outcomes[intent_name] = self._summarize_outcomes(results)
            # 分来源
            tmp_by_src: dict[str, list[QueryResult]] = {"user": [], "tool": []}
            for r in results:
                src = getattr(r, "query_source", "user") or "user"
                tmp_by_src.setdefault(src, []).append(r)
            for src, rs in tmp_by_src.items():
                if rs:
                    overall_intent_metrics_by_source.setdefault(src, {})[intent_name] = self.metrics_calculator.calculate_metrics(rs)

        # 仓库级intent指标与诊断
        repo_intent_metrics: dict[str, dict[str, dict]] = {}
        repo_intent_outcomes: dict[str, dict[str, dict]] = {}
        repo_intent_metrics_by_source: dict[str, dict[str, dict[str, dict]]] = {}
        for repo, intent_results in repo_intent_results.items():
            repo_intent_metrics[repo] = {}
            repo_intent_outcomes[repo] = {}
            repo_intent_metrics_by_source[repo] = {"user": {}, "tool": {}}
            for intent_name, results in intent_results.items():
                repo_intent_metrics[repo][intent_name] = self.metrics_calculator.calculate_metrics(results)
                repo_intent_outcomes[repo][intent_name] = self._summarize_outcomes(results)
                # 分来源
                tmp_by_src: dict[str, list[QueryResult]] = {"user": [], "tool": []}
                for r in results:
                    src = getattr(r, "query_source", "user") or "user"
                    tmp_by_src.setdefault(src, []).append(r)
                for src, rs in tmp_by_src.items():
                    if rs:
                        repo_intent_metrics_by_source[repo].setdefault(src, {})[intent_name] = self.metrics_calculator.calculate_metrics(rs)

        total_time = time.time() - start_time

        # 汇总结果
        summary = {
            "overall_metrics": overall_metrics,
            "repo_metrics": repo_metrics,
            "overall_metrics_by_source": overall_metrics_by_source,
            "repo_metrics_by_source": repo_metrics_by_source,
            "intent_metrics_overall": overall_intent_metrics,
            "intent_outcomes_overall": overall_intent_outcomes,
            "intent_metrics_per_repo": repo_intent_metrics,
            "intent_outcomes_per_repo": repo_intent_outcomes,
            "intent_metrics_overall_by_source": overall_intent_metrics_by_source,
            "intent_metrics_per_repo_by_source": repo_intent_metrics_by_source,
            "total_time_seconds": total_time,
            "total_repos": len(repo_test_cases),
            "successful_repos": len(repo_metrics),
        }

        # 保存查询结果到JSONL文件
        self._save_query_results(jsonl_file, result_file)

        self._print_summary(summary)

        return summary

    def _save_query_results(self, original_jsonl: str, result_file: str):
        """保存查询结果到JSONL文件 - 将结果写回到原始JSON中对应的query对象"""
        logger.info(f"开始保存查询结果到: {result_file}")
        
        total_saved = 0
        with open(original_jsonl, 'r', encoding='utf-8') as infile, \
             open(result_file, 'w', encoding='utf-8') as outfile:
            
            for line_num, line in enumerate(infile, 1):
                try:
                    data = json.loads(line.strip())
                    
                    # 获取该文件的查询结果
                    repo_name = data.get("repo", "")
                    original_filepath = data.get("filepath", "")
                    file_results = self.result_collector.get_results_for_file(repo_name, original_filepath)
                    
                    # 为每个function的每个query添加查询结果
                    functions = data.get("functions", [])
                    for func_index, func in enumerate(functions):
                        if func_index not in file_results:
                            continue
                            
                        func_results = file_results[func_index]
                        
                        # 处理不同类型的query源
                        for source_type, source_data in func_results.items():
                            if source_type == "intents" and "intents" in func:
                                # 处理intents类型
                                for intent_name, query_results in source_data.items():
                                    if intent_name in func["intents"]:
                                        intent_queries = func["intents"][intent_name]
                                        if isinstance(intent_queries, list):
                                            for query_index, q in enumerate(intent_queries):
                                                if query_index in query_results:
                                                    query_result_data = query_results[query_index]
                                                    if isinstance(q, dict):
                                                        # 为字典形式的query添加结果
                                                        q.update(query_result_data)
                                                    elif isinstance(q, str):
                                                        # 字符串形式需要转换为字典
                                                        new_q = {"query": q}
                                                        new_q.update(query_result_data)
                                                        func["intents"][intent_name][query_index] = new_q
                                                        
                            elif source_type in ["generated_queries", "queries"] and source_type in func:
                                # 处理generated_queries和queries类型
                                for query_index, query_results in source_data.items():
                                    if isinstance(query_index, int) and query_index < len(func[source_type]):
                                        func[source_type][query_index].update(query_results)
                                        
                            elif source_type == "comment_zh" and "comment_zh" in func:
                                # 处理comment_zh类型
                                if 0 in source_data:
                                    query_results = source_data[0]
                                    # 将comment_zh转换为字典形式并添加结果
                                    if isinstance(func["comment_zh"], str):
                                        func["comment_zh"] = {"query": func["comment_zh"]}
                                    func["comment_zh"].update(query_results)
                    
                    # 写入修改后的JSON
                    outfile.write(json.dumps(data, ensure_ascii=False) + '\n')
                    total_saved += 1
                    
                except Exception as e:
                    logger.warning(f"处理第{line_num}行时出错: {e}")
                    # 如果处理失败，写入原始数据
                    outfile.write(line)
                    total_saved += 1
        
        logger.info(f"查询结果保存完成: {total_saved} 条记录保存到 {result_file}")

    def _print_summary(self, summary: dict):
        """打印评测摘要 - 按来源分组输出，去掉重复内容"""
        overall_by_source = summary.get("overall_metrics_by_source", {})
        repo_by_source = summary.get("repo_metrics_by_source", {})
        intent_overall_by_source = summary.get("intent_metrics_overall_by_source", {})
        intent_repo_by_source = summary.get("intent_metrics_per_repo_by_source", {})

        # 按照user -> ### -> tool的顺序输出完整结果
        for i, src_label in enumerate(("user", "tool")):
            # 如果是tool，先输出分隔符
            if i == 1:
                logger.info("")
                logger.info("###" * 30)
                logger.info("")

            over = overall_by_source.get(src_label)
            if not over:
                continue

            # 整体评测结果
            logger.info("=" * 80)
            logger.info(f"评测结果 ({src_label.upper()}) - 路径匹配 vs 内容匹配对比")
            logger.info("=" * 80)

            pm = over.get("path_matching", over)
            cm = over.get("content_matching", over)

            logger.info(f"总查询数: {over.get('total_queries', 0)}")
            logger.info(f"平均输出K值: {over.get('avg_k', 0.0):.2f}")

            # 对比表格
            logger.info("=" * 70)
            logger.info("路径匹配 vs 内容匹配 对比结果")
            logger.info("=" * 70)
            logger.info(f"{'指标':<15} {'路径匹配':<12} {'内容匹配':<12} {'差异':<10}")
            logger.info("-" * 70)
            for display_name, key in [
                ("Success@1", "success_rate_at_1"),
                ("Success@3", "success_rate_at_3"),
                ("Success@5", "success_rate_at_5"),
                ("Success@k", "success_rate_at_k"),
            ]:
                pv = pm.get(key, 0.0)
                cv = cm.get(key, 0.0)
                logger.info(f"{display_name:<15} {pv:<12.3f} {cv:<12.3f} {cv-pv:>+7.3f}")

            for display_name, key in [
                ("P@1", "precision_at_1"),
                ("P@3", "precision_at_3"),
                ("P@5", "precision_at_5"),
            ]:
                pv = pm.get(key, 0.0)
                cv = cm.get(key, 0.0)
                logger.info(f"{display_name:<15} {pv:<12.3f} {cv:<12.3f} {cv-pv:>+7.3f}")

            # 添加MRR指标
            logger.info(
                f"{'MRR@K':<15} {pm.get('mrr_at_k',0.0):<12.3f} {cm.get('mrr_at_k',0.0):<12.3f} {(cm.get('mrr_at_k',0.0)-pm.get('mrr_at_k',0.0)):>+7.3f}"
            )

            logger.info("=" * 40)
            logger.info("查询时延统计:")
            logger.info("=" * 40)
            logger.info(f"  平均: {over.get('avg_query_time_ms',0.0):.2f}ms")
            logger.info(f"  最大: {over.get('max_query_time_ms',0.0):.2f}ms")
            logger.info(f"  最小: {over.get('min_query_time_ms',0.0):.2f}ms")
            logger.info(f"总耗时: {summary['total_time_seconds']:.2f}秒")
            logger.info(
                f"成功评测仓库: {summary['successful_repos']}/{summary['total_repos']}"
            )

            # Intent整体表现
            logger.info("=" * 70)
            logger.info(f"按Intent整体表现 ({src_label.upper()}):")
            logger.info("=" * 70)
            intents_metrics = intent_overall_by_source.get(src_label, {})
            for intent_name, metrics in intents_metrics.items():
                m_path = metrics.get("path_matching", metrics)
                m_content = metrics.get("content_matching", metrics)
                logger.info(f"🔎 Intent: {intent_name}")
                logger.info(
                    f"   路径: S@1/3/5/k {m_path.get('success_rate_at_1',0):.3f}/{m_path.get('success_rate_at_3',0):.3f}/{m_path.get('success_rate_at_5',0):.3f}/{m_path.get('success_rate_at_k',0):.3f}; P@1/3/5 {m_path.get('precision_at_1',0):.3f}/{m_path.get('precision_at_3',0):.3f}/{m_path.get('precision_at_5',0):.3f}; MRR {m_path.get('mrr_at_k',0):.3f}; nDCG {m_path.get('ndcg_at_k',0):.3f}"
                )
                logger.info(
                    f"   内容: S@1/3/5/k {m_content.get('success_rate_at_1',0):.3f}/{m_content.get('success_rate_at_3',0):.3f}/{m_content.get('success_rate_at_5',0):.3f}/{m_content.get('success_rate_at_k',0):.3f}; P@1/3/5 {m_content.get('precision_at_1',0):.3f}/{m_content.get('precision_at_3',0):.3f}/{m_content.get('precision_at_5',0):.3f}; MRR {m_content.get('mrr_at_k',0):.3f}; nDCG {m_content.get('ndcg_at_k',0):.3f}"
                )

            # # 各仓库详情
            # logger.info("=" * 70)
            # logger.info(f"各仓库详情 ({src_label.upper()}):")
            # logger.info("=" * 70)
            # for repo, src_map in repo_by_source.items():
            #     metrics = src_map.get(src_label)
            #     if not metrics:
            #         continue
            #     repo_path = metrics.get("path_matching", metrics)
            #     repo_content = metrics.get("content_matching", metrics)
            #     logger.info(f"📁 {repo}:")
            #     logger.info(f"   平均输出K值: {repo_path.get('avg_k', 0.0):.2f}")
            #     logger.info(
            #         f"   路径匹配Success@k: {repo_path.get('success_rate_at_k', 0.0):.3f}"
            #     )
            #     logger.info(
            #         f"   内容匹配Success@k: {repo_content.get('success_rate_at_k', 0.0):.3f}"
            #     )
            #     logger.info(
            #         f"   查询时延: 平均 {metrics.get('avg_query_time_ms', 0.0):.2f}ms"
            #     )

            # # 各仓库的Intent详情
            # logger.info("=" * 70)
            # logger.info(f"各仓库Intent详情 ({src_label.upper()}):")
            # logger.info("=" * 70)
            # for repo, src_map in intent_repo_by_source.items():
            #     intents = src_map.get(src_label, {})
            #     if not intents:
            #         continue
            #     logger.info(f"📁 {repo}:")
            #     for intent_name, metrics in intents.items():
            #         m_path = metrics.get("path_matching", metrics)
            #         m_content = metrics.get("content_matching", metrics)
            #         logger.info(f"   🔎 Intent: {intent_name}")
            #         logger.info(
            #             f"      路径匹配 Success@k: {m_path.get('success_rate_at_k',0):.3f}, 内容匹配 Success@k: {m_content.get('success_rate_at_k',0):.3f}"
            #         )

    def _summarize_outcomes(self, results: list[QueryResult]) -> dict:
        """基于路径匹配的检索结果诊断统计"""
        found_at_1 = 0
        found_but_late = 0
        not_found = 0
        for r in results:
            if r.match_rank == 1:
                found_at_1 += 1
            elif r.match_rank and r.match_rank > 1:
                found_but_late += 1
            else:
                not_found += 1
        return {
            "found_at_1": found_at_1,
            "found_but_late": found_but_late,
            "not_found": not_found,
            "total": len(results),
        }


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="端到端评测工具")
    parser.add_argument(
        "--test-file", default="testset/testset_lite_3.jsonl", help="测试数据文件"
    )
    parser.add_argument("--venv-path", default=".venv", help="虚拟环境路径")
    parser.add_argument("--log-level", default="DEBUG", help="日志级别")
    parser.add_argument(
        "--log-file", 
        type=str,
        help="指定日志文件路径，同时输出到控制台和文件"
    )

    args = parser.parse_args()

    # 设置日志
    level = getattr(logging, args.log_level.upper())
    
    # 配置日志格式
    log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    date_format = "%Y-%m-%d %H:%M:%S"
    
    # 获取根logger
    root_logger = logging.getLogger()
    root_logger.setLevel(level)
    
    # 清除可能存在的默认handler
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 创建控制台handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(level)
    console_formatter = logging.Formatter(log_format, date_format)
    console_handler.setFormatter(console_formatter)
    root_logger.addHandler(console_handler)
    
    # 如果指定了日志文件，创建文件handler
    if args.log_file:
        file_handler = logging.FileHandler(args.log_file, mode='a', encoding='utf-8')
        file_handler.setLevel(level)
        file_formatter = logging.Formatter(log_format, date_format)
        file_handler.setFormatter(file_formatter)
        root_logger.addHandler(file_handler)
        logger.info(f"日志同时输出到文件: {args.log_file}")

    # 屏蔽httpx的日志输出
    logging.getLogger("httpx").setLevel(logging.WARNING)

    # 运行评测
    evaluator = EndToEndEvaluator(args.venv_path)
    
    # 如果是DEBUG级别，提示用户将看到详细调试信息
    if args.log_level.upper() == "DEBUG":
        logger.info("🔍 DEBUG模式已启用，将输出详细的调试信息包括BadCase分析")

    # 设置信号处理器，确保程序被中断时能正确清理
    def signal_handler(signum, frame):
        logger.info(f"收到信号 {signum}，正在清理资源...")
        evaluator.service_manager.stop_service()
        sys.exit(0)

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    try:
        summary = evaluator.run_evaluation(args.test_file)

        # 保存结果：拆分为user/tool两份
        overall_by_source = summary.get("overall_metrics_by_source", {})
        intent_overall_by_source = summary.get("intent_metrics_overall_by_source", {})
        repo_by_source = summary.get("repo_metrics_by_source", {})
        intent_repo_by_source = summary.get("intent_metrics_per_repo_by_source", {})

        timestamp = int(time.time())
        for src_label in ("user", "tool"):
            payload = {
                "overall_metrics": overall_by_source.get(src_label, {}),
                "intent_metrics_overall": intent_overall_by_source.get(src_label, {}),
                "repo_metrics": {k: v.get(src_label) for k, v in repo_by_source.items() if v.get(src_label)},
                "intent_metrics_per_repo": {k: v.get(src_label) for k, v in intent_repo_by_source.items() if v.get(src_label)},
                "source": src_label,
                "total_time_seconds": summary.get("total_time_seconds", 0),
            }
            out_path = f"evaluation_summary_{src_label}_{timestamp}.json"
            with open(out_path, "w", encoding="utf-8") as f:
                json.dump(payload, f, ensure_ascii=False, indent=2)
            logger.info(f"\n{src_label} 结果已保存到: {out_path}")

    except KeyboardInterrupt:
        logger.warning("\n评测被用户中断")
        logger.info("正在清理资源...")
    except Exception as e:
        logger.error(f"评测失败: {e}")
        import traceback

        traceback.print_exc()
    finally:
        # 确保服务被停止
        evaluator.service_manager.stop_service()


if __name__ == "__main__":
    main()
