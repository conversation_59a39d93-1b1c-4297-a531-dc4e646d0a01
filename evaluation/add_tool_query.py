#!/usr/bin/env python3
"""
根据给定的 tool use prompt，将每条 query 生成对应的 tool_query 字段并写回 JSONL。
支持以下函数级 schema：
- functions[].generated_queries[]: { intent, query, ... }
- functions[].intents: { intent_name: [ query | {query: ...} ] }
- functions[].queries[]: { intent?, query } or string
- functions[].comment_zh (不会生成，保持兼容)

用法：
python -m evaluation.add_tool_query --in evaluation/testset/xxx.jsonl --out evaluation/testset/xxx.with_tool.jsonl

LLM 调用复用项目内 config.py 的客户端，固定使用 ModelType.FLASH。
"""

import argparse
import json
import os
import sys
from typing import Any, Dict, List
from tqdm import tqdm

# 将项目根目录加入 sys.path 以便导入 config
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from config import sync_llm_call  # noqa: E402

TOOL_PROMPT = """你是一个专业的代码库查询优化器。你的任务是将用户的原始查询转换为更精确、更适合语义检索的查询语句。

## 核心原则
1. **保持语义完整性**：生成完整的、语义丰富的查询语句，而不是简单的关键词
2. **增强技术表达**：突出关键的技术术语、类名、函数名等
3. **扩展相关概念**：包含同义词、相关技术概念和实现方式
4. **优化检索效果**：生成最适合embedding模型理解的查询形式

## 优化策略
- **理解意图**：深入理解用户的真实需求（理解、调试、实现、测试等）
- **技术扩展**：为中文概念补充对应的英文技术术语
- **实现导向**：从代码实现的角度重新表述查询
- **多层次覆盖**：包含具体实现、抽象概念和使用场景

## 输出格式
直接输出优化后的查询语句，不要包含任何函数调用格式或解释文字。

## 示例转换

### 理解类查询
用户输入："这个DividerTmp类在项目中是做什么用的？"
输出：DividerTmp类的功能作用和使用场景 分割线组件实现 divider component用途

### 问题排查查询  
用户输入："用户反馈设置页面的分割线颜色显示不对"
输出：设置页面分割线颜色显示异常问题 divider color display issue 样式渲染错误处理

### 功能实现查询
用户输入："需要实现类似阅读器的翻页效果"  
输出：阅读器翻页效果实现方案 page turning animation reader 页面切换动画逻辑

### 性能优化查询
用户输入："如何优化列表滚动的性能问题"
输出：列表滚动性能优化方案 list scroll performance optimization 虚拟滚动实现

### 测试相关查询
用户输入："怎么测试这个组件的边界情况"
输出：组件边界测试用例设计 component boundary testing 异常情况处理验证

请为以下用户输入生成优化查询："""


def call_llm(user_query: str) -> str:
    """使用项目内统一 LLM 客户端（ModelType.FLASH）生成 tool_query。"""
    prompt = TOOL_PROMPT + user_query
    
    try:
        # 默认使用 flash（config.sync_llm_call 内部会路由到 ModelType.FLASH）
        text = sync_llm_call(prompt, model_type="normal", max_tokens=256)
        
        if not text or not text.strip():
            print(f"警告: LLM返回空响应，查询: {user_query[:50]}...")
            # 提供基本的fallback
            return _generate_fallback_query(user_query)
        
        result = text.strip()
        
        # 清理可能包含的函数调用格式
        if result.startswith('search_codebase(query="') and result.endswith('")'):
            # 提取引号内的内容
            result = result[len('search_codebase(query="'):-2]
        elif result.startswith('search_codebase(query=\'') and result.endswith('\')'):
            result = result[len('search_codebase(query=\''):-2]
        elif '(' in result and ')' in result:
            print(f"警告: LLM返回包含函数调用格式，已清理: {result}")
            # 尝试提取有用部分
            if '"' in result:
                parts = result.split('"')
                if len(parts) >= 3:
                    result = parts[1]
        
        return result if result else _generate_fallback_query(user_query)
        
    except Exception as e:
        print(f"错误: LLM调用失败: {e}, 查询: {user_query[:50]}...")
        exit()


def _generate_fallback_query(user_query: str) -> str:
    """为失败的LLM调用生成fallback查询"""
    import re
    
    # 提取关键技术实体
    tech_entities = []
    
    # 1. 提取可能的类名、函数名（英文大小写混合）
    code_entities = re.findall(r'\b[A-Z][a-zA-Z0-9]*\b|\b[a-z][a-zA-Z0-9]*[A-Z][a-zA-Z0-9]*\b', user_query)
    tech_entities.extend(code_entities)
    
    # 2. 提取引号中的内容
    quoted_content = re.findall(r'["\']([^"\']+)["\']', user_query)
    tech_entities.extend(quoted_content)
    
    # 3. 识别查询意图和核心概念
    intent_mapping = {
        '是什么': '功能作用和使用场景',
        '怎么用': '使用方法和实现方式', 
        '如何实现': '实现方案和代码逻辑',
        '为什么': '原理机制和设计思路',
        '出错': '错误处理和问题排查',
        '异常': '异常处理和错误修复',
        '优化': '性能优化和改进方案',
        '测试': '测试用例和验证方法',
        '配置': '配置管理和参数设置'
    }
    
    # 4. 构建语义化查询
    enhanced_query_parts = []
    
    # 保留原始查询的核心语义
    core_query = user_query
    for intent_key, intent_desc in intent_mapping.items():
        if intent_key in user_query:
            enhanced_query_parts.append(intent_desc)
            break
    
    # 添加技术实体
    if tech_entities:
        enhanced_query_parts.extend(tech_entities[:3])  # 限制数量
    
    # 添加核心技术概念
    tech_concepts = ['组件', '函数', '方法', '类', '接口', '实现', '逻辑', '功能', '模块']
    for concept in tech_concepts:
        if concept in user_query:
            enhanced_query_parts.append(concept + '实现')
            break
    
    # 如果没有提取到有效内容，使用原查询
    if not enhanced_query_parts:
        return user_query + ' 实现逻辑和使用方法'
    
    # 组合成语义化查询
    result = ' '.join(enhanced_query_parts)
    return result if len(result) > 10 else user_query + ' ' + result


def inject_tool_query_into_func(func: Dict[str, Any]) -> bool:
    """为一个 function 节点中所有 query 注入 tool_query。返回是否有修改。"""
    modified = False

    # 1) generated_queries
    gqs = func.get("generated_queries")
    if isinstance(gqs, list):
        for item in gqs:
            if isinstance(item, dict):
                q = (item.get("query") or item.get("text") or item.get("q") or "").strip()
                if q and not item.get("tool_query"):
                    tq = call_llm(q)
                    item["tool_query"] = tq
                    modified = True

    # 2) intents dict
    intents = func.get("intents")
    if isinstance(intents, dict):
        for intent_name, items in intents.items():
            if isinstance(items, list):
                for idx, q in enumerate(items):
                    if isinstance(q, str):
                        text = q.strip()
                        if text:
                            # 替换为对象以便存放 tool_query
                            if not any(isinstance(q, dict) for q in items):
                                # 保持最小侵入式修改：仅当当前元素是字符串时改写为对象
                                tq = call_llm(text)
                                items[idx] = {"query": text, "tool_query": tq}
                                modified = True
                    elif isinstance(q, dict):
                        text = (q.get("query") or q.get("text") or q.get("q") or "").strip()
                        if text and not q.get("tool_query"):
                            tq = call_llm(text)
                            q["tool_query"] = tq
                            modified = True

    # 3) queries list
    queries = func.get("queries")
    if isinstance(queries, list):
        for idx, q in enumerate(queries):
            if isinstance(q, str):
                text = q.strip()
                if text:
                    tq = call_llm(text)
                    queries[idx] = {"query": text, "tool_query": tq}
                    modified = True
            elif isinstance(q, dict):
                text = (q.get("query") or q.get("text") or q.get("q") or "").strip()
                if text and not q.get("tool_query"):
                    tq = call_llm(text)
                    q["tool_query"] = tq
                    modified = True

    # 4) 不处理 comment_zh（旧版）
    return modified


def process_file(in_path: str, out_path: str):
    with open(in_path, "r", encoding="utf-8") as fin, open(out_path, "w", encoding="utf-8") as fout:
        for line in tqdm(fin):
            line = line.strip()
            if not line:
                continue
            obj = json.loads(line)
            funcs: List[Dict[str, Any]] = obj.get("functions", [])
            any_modified = False
            for func in funcs:
                if inject_tool_query_into_func(func):
                    any_modified = True
            fout.write(json.dumps(obj, ensure_ascii=False) + "\n")


def main():
    parser = argparse.ArgumentParser(description="为测试集JSONL注入tool_query字段（使用ModelType.FLASH）")
    parser.add_argument("--in", dest="in_file", required=True, help="输入JSONL")
    parser.add_argument("--out", dest="out_file", required=True, help="输出JSONL")
    args = parser.parse_args()

    process_file(args.in_file, args.out_file)


if __name__ == "__main__":
    main()
