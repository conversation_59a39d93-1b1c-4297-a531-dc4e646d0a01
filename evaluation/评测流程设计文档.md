## 代码检索评测流程设计文档

本评测方案以结合多维指标与端到端工程控制，既保证了结果的可信度与可解释性，又能有效揭示系统在检索、切分、聚合、排序等环节的优劣与改进方向。

### 1. 评测背景与目标
- **目标**: 系统性评估代码检索系统在真实仓库上的检索与排序能力，兼顾可解释性与可复现性。
- **核心问题**: 面向开发者自然语言意图（来自函数中文注释），检索系统是否能迅速、准确地定位到正确文件与对应代码片段。
- **设计原则**:
  - **双通道评估**: 同时衡量“路径匹配”和“内容匹配”，从文件定位与代码片段正确性两个维度评价系统。
  - **多指标覆盖**: 结合查准、查全、排名质量与时延，形成全面刻画。
  - **工程可复现**: 端到端启动实际服务，控制并发与端口占用，保证评测可复现、可比较。

### 2. 数据集设计与样本抽取
- **样本来源**: JSONL，每行一个文件样本，包含仓库名、文件绝对路径、函数列表、中文注释、函数体等。
- **查询构造**: 对每个函数，使用其中文注释作为自然语言查询，贴近实际开发者提问习惯。
- **真值定义**:
  - **路径真值**: 样本中的文件路径（评测时标准化），用于衡量文件级定位正确性。
  - **内容真值**: 函数体（经去注释与轻度规范化），用于衡量代码片段级正确性。
- **合理性**:
  - 中文注释通常由作者撰写，语义贴合函数职责，用作查询可有效模拟真实检索意图。
  - 同时使用路径与内容作为真值，使评测兼顾“定位”和“语义匹配”，避免单一维度失真。

### 3. 评测流程总览
- **流程步骤**:
  1) 按仓库聚合测试用例，确保每次仅对一个仓库构建与查询，避免跨仓库索引污染。
  2) 启动检索服务并进行健康检查，等待索引构建完成与就绪（含额外缓冲等待以降低冷启动影响）。
  3) 并发提交该仓库的查询（固定并发度），收集每条查询的返回列表、置信分数与执行时延。
  4) 对每条查询进行“路径匹配”和“内容匹配”两条通道的判定与打分。
  5) 在仓库内与全局范围分别聚合指标，形成仓库级与总体评测结果。
  6) 输出可读摘要与机器可读的结果文件，便于快速诊断与长期跟踪。
- **端到端特性**: 以实际 HTTP 服务为被测对象，评估覆盖索引构建、查询处理、结果整合等环节，反映真实线上行为。

### 4. 指标体系与含义
- **主要指标**:
  - **Precision@K / Recall@K / F1@K**: 分别度量查准、查全与折中表现。由于每条查询通常只有一个最相关目标，Recall@K 退化为是否命中（0/1），F1@K 与 Precision@K 等价。
  - **MRR@K**: 关注第一个正确结果的排名，直接反映“用户需要看多久才能看到正确答案”。
  - **nDCG@K**: 融合排名位置与置信分数，衡量排序质量，鼓励高分、靠前地返回正确结果。
  - **Success@K / Success@k(平均输出K)**: 分别在固定 K 与系统平均输出规模下统计命中率，平衡不同系统的输出长度差异。
  - **时延统计**: 平均/最大/最小查询时延，反映可用性与稳定性。
- **合理性**:
  - 多指标交叉验证，避免单指标偏见；排名类与二元命中类指标互补。
  - Success@k 以系统实际输出规模为参照，能更公平地比较不同输出策略的系统。

### 5. 双通道匹配方法论
- **路径匹配（文件级）**:
  - 定义: 将真值文件路径与返回列表中的路径进行规范化比对，判断是否命中及其排名。
  - 价值: 直接衡量“能否把用户带到正确文件”，对导航体验尤为关键。
  - 限制: 对仓库重构、路径变更敏感，可能低估系统在片段级的真实能力。
- **内容匹配（片段级）**:
  - 定义: 对真值函数体做轻度规范化（去注释、统一空白/大小写），判断其是否作为子串出现在任一返回内容中，并记录最早出现的位置。
  - 价值: 评估“是否找到了正确代码片段”，对解决实际开发问题更贴近。
  - 限制: 子串包含可能带来少量误报；以函数体为最小粒度可有效降低风险，必要时可拓展为 Token/AST 级匹配以进一步稳健。
- **对比视角**:
  - 路径>内容: 更擅长定位文件但片段不够精准，提示切分/重排序改进空间。
  - 内容>路径: 能找对片段但落点不在原文件，提示结构/路径依赖差异或跨文件片段聚合能力。
  - 一致提升/下降: 表明检索与排序质量在两个维度同步变化，可信度更高。

### 6. 工程与复现设计
- **执行方式**: 以单命令触发端到端评测，自动完成服务管理、健康检查、索引构建与并发查询。
- **资源控制**:
  - 每次仅对一个仓库启动服务，评测完即停止，避免残留进程与端口占用。
  - 端口占用检测与清理，确保评测环境稳定。
  - 固定并发度与超时策略，减小系统负载波动对时延与结果的影响。
- **规范化处理**:
  - 路径统一分隔符与前缀，减少跨平台差异导致的误判。
  - 内容轻度规范化，吸收无关格式差异，聚焦功能语义。
- **输出产物**:
  - 控制台汇总：总体与每仓库的“路径匹配 vs 内容匹配”对比表，含关键指标与时延分布。
  - 机器可读：结构化汇总文件，便于历史对比与可视化。

### 7. 评测合理性与有效性保障
- **数据层面**:
  - 中文注释直接来源于源码，语义与实现高度相关，能代表真实意图。
  - 函数体作为内容真值，精度与解释性强，便于人工抽检与误差分析。
- **流程层面**:
  - 仓库级隔离运行，防止索引污染，增强结论的内在有效性。
  - 健康检查与就绪等待，降低冷启动抖动对时延与早期查询结果的干扰。
  - 并发受控、超时保护，保证在不同环境的稳定表现与可比性。
- **指标层面**:
  - 命中、排名、得分三类指标共存，从“是否命中”“命中多快”“排序质量”三方面佐证结论。
  - Success@k 以系统实际输出规模自适应，提升不同系统之间比较的公平性。

### 8. 潜在威胁与缓解策略（Threats to Validity）
- **注释语义噪声**: 个别注释不充分或偏离实现。缓解：采用函数体为内容真值，必要时人工抽检高权重样本。
- **重复/近似函数**: 可能引入子串包含误报。缓解：可引入 Token/AST 结构化相似度或阈值判定作为扩展。
- **路径差异**: 平台分隔符、大小写或仓库重构。缓解：路径规范化与内容通道双保险。
- **负载波动**: 不同机器与时段影响时延。缓解：固定并发、健康检查、超时与统计分布报告；可多次运行取均值以进一步稳健。

### 9. 结果解读与故障定位建议
- **内容明显优于路径**: 检索与排序较好，但索引路径或文件落点存在偏差；优先检查文档切分与结果聚合。
- **路径明显优于内容**: 能定位文件但片段不精准；关注切分粒度、片段重排序与查询改写的语义覆盖。
- **两者均低**: 检索召回不足或索引覆盖问题；检查索引构建、文件忽略规则、嵌入器/倒排参数与查询改写策略。
- **时延异常**: 排查索引规模、I/O、并发设置与服务健康状态；区分冷启动与稳定态表现。

### 10. 可扩展与对比实验
- **Ablation（消融）**: 分别评测仅传统倒排、仅向量召回、加入重排等不同管线的影响。
- **K 值与 Top-K 扫描**: 观察 P@K、MRR、nDCG 随 K 的变化，推断召回-精度折中点。
- **更稳健的内容匹配**: 由子串匹配升级为 Token/AST/语法树对齐或向量相似度阈值，降低误报与漏报。
- **跨版本与重构鲁棒性**: 在路径变化的版本上复测，验证内容通道对重构的稳定性。



