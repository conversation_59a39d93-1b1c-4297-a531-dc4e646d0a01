# Debug 功能使用说明

## 功能概述

新增的 Debug 功能包含两个方面的调试信息：

### 1. BadCase Debug
帮助开发者调试评测过程中的问题案例，特别是：
- **未找到目标文件**：当查询没有返回包含 ground truth 文件的结果时
- **排名不佳**：当目标文件被找到但排名不在第一位时

### 2. Search Process Debug  
帮助开发者调试检索引擎内部流程，包括：
- **Query重写过程**：显示原始查询如何被重写为不同引擎的查询格式
- **Ripgrep模式生成**：显示生成的正则表达式模式和匹配结果
- **Score计算过程**：显示不同引擎结果的权重分配和分数计算
- **结果融合过程**：显示多个引擎结果如何被合并和排序

## 使用方法

### 启用 Debug 功能

通过设置日志级别为 DEBUG 来启用所有调试功能：

```bash
python evaluation/end_to_end_evaluator.py \
  --test-file testset/testset_lite_3.jsonl \
  --log-level DEBUG
```

### 输出格式

#### 1. Search Process Debug 输出示例

```
🔍 检索参数: top_k=10, use_llm_filter=True, enable_symbol_search=True
🎯 实体检测结果: 发现 2 个符号实体: ['AudioPlayer', 'init']
📋 符号检索结果详情: ['src/audio/player.c:15', 'src/audio/manager.c:8']
📊 符号检索质量评分: 0.750
🤔 Ripgrep决策分析: has_entities=True, symbol_count=3, quality=0.750, threshold=5, need_ripgrep=False
✅ 符号检索结果充足，跳过Ripgrep
🔄 阶段4结果合并: symbol(3) + ripgrep(0) = 3 (去重后), 质量评分: 0.750
🤔 Embedding决策分析: current_count=3, quality=0.750, has_entities=True, threshold=5, need_embedding=True
🔄 Query重写(Embedding): '初始化音频播放器' -> '初始化 音频 播放器 配置 设置'
📊 Embedding检索详情: 查询='初始化 音频 播放器 配置 设置', 结果数=7
📋 Embedding检索结果详情: ['src/audio/codec.c:25', 'src/media/player.c:10']
📊 结果融合权重配置: {'symbol': 1.0, 'ripgrep': 0.8, 'embedding': 0.6}
🔝 符号结果匹配加分: src/audio/player.c:15 score=1.200
📈 分数统计: Symbol平均=1.067, Ripgrep平均=0.000, Embedding平均=0.600
🔄 去重处理: 10 -> 8 个结果
🏆 Top5结果分数: ['1.200', '1.000', '1.000', '0.600', '0.600']
🔀 阶段6结果融合: 融合后得到 8 个结果
📊 融合结果score分布: ['1.200', '1.000', '1.000', '0.600', '0.600']
⏭️  跳过LLM筛选: 结果数量 8 <= 15.0
🎁 开始最终结果处理: 8 -> top 10
🎯 最终结果详情: ['src/audio/player.c:15', 'src/audio/manager.c:8', ...]
📊 检索流程统计: 符号(3) + Ripgrep(0) + Embedding(7) -> 融合(8) -> 筛选(8) -> 最终(8)
```

#### 2. Ripgrep模式生成调试示例

```
🔄 Query重写(Ripgrep): '计算向量余弦相似度' -> 3 个模式: ['cosine.*similarity', 'vector.*cosine', '余弦.*相似度']
📝 Ripgrep搜索模式: ['cosine.*similarity', 'vector.*cosine', '余弦.*相似度']
🔍 执行第1个ripgrep搜索，模式: 'cosine.*similarity'
📊 第1个模式搜索结果: 12 个匹配
✅ 第1个模式成功转换: 8/12 个结果
🔍 执行第2个ripgrep搜索，模式: 'vector.*cosine'
📊 第2个模式搜索结果: 5 个匹配
✅ 第2个模式成功转换: 3/5 个结果
🎯 Ripgrep检索汇总: 总共得到 11 个chunks，返回前 10 个
```

#### 3. BadCase Debug 输出示例

##### 3.1 未找到目标文件的情况

```
🔍 BadCase #15 - 未找到目标文件:
  Query: 初始化音频播放器并设置默认参数
  Ground Truth File: src/audio/player.c
  Ground Truth Content Preview: void init_audio_player() { 
    // 初始化音频播放器
    player.volume = 50;
    player.state = STOPPED;
    ...
  }
  找到的文件数量: 5
    #1 (score: 0.8234): src/audio/manager.c
        Content Preview: void manage_audio() { // 管理音频资源 audio_manager_init(); set_default_config(); }
    #2 (score: 0.7891): src/video/player.c  
        Content Preview: void init_video_player() { // 初始化视频播放器 player.fps = 30; player.resolution = HD; }
    #3 (score: 0.7456): src/audio/codec.c
        Content Preview: void audio_codec_init() { // 初始化音频编解码器 codec.format = MP3; codec.bitrate = 128; }
    ... 还有 2 个结果
  ────────────────────────────────────────────────────────────────────────────────
```

##### 3.2 排名不佳的情况

```
📍 BadCase #23 - 目标文件排名较低:
  Query: 计算两个向量的余弦相似度
  Ground Truth File: src/math/vector.c
  实际排名: 第 3 位 (score: 0.7123)
  排在前面的结果:
    #1 (score: 0.8456): src/math/similarity.c
        Content Preview: double cosine_similarity_advanced() { // 高级余弦相似度计算 return dot_product / (norm_a * norm_b); }
    #2 (score: 0.7834): src/math/matrix.c
        Content Preview: void matrix_cosine() { // 矩阵余弦运算 for(int i=0; i<rows; i++) { calc_cosine(matrix[i]); } }
  ────────────────────────────────────────────────────────────────────────────────
```

## 调试提示

### 分析未找到目标文件的原因

1. **语义差异**：Query 的语义与 ground truth 内容不匹配
2. **关键词缺失**：Query 中缺少 ground truth 中的关键技术词汇
3. **索引问题**：目标文件可能没有被正确索引
4. **相似文件干扰**：存在语义相似但不是目标的文件

### 分析排名不佳的原因

1. **相似文件竞争**：存在与 query 更匹配的相似文件
2. **内容质量**：ground truth 的内容质量可能不如排在前面的结果
3. **评分权重**：当前的评分算法可能更偏向某些特征

## 最佳实践

1. **合理设置日志级别**：使用 `--log-level DEBUG` 以查看详细的 debug 信息
2. **结合评估指标**：将 BadCase 分析与整体评估指标结合，找出系统性问题
3. **样本分析**：重点分析高频出现的 BadCase 模式
4. **迭代优化**：基于 BadCase 分析结果调整查询重写、索引策略等组件

## 注意事项

- BadCase Debug 会显著增加日志输出量，建议只在调试时启用
- 内容预览限制在 150-200 字符以避免日志过长
- 只显示前3个排名结果以保持可读性
