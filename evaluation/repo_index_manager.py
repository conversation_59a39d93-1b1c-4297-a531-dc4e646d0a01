"""
仓库索引管理器
负责动态切换和管理多个仓库的索引
"""

import logging
import os
import shutil

# 导入父级模块
import sys
import time
from pathlib import Path
from typing import Optional

sys.path.append(str(Path(__file__).parent.parent))

from model_factory import initialize_model_factory
from pipeline_pro import Pipeline

from config import AppConfig, create_config
from index_manager import IndexManager

logger = logging.getLogger(__name__)


class RepoIndexManager:
    """仓库索引管理器"""

    def __init__(self, base_persist_path: str = "./temp/evaluation"):
        """
        初始化仓库索引管理器

        Args:
            base_persist_path: 索引持久化基础路径
        """
        self.base_persist_path = Path(base_persist_path)
        self.base_persist_path.mkdir(parents=True, exist_ok=True)

        self.current_repo: Optional[str] = None
        self.current_config: Optional[AppConfig] = None
        self.current_index_manager: Optional[IndexManager] = None
        self.current_pipeline: Optional[Pipeline] = None

        # 缓存已构建的索引配置
        self.repo_configs: dict[str, AppConfig] = {}

        logger.info(f"初始化仓库索引管理器，基础路径: {self.base_persist_path}")

    def switch_to_repo(
        self, repo_name: str, repo_path: str, force_rebuild: bool = False
    ) -> bool:
        """
        切换到指定仓库

        Args:
            repo_name: 仓库名称
            repo_path: 仓库路径
            force_rebuild: 是否强制重建索引

        Returns:
            是否切换成功
        """
        logger.info(f"切换到仓库: {repo_name} ({repo_path})")

        # 检查仓库路径是否存在
        if not Path(repo_path).exists():
            logger.error(f"仓库路径不存在: {repo_path}")
            return False

        # 如果已经是当前仓库，直接返回
        if self.current_repo == repo_name and not force_rebuild:
            logger.info(f"已经是当前仓库: {repo_name}")
            return True

        try:
            # 创建或获取仓库配置
            config = self._get_or_create_repo_config(repo_name, repo_path)

            # 初始化模型工厂（如果还没有初始化）
            if repo_name not in self.repo_configs:
                initialize_model_factory(config)

            # 创建索引管理器
            index_manager = IndexManager(config)

            # 加载或构建索引
            logger.info(f"加载仓库 {repo_name} 的索引...")
            start_time = time.time()

            if force_rebuild:
                logger.info("强制重建索引...")
                index_manager.build_all_indexes(force=True)
            else:
                index_manager.load_indexes()

            build_time = time.time() - start_time
            logger.info(f"索引加载/构建完成，耗时: {build_time:.2f}秒")

            # 创建Pipeline
            pipeline_paths = index_manager.get_pipeline_paths()
            pipeline = Pipeline(
                bm25_persist_path=pipeline_paths["bm25_persist_path"],
                emb_persist_path=pipeline_paths["emb_persist_path"],
                # graph_persist_path=pipeline_paths.get("graph_persist_path"),
            )

            # 更新当前状态
            self.current_repo = repo_name
            self.current_config = config
            self.current_index_manager = index_manager
            self.current_pipeline = pipeline
            self.repo_configs[repo_name] = config

            logger.info(f"成功切换到仓库: {repo_name}")
            return True

        except Exception as e:
            logger.error(f"切换仓库失败: {e}")
            return False

    def _get_or_create_repo_config(self, repo_name: str, repo_path: str) -> AppConfig:
        """
        获取或创建仓库配置

        Args:
            repo_name: 仓库名称
            repo_path: 仓库路径

        Returns:
            仓库配置
        """
        if repo_name in self.repo_configs:
            return self.repo_configs[repo_name]

        # 创建仓库专用的持久化路径
        repo_persist_path = self.base_persist_path / repo_name
        repo_persist_path.mkdir(parents=True, exist_ok=True)

        # 创建基础配置
        base_config = create_config()

        # 创建仓库特定配置
        config = AppConfig(
            repo_path=repo_path,
            persist_path=str(repo_persist_path),
            host=base_config.host,
            port=base_config.port,
            log_level=base_config.log_level,
            # 索引配置
            index_config=base_config.index_config,
            embedding_config=base_config.embedding_config,
            llm_config=base_config.llm_config,
            rerank_config=base_config.rerank_config,
            ignore_config=base_config.ignore_config,
            # 文件扩展名和语言支持
            file_extensions=base_config.file_extensions,
            supported_languages=base_config.supported_languages,
        )

        logger.info(f"创建仓库配置: {repo_name}")
        logger.info(f"  仓库路径: {repo_path}")
        logger.info(f"  持久化路径: {repo_persist_path}")

        return config

    def get_current_pipeline(self) -> Optional[Pipeline]:
        """获取当前Pipeline"""
        return self.current_pipeline

    def get_current_repo(self) -> Optional[str]:
        """获取当前仓库名称"""
        return self.current_repo

    def is_repo_indexed(self, repo_name: str) -> bool:
        """检查仓库是否已建立索引"""
        repo_persist_path = self.base_persist_path / repo_name

        # 检查BM25索引
        bm25_path = repo_persist_path / "bm25_index_pro"
        bm25_exists = (bm25_path / "retriever.json").exists()

        # 检查Embedding索引
        embedding_path = repo_persist_path / "embedding_index_pro"
        embedding_exists = (embedding_path / "index.json").exists()

        return bm25_exists and embedding_exists

    def clean_repo_index(self, repo_name: str) -> bool:
        """清理指定仓库的索引"""
        try:
            repo_persist_path = self.base_persist_path / repo_name
            if repo_persist_path.exists():
                shutil.rmtree(repo_persist_path)
                logger.info(f"已清理仓库索引: {repo_name}")
            return True
        except Exception as e:
            logger.error(f"清理仓库索引失败: {e}")
            return False

    def get_repo_index_info(self, repo_name: str) -> dict:
        """获取仓库索引信息"""
        repo_persist_path = self.base_persist_path / repo_name

        info = {
            "repo_name": repo_name,
            "persist_path": str(repo_persist_path),
            "indexed": self.is_repo_indexed(repo_name),
            "bm25_index_exists": False,
            "embedding_index_exists": False,
            "index_size_mb": 0,
        }

        if repo_persist_path.exists():
            # 检查各个索引
            bm25_path = repo_persist_path / "bm25_index_pro"
            info["bm25_index_exists"] = (bm25_path / "retriever.json").exists()

            embedding_path = repo_persist_path / "embedding_index_pro"
            info["embedding_index_exists"] = (embedding_path / "index.json").exists()

            # 计算索引大小
            total_size = 0
            for root, _, files in os.walk(repo_persist_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    try:
                        total_size += os.path.getsize(file_path)
                    except OSError:
                        pass
            info["index_size_mb"] = round(total_size / (1024 * 1024), 2)

        return info

    def list_all_repos(self) -> dict[str, dict]:
        """列出所有仓库的索引信息"""
        repos_info = {}

        if self.base_persist_path.exists():
            for repo_dir in self.base_persist_path.iterdir():
                if repo_dir.is_dir():
                    repo_name = repo_dir.name
                    repos_info[repo_name] = self.get_repo_index_info(repo_name)

        return repos_info

    def cleanup(self):
        """清理资源"""
        if self.current_index_manager:
            try:
                self.current_index_manager.stop_auto_refresh()
            except Exception:
                pass

        self.current_repo = None
        self.current_config = None
        self.current_index_manager = None
        self.current_pipeline = None

        logger.info("仓库索引管理器已清理")


if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)

    # 屏蔽httpx的日志输出
    logging.getLogger("httpx").setLevel(logging.WARNING)

    manager = RepoIndexManager()

    # 测试切换仓库
    test_repo_name = "XmlGraphicsBatik"
    test_repo_path = "/Users/<USER>/ide_dev/data/arkts_from_yyq/active_repos_gitee_exclude_HarmonyOS_Codelabs/XmlGraphicsBatik"

    if manager.switch_to_repo(test_repo_name, test_repo_path):
        logger.info(f"成功切换到仓库: {test_repo_name}")

        # 测试查询
        pipeline = manager.get_current_pipeline()
        if pipeline:
            results = pipeline.run("获取路径长度", top_k=5)
            logger.info(f"查询结果数量: {len(results)}")
            for i, result in enumerate(results):
                logger.info(
                    f"  {i + 1}. {result['file_path']} (score: {result['score']:.3f})"
                )

    manager.cleanup()
