<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JSONL文件查看器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: #f5f5f5;
            color: #333;
        }
        
        .header {
            background: #fff;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-bottom: 1px solid #e0e0e0;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .upload-section {
            background: #fff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            text-align: center;
        }
        
        .file-input {
            display: none;
        }
        
        .upload-btn {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s;
        }
        
        .upload-btn:hover {
            background: #0056b3;
        }
        
        .file-info {
            margin-top: 15px;
            font-size: 14px;
            color: #666;
        }
        
        .controls {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .search-box {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            width: 250px;
        }
        
        .filter-select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            background: white;
        }
        
        .stats {
            background: #f8f9fa;
            padding: 10px 15px;
            border-radius: 4px;
            font-size: 14px;
            color: #666;
        }
        
        .content {
            display: grid;
            grid-template-columns: 250px 1fr;
            gap: 20px;
            min-height: 600px;
        }
        
        .sidebar {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            padding: 20px;
            height: fit-content;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .sidebar h3 {
            margin-bottom: 15px;
            font-size: 16px;
            color: #333;
        }
        
        .repo-list {
            list-style: none;
        }
        
        .repo-item {
            padding: 8px 12px;
            cursor: pointer;
            border-radius: 4px;
            margin-bottom: 2px;
            transition: background 0.2s;
            font-size: 14px;
        }
        
        .repo-item:hover {
            background: #f0f0f0;
        }
        
        .repo-item.active {
            background: #007bff;
            color: white;
        }
        
        .main-content {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            padding: 20px;
            overflow: auto;
            max-height: 80vh;
        }
        
        .json-viewer {
            font-family: 'Consolas', 'Monaco', 'Lucida Console', monospace;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .json-object {
            margin-left: 20px;
        }
        
        .json-key {
            color: #881391;
            font-weight: bold;
        }
        
        .json-string {
            color: #032f62;
        }
        
        .json-number {
            color: #005cc5;
        }
        
        .json-boolean {
            color: #e36209;
        }
        
        .json-null {
            color: #6a737d;
        }
        
        .item-header {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
            cursor: pointer;
            border-left: 4px solid #007bff;
        }
        
        .item-content {
            padding: 15px;
            background: #fafafa;
            border-radius: 4px;
            margin-bottom: 15px;
            display: none;
        }
        
        .item-content.expanded {
            display: block;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 20px;
        }
        
        .pagination button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .pagination button.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }
        
        .pagination button:hover:not(.active) {
            background: #f0f0f0;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
        
        .highlight {
            background: yellow;
            padding: 2px 4px;
            border-radius: 2px;
        }
        
        .function-list {
            margin-top: 20px;
        }
        
        .function-item {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            margin-bottom: 15px;
            overflow: hidden;
        }
        
        .function-header {
            background: #f8f9fa;
            padding: 15px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .function-body {
            padding: 20px;
            background: white;
        }
        
        .score-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            margin: 2px;
        }
        
        .score-high { background: #d4edda; color: #155724; }
        .score-medium { background: #fff3cd; color: #856404; }
        .score-low { background: #f8d7da; color: #721c24; }
        
        .query-item {
            margin: 8px 0;
            padding: 8px;
            border-radius: 4px;
            background: #f9f9f9;
            border-left: 3px solid #607D8B;
        }
        
        .intent-badge {
            background: #607D8B;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
            margin-right: 8px;
            font-weight: bold;
        }
        
        .intent-understanding { background: #2196F3; }
        .intent-implementation { background: #4CAF50; }
        .intent-debugging { background: #FF9800; }
        .intent-optimization { background: #9C27B0; }
        .intent-testing { background: #F44336; }
        .intent-integration { background: #795548; }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1>JSONL文件查看器 - ArkTS查询数据集</h1>
            <p>支持交互式浏览和分析JSONL格式的ArkTS代码查询生成数据，包含函数评分和LLM生成的查询</p>
        </div>
    </div>

    <div class="container">
        <div class="upload-section">
            <input type="file" id="fileInput" class="file-input" accept=".jsonl,.json">
            <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                📁 选择JSONL文件
            </button>
            <div class="file-info" id="fileInfo">请选择ArkTS查询数据集JSONL文件进行查看</div>
        </div>

        <div class="controls" id="controls" style="display: none;">
            <input type="text" class="search-box" id="searchBox" placeholder="搜索函数名、文件路径、查询内容...">
            <select class="filter-select" id="repoFilter">
                <option value="">所有仓库</option>
            </select>
            <select class="filter-select" id="scoreFilter">
                <option value="">所有评分</option>
                <option value="high">高分 (≥80)</option>
                <option value="medium">中等 (60-79)</option>
                <option value="low">低分 (<60)</option>
            </select>
            <select class="filter-select" id="intentFilter">
                <option value="">所有意图</option>
                <option value="understanding">理解类</option>
                <option value="implementation">实现类</option>
                <option value="debugging">调试类</option>
                <option value="optimization">优化类</option>
                <option value="testing">测试类</option>
                <option value="integration">集成类</option>
            </select>
            <div class="stats" id="stats">加载中...</div>
        </div>

        <div class="content" id="content" style="display: none;">
            <div class="sidebar">
                <h3>仓库列表</h3>
                <ul class="repo-list" id="repoList"></ul>
            </div>
            <div class="main-content">
                <div id="loading" class="loading">加载数据中...</div>
                <div id="jsonViewer" class="json-viewer"></div>
                <div class="pagination" id="pagination"></div>
            </div>
        </div>
    </div>

    <script>
        let data = [];
        let filteredData = [];
        let currentPage = 1;
        const itemsPerPage = 10;

        // 文件上传处理
        document.getElementById('fileInput').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (!file) return;

            document.getElementById('fileInfo').textContent = `正在加载: ${file.name}...`;
            
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    // 解析JSONL文件
                    const lines = e.target.result.split('\n').filter(line => line.trim());
                    data = lines.map((line, index) => {
                        try {
                            const item = JSON.parse(line);
                            item._lineNumber = index + 1;
                            return item;
                        } catch (err) {
                            console.error(`第${index + 1}行解析错误:`, err);
                            return null;
                        }
                    }).filter(item => item);
                    
                    filteredData = [...data];
                    initializeViewer();
                    document.getElementById('fileInfo').textContent = 
                        `已加载: ${file.name} (${data.length} 条记录)`;
                } catch (error) {
                    showError('文件解析错误: ' + error.message);
                }
            };
            reader.readAsText(file);
        });

        function initializeViewer() {
            document.getElementById('controls').style.display = 'flex';
            document.getElementById('content').style.display = 'grid';
            
            populateRepoFilter();
            renderData();
            setupEventListeners();
        }

        function populateRepoFilter() {
            const repos = [...new Set(data.map(item => item.repo || 'unknown'))].sort();
            const repoFilter = document.getElementById('repoFilter');
            const repoList = document.getElementById('repoList');
            
            repoFilter.innerHTML = '<option value="">所有仓库</option>';
            repoList.innerHTML = '';
            
            repos.forEach(repo => {
                const count = data.filter(item => item.repo === repo).length;
                
                // 添加到过滤器
                const option = document.createElement('option');
                option.value = repo;
                option.textContent = `${repo} (${count})`;
                repoFilter.appendChild(option);
                
                // 添加到侧边栏
                const li = document.createElement('li');
                li.className = 'repo-item';
                li.textContent = `${repo} (${count})`;
                li.onclick = () => {
                    document.querySelectorAll('.repo-item').forEach(item => 
                        item.classList.remove('active'));
                    li.classList.add('active');
                    filterByRepo(repo);
                };
                repoList.appendChild(li);
            });
        }

        function setupEventListeners() {
            document.getElementById('searchBox').addEventListener('input', filterData);
            document.getElementById('repoFilter').addEventListener('change', filterData);
            document.getElementById('scoreFilter').addEventListener('change', filterData);
            document.getElementById('intentFilter').addEventListener('change', filterData);
        }

        function filterByRepo(repo) {
            document.getElementById('repoFilter').value = repo;
            filterData();
        }

        function filterData() {
            const searchTerm = document.getElementById('searchBox').value.toLowerCase();
            const repoFilter = document.getElementById('repoFilter').value;
            const scoreFilter = document.getElementById('scoreFilter').value;
            const intentFilter = document.getElementById('intentFilter').value;

            filteredData = data.filter(item => {
                // 仓库过滤
                if (repoFilter && item.repo !== repoFilter) return false;
                
                // 搜索过滤
                if (searchTerm) {
                    const searchable = [
                        item.repo || '',
                        item.filepath || '',
                        JSON.stringify(item.functions || []).toLowerCase()
                    ].join(' ');
                    
                    if (!searchable.includes(searchTerm)) return false;
                }
                
                // 评分过滤
                if (scoreFilter) {
                    const fileScore = item.final_score || 0;
                    switch (scoreFilter) {
                        case 'high': if (fileScore < 80) return false; break;
                        case 'medium': if (fileScore < 60 || fileScore >= 80) return false; break;
                        case 'low': if (fileScore >= 60) return false; break;
                    }
                }
                
                // 意图过滤
                if (intentFilter && item.functions) {
                    const hasMatchingIntent = item.functions.some(func => {
                        const queries = func.generated_queries || [];
                        return queries.some(query => query.intent === intentFilter);
                    });
                    if (!hasMatchingIntent) return false;
                }
                
                return true;
            });

            currentPage = 1;
            renderData();
        }

        function renderData() {
            const container = document.getElementById('jsonViewer');
            const stats = document.getElementById('stats');
            const start = (currentPage - 1) * itemsPerPage;
            const end = start + itemsPerPage;
            const pageData = filteredData.slice(start, end);

            stats.textContent = `显示 ${start + 1}-${Math.min(end, filteredData.length)} / ${filteredData.length} 条记录`;

            let html = '';
            pageData.forEach((item, index) => {
                const functionCount = (item.functions || []).length;
                const isArkUI = item.isArkUI ? '📱 ArkUI' : '📄 普通';
                const fileScore = item.final_score || 0;
                const totalQueries = (item.functions || []).reduce((total, func) => {
                    return total + (func.generated_queries || []).length;
                }, 0);
                
                html += `
                    <div class="function-item">
                        <div class="function-header" onclick="toggleItem(${start + index})">
                            <div>
                                <strong>${item.repo || 'unknown'}</strong>
                                <span style="margin: 0 10px;">|</span>
                                <span>${item.filepath || '未命名文件'}</span>
                                <span style="margin: 0 10px;">|</span>
                                <span>${isArkUI}</span>
                                <span style="margin: 0 10px;">|</span>
                                <span>函数数: ${functionCount}</span>
                                <span style="margin: 0 10px;">|</span>
                                <span>查询数: ${totalQueries}</span>
                            </div>
                            <div>
                                <span class="score-badge ${getScoreClass(fileScore/20)}" style="margin-right: 10px;">评分: ${fileScore.toFixed(1)}</span>
                                <span style="margin-right: 10px;">令牌: ${item.token_count || 0}</span>
                                <span>▼</span>
                            </div>
                        </div>
                        <div class="function-body" id="item-${start + index}" style="display: none;">
                            ${renderItemDetails(item)}
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;
            renderPagination();
        }

        function renderItemDetails(item) {
            let html = `
                <div style="margin-bottom: 15px;">
                    <strong>文件路径:</strong> ${item.filepath || 'N/A'}<br>
                    <strong>仓库:</strong> ${item.repo || 'N/A'}<br>
                    <strong>是否为ArkUI:</strong> ${item.isArkUI ? '是' : '否'}<br>
                    <strong>Token数:</strong> ${item.token_count || 0}<br>
                    <strong>文件评分:</strong> ${item.final_score ? item.final_score.toFixed(2) : 'N/A'}
                </div>
            `;

            // Show quality scores if available
            if (item.quality_scores) {
                html += '<h4>文件质量评分:</h4>';
                html += '<div style="margin-bottom: 15px;">';
                Object.entries(item.quality_scores).forEach(([key, value]) => {
                    if (typeof value === 'number') {
                        html += `<span class="score-badge ${getScoreClass(value/20)}">${key}: ${value.toFixed(2)}</span>`;
                    }
                });
                html += '</div>';
            }

            if (item.functions && item.functions.length > 0) {
                html += '<h4>函数详情:</h4>';
                item.functions.forEach((func, funcIndex) => {
                    const scores = func.quality_scores || {};
                    const finalScore = func.final_score || 0;
                    
                    html += `
                        <div style="border: 1px solid #eee; border-radius: 4px; padding: 15px; margin-bottom: 10px; background: #fafafa;">
                            <div style="margin-bottom: 10px;">
                                <strong>函数名:</strong> ${func.name || '未命名'}<br>
                                <strong>类型:</strong> ${func.type || 'N/A'}<br>
                                <strong>唯一ID:</strong> ${func.unique_id || 'N/A'}<br>
                                <strong>最终评分:</strong> <span class="score-badge ${getScoreClass(finalScore/20)}">${finalScore.toFixed(2)}</span>
                                <div style="margin-top: 5px;">
                                    ${Object.entries(scores).map(([key, value]) => {
                                        if (typeof value === 'number' && key !== 'final') {
                                            return `<span class="score-badge ${getScoreClass(value/20)}">${key}: ${value.toFixed(2)}</span>`;
                                        }
                                        return '';
                                    }).join('')}
                                </div>
                            </div>
                            ${func.body ? `<div><strong>函数体:</strong><br><pre style="background: #f0f0f0; padding: 10px; border-radius: 4px; margin: 5px 0; white-space: pre-wrap; font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto;">${escapeHtml(func.body)}</pre></div>` : ''}
                            ${renderQueries(func.generated_queries)}
                        </div>
                    `;
                });
            }

            return html;
        }

        function renderQueries(queries) {
            if (!queries || queries.length === 0) return '';
            
            let html = '<div><strong>生成的查询:</strong><br>';
            queries.forEach((query, index) => {
                const intentColor = {
                    'understanding': '#2196F3',
                    'implementation': '#4CAF50', 
                    'debugging': '#FF9800',
                    'optimization': '#9C27B0',
                    'testing': '#F44336',
                    'integration': '#795548'
                }[query.intent] || '#607D8B';
                
                html += `
                    <div style="margin: 8px 0; padding: 8px; border-left: 3px solid ${intentColor}; background: #f9f9f9;">
                        <span style="background: ${intentColor}; color: white; padding: 2px 6px; border-radius: 3px; font-size: 12px; margin-right: 8px;">${query.intent}</span>
                        <span style="font-size: 14px;">${escapeHtml(query.query)}</span>
                    </div>
                `;
            });
            html += '</div>';
            return html;
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function getScoreClass(score) {
            if (score >= 4.0) return 'score-high';
            if (score >= 3.0) return 'score-medium';
            return 'score-low';
        }

        function toggleItem(index) {
            const element = document.getElementById(`item-${index}`);
            if (element.style.display === 'none') {
                element.style.display = 'block';
            } else {
                element.style.display = 'none';
            }
        }

        function renderPagination() {
            const totalPages = Math.ceil(filteredData.length / itemsPerPage);
            const pagination = document.getElementById('pagination');
            
            if (totalPages <= 1) {
                pagination.innerHTML = '';
                return;
            }

            let html = '';
            
            // 上一页
            html += `<button ${currentPage === 1 ? 'disabled' : ''} onclick="changePage(${currentPage - 1})">上一页</button>`;
            
            // 页码
            for (let i = 1; i <= totalPages; i++) {
                if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
                    html += `<button class="${i === currentPage ? 'active' : ''}" onclick="changePage(${i})">${i}</button>`;
                } else if (i === currentPage - 3 || i === currentPage + 3) {
                    html += '<span>...</span>';
                }
            }
            
            // 下一页
            html += `<button ${currentPage === totalPages ? 'disabled' : ''} onclick="changePage(${currentPage + 1})">下一页</button>`;
            
            pagination.innerHTML = html;
        }

        function changePage(page) {
            const totalPages = Math.ceil(filteredData.length / itemsPerPage);
            if (page >= 1 && page <= totalPages) {
                currentPage = page;
                renderData();
                window.scrollTo({ top: 0, behavior: 'smooth' });
            }
        }

        function showError(message) {
            document.getElementById('jsonViewer').innerHTML = 
                `<div class="error">${message}</div>`;
        }

        // 支持拖拽上传
        document.addEventListener('dragover', (e) => {
            e.preventDefault();
            e.stopPropagation();
        });

        document.addEventListener('drop', (e) => {
            e.preventDefault();
            e.stopPropagation();
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                document.getElementById('fileInput').files = files;
                document.getElementById('fileInput').dispatchEvent(new Event('change'));
            }
        });

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 自动加载示例数据（如果有）
            const urlParams = new URLSearchParams(window.location.search);
            const fileUrl = urlParams.get('file');
            if (fileUrl) {
                fetch(fileUrl)
                    .then(response => response.text())
                    .then(text => {
                        const lines = text.split('\n').filter(line => line.trim());
                        data = lines.map((line, index) => {
                            try {
                                const item = JSON.parse(line);
                                item._lineNumber = index + 1;
                                return item;
                            } catch (err) {
                                return null;
                            }
                        }).filter(item => item);
                        
                        filteredData = [...data];
                        initializeViewer();
                        document.getElementById('fileInfo').textContent = 
                            `已加载URL文件 (${data.length} 条记录)`;
                    })
                    .catch(error => {
                        showError('URL加载失败: ' + error.message);
                    });
            }
        });
    </script>
</body>
</html>