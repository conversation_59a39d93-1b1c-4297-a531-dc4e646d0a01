#!/usr/bin/env python3
"""
ArkTS集成数据收集器
在数据收集阶段就进行质量评分的集成工具
"""

import json
import logging
import os
import re
import sys
from collections import defaultdict
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path
from typing import Any, Optional

import numpy as np
import pandas as pd

try:
    from tqdm import tqdm
except ImportError:
    tqdm = lambda x, **kwargs: x

try:
    from transformers import AutoTokenizer

    tokenizer = AutoTokenizer.from_pretrained(
        "Qwen/Qwen2.5-7B-Instruct", trust_remote_code=True
    )
except ImportError:
    tokenizer = None
    logging.warning("transformers未安装，token计算将不可用")

import random

# 添加路径
sys.path.append(str(Path(__file__).parent.parent.parent))
from codebase_processor.arkts_parser import arkts_parser

sys.path.append(str(Path(__file__).parent))
from pipeline_config import DATA_COLLECTION_CONFIG, FILTER_CONFIG, SAMPLING_CONFIG

# 配置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


@dataclass
class CollectorConfig:
    """数据收集配置"""

    repos_dir: str = DATA_COLLECTION_CONFIG["repos_dir"]
    output_dir: str = "pipeline_output"
    allowed_extensions: Optional[set] = None
    exclude_dirs: Optional[set] = None
    max_token_num: int = DATA_COLLECTION_CONFIG["max_token_num"]
    min_file_lines: int = DATA_COLLECTION_CONFIG["min_file_lines"]
    max_file_lines: int = DATA_COLLECTION_CONFIG["max_file_lines"]
    max_line_length: int = DATA_COLLECTION_CONFIG["max_line_length"]
    max_empty_line_ratio: float = DATA_COLLECTION_CONFIG["empty_line_ratio"]
    target_size: int = FILTER_CONFIG["target_size"]
    top_percent: float = FILTER_CONFIG["top_percent"]
    random_seed: int = FILTER_CONFIG["random_seed"]
    quality_weights: Optional[dict] = None

    # Function sampling configuration
    function_target_size: int = SAMPLING_CONFIG["target_size"]
    function_min_per_file: int = SAMPLING_CONFIG["min_per_file"]
    function_max_per_file: int = SAMPLING_CONFIG["max_per_file"]
    function_quality_weights: Optional[dict] = None

    def __post_init__(self):
        if self.allowed_extensions is None:
            self.allowed_extensions = {".ets"}
        if self.exclude_dirs is None:
            self.exclude_dirs = set(DATA_COLLECTION_CONFIG["exclude_dirs"])
        if self.quality_weights is None:
            self.quality_weights = FILTER_CONFIG["quality_weights"]
        if self.function_quality_weights is None:
            self.function_quality_weights = SAMPLING_CONFIG["quality_weights"]


@dataclass
class FileInfo:
    """文件信息数据类"""

    path: Path
    repo_name: str
    content: str = ""
    lines: Optional[list[str]] = None
    token_count: int = 0
    is_arkui: bool = False
    functions: Optional[list[dict]] = None
    quality_scores: Optional[dict] = None
    final_score: float = 0.0

    def __post_init__(self):
        if self.lines is None:
            self.lines = []
        if self.functions is None:
            self.functions = []
        if self.quality_scores is None:
            self.quality_scores = {}


class TokenCounter:
    """Token计数器"""

    @staticmethod
    def count_tokens(content: str) -> int:
        """计算内容的token数量"""
        if tokenizer is None:
            return max(1, len(content) // 4)
        try:
            return len(tokenizer.tokenize(content))
        except Exception as e:
            logger.warning(f"Token计算失败: {e}")
            return max(1, len(content) // 4)


class FileValidator:
    """文件验证器"""

    def __init__(self, config: CollectorConfig):
        self.config = config

    def is_valid_file(self, file_path: Path) -> bool:
        """检查文件是否有效"""
        try:
            allowed_ext = self.config.allowed_extensions or set()
            if file_path.suffix.lower() not in allowed_ext:
                return False
            if not file_path.exists() or file_path.stat().st_size == 0:
                return False
            return True
        except Exception as e:
            logger.debug(f"文件验证失败 {file_path}: {e}")
            return False

    def validate_content(self, content: str) -> bool:
        """验证文件内容"""
        if not content or len(content) < 36:
            return False

        lines = content.splitlines()
        line_count = len(lines)

        if (
            line_count < self.config.min_file_lines
            or line_count > self.config.max_file_lines
        ):
            return False

        if any(len(line.strip()) > self.config.max_line_length for line in lines):
            return False

        empty_lines = sum(1 for line in lines if not line.strip())
        if (
            line_count > 0
            and empty_lines / line_count > self.config.max_empty_line_ratio
        ):
            return False

        return True


class ArkTSAnalyzer:
    """ArkTS代码分析器"""

    ARKUI_PATTERNS = [
        r"@Entry",
        r"@Component",
        r"@State",
        r"@Prop",
        r"@Link",
        r"struct\s+\w+\s*{",
        r"build\s*\(\s*\)\s*{",
        r"\b(Column|Row|Stack|Button|Text|Image|list|Tabs|Navigation|Grid)\s*[({]",
    ]

    CONTROL_KEYWORDS = {
        "if",
        "else",
        "for",
        "while",
        "do",
        "switch",
        "case",
        "try",
        "catch",
        "finally",
        "with",
        "return",
        "break",
        "continue",
        "throw",
        "typeof",
        "instanceof",
        "new",
        "delete",
        "void",
        "in",
        "of",
        "var",
        "let",
        "const",
        "function",
        "class",
        "interface",
        "enum",
        "struct",
        "import",
        "export",
        "from",
        "as",
        "default",
    }

    ARKUI_COMPONENTS = {
        "Column",
        "Row",
        "Stack",
        "Button",
        "Text",
        "Image",
        "list",
        "Tabs",
        "Navigation",
        "Grid",
        "Scroll",
        "Flex",
        "RelativeContainer",
        "GridRow",
        "GridCol",
        "Swiper",
    }

    def __init__(self):
        self.parser = arkts_parser

    def is_arkui_file(self, content: str) -> bool:
        """判断是否为ArkUI文件"""
        matches = sum(
            1 for pattern in self.ARKUI_PATTERNS if re.search(pattern, content)
        )
        return matches >= 2

    def extract_functions(self, content: str) -> list[dict]:
        """提取函数信息"""
        if not content:
            return []

        seen_funcs = set()

        if self.parser.is_available():
            tree = self.parser.parse(content)
            if tree:
                return self._extract_with_tree_sitter(content, tree, seen_funcs)

        return self._extract_with_simple_parser(content, seen_funcs)

    def _extract_with_tree_sitter(
        self, content: str, tree, seen_funcs: set
    ) -> list[dict]:
        """使用tree-sitter提取函数"""
        functions = []
        lines = content.splitlines()

        def build_context_path(context_stack):
            return ".".join(
                ctx["name"]
                for ctx in context_stack
                if ctx["type"] in ["class", "struct", "interface"]
            )

        def build_parent_definition(context_stack):
            if not context_stack:
                return None
            parent = context_stack[-1]
            return f"{parent['type']}.{parent['name']}"

        def traverse_node(node, context_stack=None):
            if context_stack is None:
                context_stack = []

            container_types = {
                "struct_declaration": "struct",
                "class_declaration": "class",
                "interface_declaration": "interface",
                "enum_declaration": "enum",
            }

            function_types = {
                "function_declaration": "function",
                "method_definition": "method",
                "constructor_definition": "constructor",
            }

            if node.type in container_types:
                name = self._extract_node_name(node, content)
                if name:
                    context = {"type": container_types[node.type], "name": name}
                    context_stack.append(context)

                    body = self._extract_node_body(node, lines)
                    if body and len(body.strip()) >= 20:
                        unique_id = (
                            f"{build_context_path(context_stack[:-1])}.{name}".lstrip(
                                "."
                            )
                        )
                        func_id = f"{unique_id}:{hash(body)}"

                        if func_id not in seen_funcs:
                            seen_funcs.add(func_id)
                            functions.append(
                                {
                                    "name": name,
                                    "unique_id": unique_id,
                                    "type": container_types[node.type],
                                    "parent_definition": build_parent_definition(
                                        context_stack[:-1]
                                    ),
                                    "full_type": f"{build_parent_definition(context_stack[:-1])}.{container_types[node.type]}"
                                    if context_stack[:-1]
                                    else container_types[node.type],
                                    "body": body.strip(),
                                    "start_line": node.start_point[0] + 1,
                                    "end_line": node.end_point[0] + 1,
                                }
                            )

            elif node.type in function_types:
                name = self._extract_node_name(node, content)
                if name and self._is_valid_function_name(name):
                    body = self._extract_node_body(node, lines)
                    if body and len(body.strip()) >= 20:
                        unique_id = (
                            f"{build_context_path(context_stack)}.{name}".lstrip(".")
                        )
                        func_id = f"{unique_id}:{hash(body)}"

                        if func_id not in seen_funcs:
                            seen_funcs.add(func_id)
                            functions.append(
                                {
                                    "name": name,
                                    "unique_id": unique_id,
                                    "type": function_types[node.type],
                                    "parent_definition": build_parent_definition(
                                        context_stack
                                    ),
                                    "full_type": f"{build_parent_definition(context_stack)}.{function_types[node.type]}"
                                    if context_stack
                                    else function_types[node.type],
                                    "body": body.strip(),
                                    "start_line": node.start_point[0] + 1,
                                    "end_line": node.end_point[0] + 1,
                                }
                            )

            for child in node.children:
                traverse_node(child, context_stack.copy())

            # 清理上下文栈
            if (
                node.type in container_types
                and context_stack
                and len(context_stack) > 0
            ):
                # 获取当前节点名称并与栈顶比较
                current_name = self._extract_node_name(node, content)
                if current_name and context_stack[-1]["name"] == current_name:
                    context_stack.pop()

        traverse_node(tree.root_node)
        return functions

    def _extract_with_simple_parser(self, content: str, seen_funcs: set) -> list[dict]:
        """使用简单解析器提取函数"""
        return []

    def _extract_node_name(self, node, content: str) -> Optional[str]:
        """从AST节点提取名称"""

        def find_identifier(n, depth=0):
            if depth > 3:
                return None
            if n.type in ["identifier", "property_identifier", "type_identifier"]:
                return n.text.decode("utf-8")
            for child in n.children:
                result = find_identifier(child, depth + 1)
                if result:
                    return result
            return None

        return find_identifier(node)

    def _extract_node_body(self, node, lines: list[str]) -> str:
        """提取节点对应的文本"""
        start, end = node.start_point[0], node.end_point[0]
        if 0 <= start < len(lines) and 0 <= end < len(lines):
            return "\n".join(lines[start : end + 1])
        return ""

    def _is_valid_function_name(self, name: str) -> bool:
        """检查是否为有效的函数名"""
        if not name or not name.isidentifier():
            return False
        return (
            name.lower() not in self.CONTROL_KEYWORDS
            and name not in self.ARKUI_COMPONENTS
        )


class QualityScorer:
    """质量评分器 - 将filter_arkts_dataset.py的评分逻辑整合进来"""

    def __init__(self, weights: Optional[dict] = None):
        self.weights = weights or FILTER_CONFIG["quality_weights"]

    def calculate_complexity_score(self, content: str) -> float:
        """计算代码复杂度评分"""
        lines = content.split("\n")

        # 基础指标
        code_lines = len(
            [l for l in lines if l.strip() and not l.strip().startswith("//")]
        )
        comment_lines = len(
            [
                l
                for l in lines
                if l.strip().startswith("//") or l.strip().startswith("/*")
            ]
        )  # noqa: E741

        # 结构复杂度
        control_structures = len(
            re.findall(r"\b(if|for|while|switch|try|catch)\b", content)
        )
        nesting_depth = content.count("{") + content.count("}")

        # 函数复杂度
        function_count = len(
            re.findall(
                r"\bfunction\s+\w+|\b\w+\s*\([^)]*\)\s*{|\b\w+\([^)]*\)\s*=>\s*{",
                content,
            )
        )

        # 计算复杂度评分 (0-100)
        complexity_score = min(
            100,
            (
                min(code_lines / 50 * 20, 20)  # 代码行数权重
                + min(comment_lines / max(code_lines, 1) * 30, 30)  # 注释比例权重
                + min(control_structures / max(code_lines, 1) * 200, 25)  # 控制结构密度
                + min(function_count / max(code_lines, 1) * 100, 15)  # 函数密度
                + min(nesting_depth / max(code_lines, 1) * 50, 10)  # 嵌套深度
            ),
        )

        return complexity_score

    def calculate_diversity_score(self, item: dict) -> float:
        """计算代码多样性评分"""
        functions = item.get("functions", [])
        if not functions:
            return 0

        # 函数类型多样性
        type_counts = defaultdict(int)
        for func in functions:
            type_counts[func.get("type", "unknown")] += 1

        type_diversity = len(type_counts) / max(len(functions), 1) * 50

        # 函数命名多样性
        names = [func.get("name", "") for func in functions]
        unique_names = len(set(names))
        name_diversity = unique_names / max(len(names), 1) * 25

        # 结构多样性
        has_struct = any(func.get("type") == "struct" for func in functions)
        has_class = any(func.get("type") == "class" for func in functions)
        has_interface = any(func.get("type") == "interface" for func in functions)
        structure_diversity = (has_struct + has_class + has_interface) / 3 * 25

        return type_diversity + name_diversity + structure_diversity

    def calculate_quality_score(self, content: str) -> float:
        """计算代码质量评分"""
        lines = content.split("\n")

        # 代码规范检查
        proper_indentation = all(
            line.startswith((" ", "\t")) or not line.strip()
            for line in lines[1:]
            if lines
        )
        consistent_braces = content.count("{") == content.count("}")

        # 命名规范检查
        camel_case_vars = len(re.findall(r"\b[a-z][a-zA-Z0-9]*\b", content))
        pascal_case_types = len(re.findall(r"\b[A-Z][a-zA-Z0-9]*\b", content))

        # 导入语句检查
        has_imports = bool(re.search(r"^import\s+", content, re.MULTILINE))

        # ArkUI特征检查
        has_arkui_features = bool(
            re.search(r"@Component|@Entry|@State|@Builder", content)
        )

        quality_score = min(
            100,
            (
                (proper_indentation * 20)
                + (consistent_braces * 15)
                + (min(camel_case_vars / max(len(lines), 1) * 100, 20))
                + (min(pascal_case_types / max(len(lines), 1) * 100, 15))
                + (has_imports * 15)
                + (has_arkui_features * 15)
            ),
        )

        return quality_score

    def calculate_representativeness_score(self, item: dict) -> float:
        """计算代表性评分"""
        content = item.get("content", "")
        functions = item.get("functions", [])

        # 文件大小适中
        lines = content.split("\n")
        size_score = 0
        if 50 <= len(lines) <= 500:
            size_score = 25
        elif 20 <= len(lines) < 50:
            size_score = 15
        elif 500 < len(lines) <= 800:
            size_score = 15

        # 函数数量适中
        func_count = len(functions)
        func_score = 0
        if 3 <= func_count <= 15:
            func_score = 25
        elif 1 <= func_count < 3:
            func_score = 15
        elif 15 < func_count <= 25:
            func_score = 15

        # 包含典型ArkTS模式
        patterns = [
            r"@Component",
            r"@Entry",
            r"@State",
            r"@Builder",
            r"struct\s+\w+",
            r"build\(\)\s*{",
            r"Column\s*\(",
            r"Row\s*\(",
        ]
        pattern_matches = sum(1 for pattern in patterns if re.search(pattern, content))
        pattern_score = min(pattern_matches / len(patterns) * 50, 50)

        return size_score + func_score + pattern_score

    def calculate_completeness_score(self, item: dict) -> float:
        """计算完整性评分"""
        functions = item.get("functions", [])
        if not functions:
            return 0

        # 检查函数信息完整性
        completeness_score = 0
        for func in functions:
            if all(
                key in func
                for key in ["name", "type", "body", "start_line", "end_line"]
            ):
                completeness_score += 20
            if func.get("unique_id"):
                completeness_score += 10
            if func.get("parent_definition"):
                completeness_score += 10

        return min(completeness_score / max(len(functions), 1), 100)

    def calculate_unique_id_complexity_score(self, item: dict):
        """计算unique_id复杂度评分"""
        functions = item.get("functions", [])
        if not functions:
            return 0

        complexity_scores = []
        for func in functions:
            unique_id = func.get("unique_id", "")
            if not unique_id:
                complexity_scores.append(0)
                continue

            # 基础长度评分
            length_score = min(len(unique_id) / 10 * 20, 20)

            # 层次深度评分（根据点号数量）
            depth_score = unique_id.count(".") * 15

            # 语义丰富度评分
            simple_words = {
                "info",
                "item",
                "data",
                "value",
                "temp",
                "obj",
                "var",
                "test",
                "demo",
            }
            words = unique_id.lower().split(".")
            simple_word_ratio = sum(1 for word in words if word in simple_words) / max(
                len(words), 1
            )
            semantic_score = max(0, 30 - simple_word_ratio * 30)

            # 命名规范评分
            camel_case_score = 15 if re.search(r"[a-z][A-Z]", unique_id) else 0
            pascal_case_score = (
                15 if re.search(r"^[A-Z][a-zA-Z0-9]*", unique_id.split(".")[-1]) else 0
            )

            # 综合评分
            total_score = min(
                100,
                length_score
                + depth_score
                + semantic_score
                + camel_case_score
                + pascal_case_score,
            )
            complexity_scores.append(total_score)

        return np.mean(complexity_scores) if complexity_scores else 0

    def calculate_final_score(self, item: dict) -> dict[str, float]:
        """计算综合评分"""
        content = item.get("content", "")

        scores = {
            "complexity": self.calculate_complexity_score(content),
            "diversity": self.calculate_diversity_score(item),
            "quality": self.calculate_quality_score(content),
            "representativeness": self.calculate_representativeness_score(item),
            "completeness": self.calculate_completeness_score(item),
            "unique_id_complexity": self.calculate_unique_id_complexity_score(item),
        }

        # 使用配置中的权重
        final_score = sum(scores[k] * self.weights[k] for k in self.weights)
        scores["final"] = final_score

        return scores


class FunctionQualityScorer:
    """函数级别质量评分器"""

    def __init__(self, weights: Optional[dict] = None):
        self.weights = weights or SAMPLING_CONFIG["quality_weights"]

    def calculate_function_quality(
        self, function: dict, file_context: dict
    ) -> dict[str, float]:
        """计算单个函数的质量评分"""
        body = function.get("body", "")
        name = function.get("name", "")
        unique_id = function.get("unique_id", "")

        scores = {}

        # 1. 代码复杂度评分 (0-100)
        lines = body.split("\n")
        code_lines = len(
            [l for l in lines if l.strip() and not l.strip().startswith("//")]
        )  # noqa: E741

        # 基础复杂度
        control_structures = len(
            re.findall(r"\b(if|for|while|switch|try|catch)\b", body)
        )
        nesting_depth = body.count("{") + body.count("}")

        complexity_score = min(
            100,
            (
                min(code_lines / 30 * 30, 30)  # 代码行数
                + min(control_structures / max(code_lines, 1) * 200, 35)  # 控制结构
                + min(nesting_depth / max(code_lines, 1) * 100, 20)  # 嵌套深度
                + min(len(body) / 500 * 15, 15)  # 代码长度
            ),
        )
        scores["complexity"] = complexity_score

        # 2. 命名质量评分 (0-100)
        naming_score = 0
        if name:
            # 排除简单命名
            simple_names = {
                "test",
                "demo",
                "temp",
                "example",
                "sample",
                "func",
                "method",
            }
            if name.lower() not in simple_names:
                naming_score += 30

            # 命名规范
            if re.match(r"^[a-z][a-zA-Z0-9]*$", name):  # camelCase
                naming_score += 25
            elif re.match(r"^[A-Z][a-zA-Z0-9]*$", name):  # PascalCase
                naming_score += 25

            # 语义丰富度
            if len(name) >= 4:
                naming_score += min(len(name) * 3, 30)

            # unique_id质量
            if unique_id and len(unique_id.split(".")) >= 2:
                naming_score += 15

        scores["naming"] = min(naming_score, 100)

        # 3. 代码质量评分 (0-100)
        quality_score = 0

        # 注释比例
        comment_lines = len([l for l in lines if l.strip().startswith("//")])  # noqa: E741
        if code_lines > 0:
            comment_ratio = comment_lines / code_lines
            quality_score += min(comment_ratio * 100, 25)

        # 代码规范
        if re.search(r"^[ \t]+\w", body, re.MULTILINE):  # 有缩进
            quality_score += 20

        # ArkUI特征
        if re.search(r"@Builder|@State|@Prop|@Link", body):
            quality_score += 15

        # 函数体完整性
        if body.count("{") == body.count("}"):
            quality_score += 20

        # 参数合理性
        params = re.findall(r"\(([^)]*)\)", body)
        if params and len(params[0]) < 100:  # 参数不太长
            quality_score += 20

        scores["quality"] = min(quality_score, 100)

        # 4. 代表性评分 (0-100)
        representative_score = 0

        # 函数类型代表性
        func_type = function.get("type", "")
        if func_type in ["method", "function"]:
            representative_score += 40
        elif func_type in ["struct", "class"]:
            representative_score += 30

        # ArkUI模式
        arkui_patterns = [
            r"@Builder",
            r"@State",
            r"@Prop",
            r"@Link",
            r"build\(\)",
            r"Column\s*\(",
            r"Row\s*\(",
            r"Flex\s*\(",
            r"Stack\s*\(",
        ]
        pattern_matches = sum(
            1 for pattern in arkui_patterns if re.search(pattern, body)
        )
        representative_score += min(pattern_matches * 10, 40)

        # 大小适中
        if 10 <= code_lines <= 50:
            representative_score += 20

        scores["representative"] = min(representative_score, 100)

        # 5. 上下文完整性评分 (0-100)
        context_score = 0

        if function.get("parent_definition"):
            context_score += 30

        if function.get("unique_id") and "." in function.get("unique_id", ""):
            context_score += 30

        if function.get("full_type"):
            context_score += 20

        if function.get("start_line") and function.get("end_line"):
            line_count = abs(function["end_line"] - function["start_line"]) + 1
            if 5 <= line_count <= 100:
                context_score += 20

        scores["context"] = min(context_score, 100)

        # 使用配置中的权重
        final_score = sum(scores[k] * self.weights[k] for k in self.weights)
        scores["final"] = final_score

        return scores


class IntegratedDataCollector:
    """集成数据收集器 - 先文件打分采样，再函数打分采样"""

    def __init__(self, config: CollectorConfig):
        self.config = config
        self.validator = FileValidator(config)
        self.analyzer = ArkTSAnalyzer()
        self.token_counter = TokenCounter()
        self.quality_scorer = QualityScorer(config.quality_weights)
        self.function_quality_scorer = FunctionQualityScorer(
            config.function_quality_weights
        )

        self.output_dir = Path(config.output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)

        self.all_files = []  # 使用FileInfo对象

    def collect_from_repo(self, repo_path: Path) -> tuple[int, str]:
        """从单个仓库收集数据"""
        if not repo_path.is_dir():
            return 0, str(repo_path)

        repo_name = repo_path.name
        processed_count = 0

        try:
            for root, dirs, files in os.walk(repo_path):
                exclude_dirs = self.config.exclude_dirs or set()
                dirs[:] = [d for d in dirs if d not in exclude_dirs]

                for file in files:
                    file_path = Path(root) / file
                    if self.validator.is_valid_file(file_path):
                        file_info = self._process_file(file_path, repo_name)
                        if file_info:
                            self.all_files.append(file_info)
                            processed_count += 1

        except Exception as e:
            logger.error(f"处理仓库 {repo_path} 时出错: {e}")

        return processed_count, repo_name

    def _process_file(self, file_path: Path, repo_name: str) -> Optional[FileInfo]:
        """处理单个文件并进行评分，返回FileInfo对象"""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()

            if not self.validator.validate_content(content):
                return None

            # 提取函数信息
            functions = self.analyzer.extract_functions(content)
            if not functions:
                return None

            # 创建FileInfo对象
            file_info = FileInfo(
                path=file_path,
                repo_name=repo_name,
                content=content,
                lines=content.splitlines(),
                functions=functions,
                token_count=self.token_counter.count_tokens(content),
                is_arkui=self.analyzer.is_arkui_file(content),
            )

            # 计算质量评分
            file_data = {"content": content, "functions": functions}
            file_info.quality_scores = self.quality_scorer.calculate_final_score(
                file_data
            )
            file_info.final_score = file_info.quality_scores["final"]

            return file_info

        except Exception as e:
            logger.debug(f"处理文件 {file_path} 时出错: {e}")
            return None

    def filter_and_sample(self) -> list[FileInfo]:
        """基于评分进行过滤和采样"""
        if not self.all_files:
            return []

        logger.info("开始基于评分进行过滤和采样...")

        # 按评分排序
        scored_files = sorted(self.all_files, key=lambda x: x.final_score, reverse=True)

        # 选择top百分比
        top_n = max(
            int(len(scored_files) * self.config.top_percent),
            self.config.target_size,
        )
        top_files = scored_files[:top_n]

        # 分层采样确保多样性
        final_selection = self._stratified_sampling(
            top_files, self.config.target_size, self.config.random_seed
        )

        return final_selection

    def _stratified_sampling(
        self, files: list[FileInfo], target_size: int, seed: int
    ) -> list[FileInfo]:
        """分层采样确保多样性"""
        import random

        random.seed(seed)

        # 按项目分层
        buckets = defaultdict(list)
        for file_info in files:
            buckets[file_info.repo_name].append(file_info)

        # 确保每个项目至少有1个样本
        selected = []
        remaining_capacity = target_size

        for project, files_in_project in buckets.items():
            if remaining_capacity <= 0:
                break

            selected_count = min(
                max(1, len(files_in_project) // 10), remaining_capacity
            )
            selected.extend(
                random.sample(
                    files_in_project, min(selected_count, len(files_in_project))
                )
            )
            remaining_capacity -= selected_count

        # 如果还有容量，按评分补充
        if remaining_capacity > 0:
            all_files = [f for files in buckets.values() for f in files]
            remaining = [f for f in all_files if f not in selected]
            remaining.sort(key=lambda x: x.final_score, reverse=True)
            selected.extend(remaining[:remaining_capacity])

        selected.sort(key=lambda x: x.final_score, reverse=True)
        return selected[:target_size]

    def score_functions_in_place(self, filtered_files: list[FileInfo]):
        """对高质量文件中的functions进行评分，直接修改原函数对象"""
        logger.info("开始对functions进行评分...")

        total_functions = 0
        scored_functions = 0

        # 直接在原函数对象上添加评分信息
        for file_info in filtered_files:
            functions = file_info.functions or []
            total_functions += len(functions)

            for func in functions:
                # 计算函数质量评分
                scores = self.function_quality_scorer.calculate_function_quality(
                    func,
                    {
                        "content": file_info.content,
                        "repo": file_info.repo_name,
                        "isArkUI": file_info.is_arkui,
                    },
                )

                # 直接在原函数对象上添加评分字段
                func["quality_scores"] = scores
                func["final_score"] = scores["final"]
                func["source_file"] = str(file_info.path)
                func["file_repo"] = file_info.repo_name
                func["file_isArkUI"] = file_info.is_arkui

                scored_functions += 1

        logger.info(f"共对 {scored_functions}/{total_functions} 个functions进行了评分")

        # 返回统计信息
        return {
            "total_functions": total_functions,
            "scored_functions": scored_functions,
            "mean_function_score": np.mean(
                [
                    func["final_score"]
                    for file_info in filtered_files
                    for func in (file_info.functions or [])
                    if "final_score" in func
                ]
            )
            if scored_functions > 0
            else 0,
        }

    def save_results(self, filtered_files: list[FileInfo]):
        """保存结果 - 简化版（添加repo_path信息）"""
        # 只保存高质量数据集
        high_quality_path = self.output_dir / "arkts_high_quality_dataset.jsonl"

        # 尝试推断项目路径
        repo_path = None
        if filtered_files:
            first_file_path = Path(filtered_files[0].path)
            # 向上查找到包含.git或README.md的目录
            current = first_file_path.parent
            while current != current.parent:
                if (current / ".git").exists() or (current / "README.md").exists():
                    repo_path = str(current)
                    break
                current = current.parent

        with open(high_quality_path, "w", encoding="utf-8") as f:
            for file_info in filtered_files:
                # 转换为原始数据结构格式
                data = {
                    "content": file_info.content,
                    "filepath": str(file_info.path),
                    "repo": file_info.repo_name,
                    "isArkUI": file_info.is_arkui,
                    "functions": file_info.functions,
                    "token_count": file_info.token_count,
                    "quality_scores": file_info.quality_scores,
                    "final_score": file_info.final_score,
                }
                # 添加repo_path信息供文档分析使用
                if repo_path:
                    data["repo_path"] = repo_path

                f.write(json.dumps(data, ensure_ascii=False) + "\n")

        logger.info(f"高质量数据集已保存到: {high_quality_path}")
        if repo_path:
            logger.info(f"检测到项目路径: {repo_path}")

    def collect_all(self, use_parallel: bool = False) -> dict[str, Any]:
        """收集所有仓库的数据并进行质量评估"""
        logger.info("开始集成数据收集和质量评估...")

        repos_dir = Path(self.config.repos_dir)
        if not repos_dir.exists():
            raise FileNotFoundError(f"仓库目录不存在: {repos_dir}")

        repo_paths = [d for d in repos_dir.iterdir() if d.is_dir()]
        logger.info(f"找到 {len(repo_paths)} 个仓库")

        total_files = 0

        if use_parallel and len(repo_paths) > 1:
            with ThreadPoolExecutor(max_workers=4) as executor:
                results = list(
                    tqdm(
                        executor.map(self.collect_from_repo, repo_paths),
                        total=len(repo_paths),
                        desc="处理仓库",
                    )
                )

                for file_count, repo_name in results:
                    total_files += file_count
                    if file_count > 0:
                        logger.debug(f"仓库 {repo_name}: {file_count} 个文件")
        else:
            for repo_path in tqdm(repo_paths, desc="处理仓库"):
                file_count, repo_name = self.collect_from_repo(repo_path)
                total_files += file_count
                if file_count > 0:
                    logger.debug(f"仓库 {repo_name}: {file_count} 个文件")

        logger.info(f"总共收集 {len(self.all_files)} 个文件")

        # 进行质量过滤和采样
        filtered_files = self.filter_and_sample()

        # 对高质量文件中的functions进行评分
        function_stats = self.score_functions_in_place(filtered_files)

        # 保存结果（包含评分后的functions）
        self.save_results(filtered_files)

        # 生成统计报告
        stats = {
            "total_repos": len(repo_paths),
            "total_files_collected": len(self.all_files),
            "high_quality_files": len(filtered_files),
            "total_functions": function_stats["total_functions"],
            "scored_functions": function_stats["scored_functions"],
            "selection_rate": len(filtered_files) / len(self.all_files)
            if self.all_files
            else 0,
            "mean_final_score": np.mean([f.final_score for f in filtered_files])
            if filtered_files
            else 0,
            "mean_function_score": function_stats["mean_function_score"],
            "output_dir": str(self.output_dir),
            "timestamp": datetime.now().isoformat(),
        }

        # 保存统计信息
        stats_path = self.output_dir / "pipeline_stats.json"
        with open(stats_path, "w", encoding="utf-8") as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)

        logger.info("=== 集成数据收集完成 ===")
        logger.info(
            f"从 {len(self.all_files)} 个文件中选择 {len(filtered_files)} 个高质量文件"
        )
        logger.info(f"文件选择率: {stats['selection_rate']:.2%}")
        logger.info(f"共对 {function_stats['scored_functions']} 个functions进行了评分")
        logger.info(f"平均函数评分: {function_stats['mean_function_score']:.2f}")

        return stats


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="ArkTS集成数据收集器")
    parser.add_argument(
        "--repos-dir", default=DATA_COLLECTION_CONFIG["repos_dir"], help="仓库目录路径"
    )
    parser.add_argument("--output-dir", default="pipeline_output", help="输出目录")
    parser.add_argument("--parallel", action="store_true", help="使用并行处理")
    parser.add_argument("--verbose", action="store_true", help="详细日志")
    parser.add_argument(
        "--target-size",
        type=int,
        default=FILTER_CONFIG["target_size"],
        help="目标高质量文件数量",
    )
    parser.add_argument(
        "--top-percent",
        type=float,
        default=FILTER_CONFIG["top_percent"],
        help="选择top百分比",
    )

    args = parser.parse_args()
    args.output_dir = "/Users/<USER>/projects/codebase_retriever/evaluation/benchmark/test"
    # args.repo_dir = "/Users/<USER>/projects/hmos_projects/oh-example-composer"

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    config = CollectorConfig(
        repos_dir=args.repos_dir,
        output_dir=args.output_dir,
        target_size=args.target_size,
        top_percent=args.top_percent,
    )

    collector = IntegratedDataCollector(config)

    try:
        stats = collector.collect_all(use_parallel=args.parallel)
        print(json.dumps(stats, indent=2, ensure_ascii=False))
    except Exception as e:
        logger.error(f"数据收集失败: {e}")
        return 1

    return 0


if __name__ == "__main__":
    main()
