#!/usr/bin/env python3
"""
Enhanced LLM-based Natural Language Query Generator for ArkTS Functions
基于项目上下文理解的增强版查询生成器，生成更符合开发者实际工作流程的搜索语句
集成项目文档理解功能，提供更准确的架构和功能上下文
"""

import argparse
import asyncio
import concurrent.futures
import glob
import json
import logging
import os
import re
import time
from ast import arg
from collections import defaultdict
from dataclasses import dataclass
from datetime import datetime
from functools import lru_cache
from pathlib import Path
from typing import Any, Optional

import requests

# Configure DeepSeek API key
os.environ["DEEPSEEK_API_KEY"] = "***********************************"


@dataclass
class ProjectContext:
    """项目上下文信息（简化版）"""

    repo_name: str
    common_patterns: list[str]
    business_domains: list[str]
    ui_components: list[str]
    architecture_patterns: list[str]
    # 简化后保留的关键字段
    project_summary: str = ""
    main_features: Optional[list[str]] = None
    technology_stack: Optional[list[str]] = None

    def __post_init__(self):
        if self.main_features is None:
            self.main_features = []
        if self.technology_stack is None:
            self.technology_stack = []


class ProjectDocumentationReader:
    """
    项目文档阅读器 - 读取和理解项目文档，提取架构、功能、接口等信息
    优先使用README.md进行项目理解，支持结果持久化
    """

    def __init__(self, api_key: Optional[str] = None, base_url: Optional[str] = None):
        self.api_key = api_key or os.getenv("DEEPSEEK_API_KEY")
        self.base_url = base_url or "https://api.deepseek.com"

        # 文档文件类型
        self.doc_extensions = {".md", ".txt", ".rst", ".doc", ".docx"}

        # 重要文档文件名模式
        self.important_doc_patterns = [
            r"README",
            r"ARCHITECTURE",
            r"DESIGN",
            r"API",
            r"GUIDE",
            r"OVERVIEW",
            r"架构",
            r"设计",
            r"文档",
            r"说明",
        ]

        # ArkTS项目特定文件模式
        self.arkts_config_patterns = [
            r"build-profile\.json5",
            r"hvigorfile\.ts",
            r"oh-package\.json5",
            r"app\.json5",
            r"module\.json5",
        ]

        # 移除缓存持久化功能

    def find_readme_file(self, repo_path: str) -> Optional[Path]:
        """查找README.md文件"""
        repo_path_obj = Path(repo_path)

        # 优先查找根目录的README.md
        readme_patterns = ["README.md", "readme.md", "Readme.md", "README.MD"]

        for pattern in readme_patterns:
            readme_path = repo_path_obj / pattern
            if readme_path.exists():
                return readme_path

        # 如果根目录没有，查找子目录
        for readme_file in repo_path_obj.rglob("README.md"):
            # 优先选择层级较浅的README
            if len(readme_file.parts) - len(repo_path_obj.parts) <= 2:
                return readme_file

        return None

    def get_project_understanding(self, repo_path: str) -> dict[str, Any]:
        """获取项目理解信息，结合现有的 .project_analysis_cache 目录和 README.md 信息"""
        # 尝试从现有的 .project_analysis_cache 目录读取
        cache_dir = Path(".project_analysis_cache")
        repo_name = Path(repo_path).name
        cache_file = cache_dir / f"{repo_name}.txt"

        cache_analysis = {}
        readme_analysis = {}

        # 1. 读取缓存分析文件
        if cache_file.exists():
            print(f"找到现有项目分析文件: {cache_file}")
            cache_analysis = self._parse_existing_cache_file(cache_file)
        else:
            print(f"未找到项目 {repo_name} 的缓存分析文件")

        # 2. 读取项目README.md文件
        if repo_path and Path(repo_path).exists():
            readme_path = self.find_readme_file(repo_path)
            if readme_path:
                print(f"找到README文件: {readme_path}")
                readme_analysis = self.analyze_readme_content(readme_path)
            else:
                print(f"未找到项目 {repo_name} 的README.md文件")

        # 3. 合并分析结果（README优先级更高）
        return self._merge_project_analysis(cache_analysis, readme_analysis, repo_name)

    def _parse_existing_cache_file(self, cache_file: Path) -> dict[str, Any]:
        """解析现有的项目分析缓存文件（增强版）"""
        try:
            with open(cache_file, "r", encoding="utf-8") as f:
                content = f.read()

            analysis = self._get_empty_analysis()

            # 1. 提取项目概述（从文件结构描述中）
            analysis["project_summary"] = self._extract_summary_from_cache(content)

            # 2. 提取技术栈
            analysis["technology_stack"] = self._extract_tech_stack_from_cache(content)

            # 3. 提取业务领域
            analysis["business_domains"] = self._extract_business_domains_from_cache(
                content
            )

            # 4. 从文件结构中提取主要功能
            analysis["main_features"] = self._extract_features_from_cache(content)

            print(
                f"从现有缓存解析完成: {len(analysis['main_features'])}个功能, {len(analysis['technology_stack'])}个技术栈"
            )
            return analysis

        except Exception as e:
            print(f"解析现有缓存文件失败: {e}")
            return self._get_empty_analysis()

    def _extract_summary_from_cache(self, content: str) -> str:
        """从缓存文件中提取项目概述"""
        lines = content.split("\n")[:15]  # 扩展到前15行
        summary_parts = []

        for line in lines:
            line = line.strip()
            # 跳过文件路径行
            if (
                line
                and not line.startswith("/")
                and not line.startswith(".")
                and len(line) > 15
            ):
                # 过滤文件扩展名和特殊字符
                if not any(
                    ext in line for ext in [".ets", ".ts", ".js", "├──", "└──", "│"]
                ):
                    summary_parts.append(line)
                    if len(" ".join(summary_parts)) > 250:
                        break

        return " ".join(summary_parts)[:400] if summary_parts else ""

    def _extract_tech_stack_from_cache(self, content: str) -> list[str]:
        """从缓存文件中提取技术栈"""
        tech_stack = []
        content_lower = content.lower()

        # 扩展技术栈关键词
        tech_mapping = {
            "arkts": "ArkTS",
            "harmonyos": "HarmonyOS",
            "arkui": "ArkUI",
            "ets": "ArkUI ETS",
            "typescript": "TypeScript",
            "javascript": "JavaScript",
            "java": "Java",
            "node.js": "Node.js",
            "react": "React",
            "vue": "Vue",
            "angular": "Angular",
        }

        for tech_key, tech_name in tech_mapping.items():
            if tech_key in content_lower and tech_name not in tech_stack:
                tech_stack.append(tech_name)

        # 根据文件类型推断技术栈
        if ".ets" in content and "ArkUI ETS" not in tech_stack:
            tech_stack.append("ArkUI ETS")
        if "hvigorfile.ts" in content and "Hvigor" not in tech_stack:
            tech_stack.append("Hvigor")
        if "oh-package.json5" in content and "OpenHarmony" not in tech_stack:
            tech_stack.append("OpenHarmony")

        return tech_stack

    def _extract_business_domains_from_cache(self, content: str) -> list[str]:
        """从缓存文件中提取业务领域"""
        domains = []
        content_lower = content.lower()

        # 业务领域关键词映射
        domain_mapping = {
            ("应用", "app", "mobile", "移动"): "移动应用开发",
            ("组件", "component", "ui"): "UI组件开发",
            ("登录", "login", "auth"): "用户认证",
            ("购物", "shop", "cart", "order", "订单"): "电子商务",
            ("相机", "camera", "photo", "拍照"): "多媒体处理",
            ("设置", "setting", "config"): "系统配置",
            ("联系", "contact", "conversation", "对话"): "通讯交流",
            ("新闻", "news", "media"): "媒体资讯",
            ("旅游", "tourist", "travel"): "旅游服务",
            ("教育", "education", "learn"): "教育培训",
            ("办公", "office", "work"): "办公协同",
            ("保险", "insurance"): "金融保险",
        }

        for keywords, domain in domain_mapping.items():
            if any(
                keyword in content_lower or keyword in content for keyword in keywords
            ):
                domains.append(domain)

        return list(set(domains))

    def _extract_features_from_cache(self, content: str) -> list[str]:
        """从缓存文件中提取主要功能（基于文件描述）"""
        features = []

        # 从注释描述中提取功能
        feature_patterns = [
            r"#\s*([^\n#]+)(?:提供|功能|组件|处理|管理|服务)",  # 从注释中提取
            r"([^\n]+)(?:页面|组件|功能|模块)",  # 页面/组件名称
            r"([^\n]+)用于([^\n#]+)",  # "用于"模式
        ]

        for pattern in feature_patterns:
            matches = re.findall(pattern, content)
            for match in matches[:5]:  # 限制每个模式最多5个
                if isinstance(match, tuple):
                    feature = " ".join(match).strip()
                else:
                    feature = match.strip()

                # 清理和过滤
                feature = re.sub(r"[#\-*]+", "", feature).strip()
                if 10 < len(feature) < 50 and feature not in features:
                    features.append(feature)

        # 基于目录结构推断功能
        structure_features = []
        if "features/" in content:
            # 提取features目录下的子目录名
            feature_dirs = re.findall(r"features/([^/\n]+)", content)
            for dir_name in set(feature_dirs):
                if dir_name not in ["src", "main", "ets"]:
                    structure_features.append(f"{dir_name}功能模块")

        # 合并功能列表
        all_features = features + structure_features
        return list(set(all_features))[:8]  # 去重并限制最多8个

    def analyze_readme_content(self, readme_path: Path) -> dict[str, Any]:
        """分析README.md内容，提取项目信息（简化版）"""
        try:
            with open(readme_path, "r", encoding="utf-8") as f:
                content = f.read()
        except Exception as e:
            print(f"读取README失败: {e}")
            return self._get_empty_analysis()

        analysis = self._get_empty_analysis()
        lines = content.split("\n")

        # 1. 提取项目概述
        project_summary = self._extract_project_summary(lines)
        analysis["project_summary"] = project_summary

        # 2. 提取主要功能特性
        main_features = self._extract_features_from_readme(content)
        analysis["main_features"] = main_features

        # 3. 提取技术栈
        technology_stack = self._extract_technology_stack(content)
        analysis["technology_stack"] = technology_stack

        # 4. 提取业务领域
        business_domains = self._extract_business_domains(content)
        analysis["business_domains"] = business_domains

        print(
            f"README分析完成: 提取到 {len(main_features)} 个功能, {len(technology_stack)} 个技术栈"
        )
        return analysis

    def _extract_project_summary(self, lines: list[str]) -> str:
        """从README中提取项目概述"""
        summary_lines = []
        found_description = False

        for line in lines[:20]:  # 只看前20行
            line = line.strip()
            if not line or line.startswith("#"):
                if found_description and summary_lines:
                    break
                continue

            # 跳过badge等
            if any(
                badge in line.lower() for badge in ["badge", "shield", "img", "license"]
            ):
                continue

            summary_lines.append(line)
            found_description = True

            if len(" ".join(summary_lines)) > 200:
                break

        return " ".join(summary_lines)[:300] if summary_lines else ""

    def _extract_features_from_readme(self, content: str) -> list[str]:
        """从README中提取功能特性（增强版）"""
        features = []

        # 扩展功能关键词匹配
        feature_keywords = [
            # 技术功能
            "代码检索",
            "智能搜索",
            "代码分析",
            "向量检索",
            "API服务",
            "多语言解析",
            "增量索引",
            "实时更新",
            "混合检索",
            "语义检索",
            # 业务功能
            "用户登录",
            "用户注册",
            "用户管理",
            "权限管理",
            "商品管理",
            "购物车",
            "订单管理",
            "支付功能",
            "消息通知",
            "文件管理",
            "图片处理",
            "相机功能",
            "位置服务",
            "地图导航",
            "社交分享",
            "评论系统",
        ]

        content_lower = content.lower()
        for keyword in feature_keywords:
            if keyword.lower() in content_lower or keyword in content:
                features.append(keyword)

        # 从列表项中提取（增强模式）
        list_patterns = [
            r"[-*]\s*([^\n]+)",  # 基本列表
            r"\d+\.\s*([^\n]+)",  # 数字列表
            r"##\s*([^\n]+)",  # 二级标题
        ]

        for pattern in list_patterns:
            matches = re.findall(pattern, content)
            for match in matches[:5]:
                clean_item = re.sub(r"[\*\*`#]", "", match).strip()
                if 8 < len(clean_item) < 60 and clean_item not in features:
                    # 过滤明显的非功能项
                    if not any(
                        skip in clean_item.lower()
                        for skip in [
                            "install",
                            "setup",
                            "requirement",
                            "license",
                            "author",
                            "version",
                        ]
                    ):
                        features.append(clean_item)

        return list(set(features))[:10]  # 去重并增加到最多10个

    def _extract_technology_stack(self, content: str) -> list[str]:
        """提取技术栈信息（增强版）"""
        tech_stack = []
        content_lower = content.lower()

        # 扩展技术栈关键词映射
        tech_keywords = {
            "arkts": "ArkTS",
            "harmonyos": "HarmonyOS",
            "arkui": "ArkUI",
            "ets": "ArkUI ETS",
            "python": "Python",
            "fastapi": "FastAPI",
            "typescript": "TypeScript",
            "javascript": "JavaScript",
            "tree-sitter": "Tree-sitter",
            "react": "React",
            "vue": "Vue.js",
            "angular": "Angular",
            "node.js": "Node.js",
            "nodejs": "Node.js",
            "express": "Express.js",
            "nest": "NestJS",
            "spring": "Spring",
            "django": "Django",
            "flask": "Flask",
            "docker": "Docker",
            "kubernetes": "Kubernetes",
            "redis": "Redis",
            "mysql": "MySQL",
            "postgresql": "PostgreSQL",
            "mongodb": "MongoDB",
        }

        for tech_key, tech_name in tech_keywords.items():
            if tech_key in content_lower and tech_name not in tech_stack:
                tech_stack.append(tech_name)

        # 从代码块或特殊格式中提取
        code_block_pattern = r"```(\w+)\n"
        code_matches = re.findall(code_block_pattern, content)
        for match in code_matches:
            if match.lower() in tech_keywords:
                tech_name = tech_keywords[match.lower()]
                if tech_name not in tech_stack:
                    tech_stack.append(tech_name)

        return list(set(tech_stack))

    def _extract_business_domains(self, content: str) -> list[str]:
        """提取业务领域（增强版）"""
        domains = []
        content_lower = content.lower()

        # 扩展业务领域关键词映射
        domain_mapping = {
            # 技术领域
            ("code", "codebase", "代码", "programming"): "代码开发工具",
            ("search", "retrieval", "检索", "搜索", "query"): "搜索检索服务",
            ("ai", "llm", "intelligent", "智能", "machine learning"): "人工智能",
            # 业务领域
            ("app", "mobile", "应用", "移动"): "移动应用开发",
            ("e-commerce", "shopping", "电商", "购物"): "电子商务",
            ("education", "教育", "learning", "course"): "教育培训",
            ("finance", "金融", "payment", "banking"): "金融服务",
            ("social", "社交", "chat", "message"): "社交通讯",
            ("media", "news", "媒体", "新闻"): "媒体资讯",
            ("game", "gaming", "游戏"): "游戏娱乐",
            ("health", "健康", "medical"): "医疗健康",
            ("travel", "旅游", "tourism"): "旅游服务",
            ("office", "办公", "productivity"): "办公协同",
            ("iot", "物联网", "smart home"): "物联网",
        }

        for keywords, domain in domain_mapping.items():
            if any(keyword in content_lower for keyword in keywords):
                domains.append(domain)

        return list(set(domains))

    def find_documentation_files(self, repo_path: str) -> list[Path]:
        repo_path_obj = Path(repo_path)
        doc_files = []

        # 递归查找文档文件
        for pattern in ["**/*.md", "**/*.txt", "**/*.rst"]:
            doc_files.extend(repo_path_obj.glob(pattern))

        # 按重要性排序
        def doc_importance_score(file_path: Path) -> int:
            filename = file_path.name.upper()
            score = 0

            # README 最重要
            if "README" in filename:
                score += 100

            # 架构和设计文档
            for pattern in self.important_doc_patterns:
                if re.search(pattern.upper(), filename):
                    score += 50

            # 根目录文件加分
            if len(file_path.parts) <= len(repo_path_obj.parts) + 1:
                score += 30

            # 文件大小加分
            try:
                size = file_path.stat().st_size
                if 1000 < size < 50000:  # 合适的文档大小
                    score += 20
            except:
                pass

            return score

        # 排序并返回重要文档
        sorted_docs = sorted(doc_files, key=doc_importance_score, reverse=True)
        return sorted_docs[:10]  # 最多返10个重要文档

    def read_documentation_content(self, doc_files: list[Path]) -> dict[str, str]:
        """读取文档内容"""
        docs_content = {}

        for doc_file in doc_files:
            try:
                with open(doc_file, "r", encoding="utf-8") as f:
                    content = f.read()

                # 限制文档大小
                if len(content) > 20000:
                    content = content[:20000] + "..."

                docs_content[str(doc_file)] = content
            except Exception as e:
                print(f"读取文档 {doc_file} 失败: {e}")
                continue

        return docs_content

    def analyze_project_documentation(self, repo_path: str) -> dict[str, Any]:
        """分析项目文档并提取关键信息（结合缓存和README）"""
        return self.get_project_understanding(repo_path)

    def _get_empty_analysis(self) -> dict[str, Any]:
        """返回空的分析结果"""
        return {
            "project_summary": "",
            "main_features": [],
            "technology_stack": [],
            "business_domains": [],
        }

    def _merge_project_analysis(
        self,
        cache_analysis: dict[str, Any],
        readme_analysis: dict[str, Any],
        repo_name: str,
    ) -> dict[str, Any]:
        """合并缓存分析和README分析结果"""
        merged_analysis = self._get_empty_analysis()

        # 1. 项目概述：优先使用README，如果为空则使用缓存
        readme_summary = readme_analysis.get("project_summary", "").strip()
        cache_summary = cache_analysis.get("project_summary", "").strip()

        if readme_summary:
            merged_analysis["project_summary"] = readme_summary
        elif cache_summary:
            merged_analysis["project_summary"] = cache_summary

        # 2. 主要功能：合并并去重
        readme_features = readme_analysis.get("main_features", [])
        cache_features = cache_analysis.get("main_features", [])
        all_features = list(set(readme_features + cache_features))
        merged_analysis["main_features"] = all_features[:10]  # 限制最多10个功能

        # 3. 技术栈：合并并去重
        readme_tech = readme_analysis.get("technology_stack", [])
        cache_tech = cache_analysis.get("technology_stack", [])
        all_tech = list(set(readme_tech + cache_tech))
        merged_analysis["technology_stack"] = all_tech

        # 4. 业务领域：合并并去重
        readme_domains = readme_analysis.get("business_domains", [])
        cache_domains = cache_analysis.get("business_domains", [])
        all_domains = list(set(readme_domains + cache_domains))
        merged_analysis["business_domains"] = all_domains

        print(f"合并分析结果完成 [{repo_name}]: ")
        print(f"  - 功能特性: {len(merged_analysis['main_features'])}个")
        print(f"  - 技术栈: {len(merged_analysis['technology_stack'])}个")
        print(f"  - 业务领域: {len(merged_analysis['business_domains'])}个")
        print(f"  - 概述长度: {len(merged_analysis['project_summary'])}字符")

        return merged_analysis


class ProjectAnalyzer:
    """项目分析器 - 分析整个项目的上下文信息并集成文档理解"""

    def __init__(self, api_key: Optional[str] = None, base_url: Optional[str] = None):
        self.arkui_components = {
            "Column",
            "Row",
            "Stack",
            "Flex",
            "Button",
            "Text",
            "Image",
            "list",
            "Grid",
            "Tabs",
            "Navigation",
            "Scroll",
            "Swiper",
        }

        self.business_keywords = {
            "permission": "权限管理",
            "calendar": "日历功能",
            "payment": "支付处理",
            "scan": "扫码功能",
            "message": "消息处理",
            "network": "网络请求",
            "storage": "数据存储",
            "media": "媒体处理",
            "location": "位置服务",
            "camera": "相机功能",
            "notification": "通知管理",
            "user": "用户管理",
        }

        # 初始化文档阅读器
        self.doc_reader = ProjectDocumentationReader(api_key, base_url)
        self._cached_doc_analysis = None
        self._repo_path = None

    def analyze_project_context(
        self, functions_data: list[dict[str, Any]], repo_path: Optional[str] = None
    ) -> ProjectContext:
        """分析项目整体上下文（集成文档分析）"""

        # 基础代码分析
        repo_names = set()
        all_patterns = []
        business_domains = []
        ui_components = []

        for item in functions_data:
            repo_names.add(item.get("repo", "unknown"))

            for func in item.get("functions", []):
                body = func.get("body", "")

                # 分析UI组件使用
                for component in self.arkui_components:
                    if component in body:
                        ui_components.append(component)

                # 分析业务域
                for keyword, domain in self.business_keywords.items():
                    if keyword in body.lower():
                        business_domains.append(domain)

                # 分析代码模式
                if "@Builder" in body:
                    all_patterns.append("Builder模式")
                if "@State" in body:
                    all_patterns.append("状态管理")
                if "@Component" in body:
                    all_patterns.append("组件开发")

        # 文档分析（如果提供了repo_path）
        doc_analysis = {}
        if repo_path and repo_path != self._repo_path:
            print(f"开始分析项目文档: {repo_path}")
            doc_analysis = self.doc_reader.analyze_project_documentation(repo_path)
            self._cached_doc_analysis = doc_analysis
            self._repo_path = repo_path
        elif self._cached_doc_analysis:
            doc_analysis = self._cached_doc_analysis

        # 构建增强的项目上下文
        context = ProjectContext(
            repo_name=list(repo_names)[0] if repo_names else "unknown",
            common_patterns=list(set(all_patterns)),
            business_domains=list(
                set(business_domains + doc_analysis.get("business_domains", []))
            ),
            ui_components=list(set(ui_components)),
            architecture_patterns=["ArkUI", "ArkTS", "HarmonyOS"],
            # 文档分析结果
            project_summary=doc_analysis.get("project_summary", ""),
            main_features=doc_analysis.get("main_features", []),
            technology_stack=doc_analysis.get("technology_stack", []),
        )

        if doc_analysis:
            print(
                f"文档分析完成: 提取到 {len(context.main_features or [])} 个主要功能, {len(context.technology_stack or [])} 个技术栈"
            )

        return context

    def get_project_documentation_summary(self) -> str:
        """获取项目文档摘要（简化版）"""
        if not self._cached_doc_analysis:
            return "暂无项目文档信息"

        doc = self._cached_doc_analysis
        summary_parts = []

        if doc.get("project_summary"):
            summary_parts.append(f"项目概述: {doc['project_summary']}")

        if doc.get("main_features"):
            features = ", ".join(doc["main_features"][:3])
            summary_parts.append(f"主要功能: {features}")

        if doc.get("technology_stack"):
            tech = ", ".join(doc["technology_stack"][:3])
            summary_parts.append(f"技术栈: {tech}")

        return "\n".join(summary_parts) if summary_parts else "暂无项目文档信息"

    def extract_function_context(self, function: dict[str, Any]) -> dict[str, Any]:
        """提取函数的详细上下文信息"""
        body = function.get("body", "")
        name = function.get("name", "")

        return {
            "function_name": name,
            "business_domain": self._identify_business_domain(body),
            "ui_components": self._identify_ui_components(body),
            "complexity_level": self._assess_complexity(body),
            "main_responsibility": self._identify_responsibility(body),
            "usage_scenarios": self._identify_usage_scenarios(body),
        }

    def _identify_business_domain(self, body: str) -> str:
        """识别业务领域"""
        body_lower = body.lower()
        for keyword, domain in self.business_keywords.items():
            if keyword in body_lower:
                return domain
        return (
            "UI界面"
            if any(comp in body for comp in self.arkui_components)
            else "通用业务"
        )

    def _identify_ui_components(self, body: str) -> list[str]:
        """识别使用的UI组件"""
        return [comp for comp in self.arkui_components if comp in body]

    def _assess_complexity(self, body: str) -> str:
        """评估代码复杂度"""
        code_lines = len(
            [
                l
                for l in body.split("\n")
                if l.strip() and not l.strip().startswith("//")
            ]
        )
        return "简单" if code_lines < 10 else "中等" if code_lines < 30 else "复杂"

    def _identify_responsibility(self, body: str) -> str:
        """识别主要职责"""
        if "@Builder" in body:
            return "UI构建器"
        elif "@Component" in body:
            return "组件定义"
        elif "permission" in body.lower():
            return "权限处理"
        elif "network" in body.lower() or "http" in body.lower():
            return "网络通信"
        else:
            return "业务逻辑"

    def _identify_usage_scenarios(self, body: str) -> list[str]:
        """识别使用场景"""
        scenarios = []
        if "@Builder" in body:
            scenarios.append("UI组件构建")
        if "permission" in body.lower():
            scenarios.append("权限申请场景")
        if "calendar" in body.lower():
            scenarios.append("日历相关功能")
        if "payment" in body.lower():
            scenarios.append("支付流程")
        return scenarios if scenarios else ["通用功能场景"]

    def _extract_business_keywords(self, body: str) -> list[str]:
        """提取业务关键词"""
        keywords = []
        body_lower = body.lower()

        business_terms = {
            "login": "用户登录",
            "register": "用户注册",
            "payment": "支付处理",
            "order": "订单管理",
            "user": "用户管理",
            "profile": "个人资料",
            "setting": "设置配置",
            "notification": "消息通知",
            "camera": "拍照功能",
            "location": "位置服务",
            "share": "分享功能",
            "download": "下载功能",
            "upload": "上传功能",
            "search": "搜索功能",
            "filter": "筛选功能",
            "favorite": "收藏功能",
            "cart": "购物车",
            "comment": "评论功能",
            "rating": "评分功能",
        }

        for eng, chn in business_terms.items():
            if eng in body_lower:
                keywords.append(chn)

        return keywords[:3]  # 最多返回3个关键词

    def _extract_user_actions(self, body: str) -> list[str]:
        """提取用户操作"""
        actions = []
        body_lower = body.lower()

        action_patterns = {
            "click": "点击操作",
            "tap": "点击操作",
            "swipe": "滑动操作",
            "scroll": "滚动操作",
            "input": "输入操作",
            "select": "选择操作",
            "submit": "提交操作",
            "cancel": "取消操作",
            "confirm": "确认操作",
            "refresh": "刷新操作",
            "back": "返回操作",
            "close": "关闭操作",
            "open": "打开操作",
            "save": "保存操作",
            "delete": "删除操作",
            "edit": "编辑操作",
        }

        for pattern, action in action_patterns.items():
            if pattern in body_lower:
                actions.append(action)

        return list(set(actions))[:3]  # 去重并最多返回3个

    def _identify_technical_challenges(self, body: str) -> list[str]:
        """识别技术挑战"""
        challenges = []
        body_lower = body.lower()

        challenge_indicators = {
            "async": "异步处理",
            "await": "异步等待",
            "promise": "异步Promise",
            "try": "异常处理",
            "catch": "错误捕获",
            "timeout": "超时处理",
            "retry": "重试机制",
            "cache": "缓存机制",
            "validate": "数据验证",
            "encrypt": "数据加密",
            "compress": "数据压缩",
            "thread": "多线程",
            "lock": "线程锁",
            "memory": "内存管理",
            "performance": "性能优化",
            "security": "安全控制",
        }

        for indicator, challenge in challenge_indicators.items():
            if indicator in body_lower:
                challenges.append(challenge)

        return list(set(challenges))[:3]  # 去重并最多返回3个


class EnhancedLLMQueryGenerator:
    """增强版LLM查询生成器（集成文档理解 + 性能优化 + 日志统计）"""

    def __init__(
        self,
        api_key: Optional[str] = None,
        base_url: Optional[str] = None,
        model: str = "deepseek-chat",
        batch_size: int = 5,  # 新增：批处理大小
        max_concurrent: int = 3,  # 新增：最大并发数
        log_level: str = "INFO",  # 新增：日志级别
    ):
        self.api_key = api_key or os.getenv("DEEPSEEK_API_KEY")
        self.base_url = base_url or "https://api.deepseek.com"
        self.model = model
        self.batch_size = batch_size
        self.max_concurrent = max_concurrent
        self.project_analyzer = ProjectAnalyzer(api_key, base_url)

        # 性能优化：缓存常用数据
        self._context_cache = {}
        self._fallback_cache = {}

        # 日志和时间统计配置
        self._setup_logger(log_level)
        self._timing_stats = {
            "total_start_time": None,
            "total_end_time": None,
            "llm_api_calls": [],
            "cache_hits": 0,
            "cache_misses": 0,
            "function_processing_times": [],
            "batch_processing_times": [],
            "project_analysis_time": 0,
            "total_functions_processed": 0,
            "successful_generations": 0,
            "fallback_generations": 0,
        }

        # 增强的系统提示词，更注重实际开发场景和项目上下文
        self.system_prompt = """你是一个经验丰富的HarmonyOS/ArkTS开发者，深度了解企业级应用开发的真实场景和痛点。

**核心任务：**
基于提供的代码片段，生成真实开发者在实际工作中会搜索的自然语言查询。这些查询必须反映具体的业务需求、技术挑战和实际开发场景。

**查询生成原则：**
1. **业务导向** - 查询要体现具体的业务场景和用户需求
2. **问题驱动** - 模拟开发者遇到的真实技术问题
3. **实用性强** - 查询要能直接指导开发工作
4. **口语化表达** - 使用开发者日常交流的自然语言
5. **场景具体** - 避免泛化的技术描述，要有明确的使用场景

**真实查询场景模拟：**

🔍 **功能开发场景**
- "我需要实现一个[具体功能]，用户可以[具体操作]，有没有类似的代码参考？"
- "在[业务场景]中，如何处理[具体问题]的逻辑？"
- "开发[功能模块]时，需要调用哪些API接口？"

🐛 **问题解决场景**
- "[功能]在[使用场景]下出现[具体问题]，怎么排查？"
- "用户反馈[具体现象]，可能是哪里的代码问题？"
- "[操作步骤]后系统[异常表现]，如何修复？"

🚀 **优化改进场景**
- "[功能模块]在[使用情况]下性能较慢，如何优化？"
- "[界面组件]的用户体验不够好，有什么改进方案？"
- "如何让[功能]更符合[平台]的设计规范？"

🔧 **集成对接场景**
- "需要把[功能A]和[功能B]结合，如何设计接口？"
- "在[业务流程]中集成[第三方服务]的最佳实践是什么？"
- "[模块]需要适配[新需求]，改动点在哪里？"

📚 **学习理解场景**
- "这个[业务功能]的实现逻辑是怎样的？"
- "[技术方案]在这个项目中是如何应用的？"
- "为什么[功能]要这样设计，有什么考虑？"

✅ **测试验证场景**
- "[功能]在[边界条件]下是否正常工作？"
- "如何验证[业务逻辑]的正确性？"
- "[用户操作]的各种情况都覆盖到了吗？"

**输出要求：**
- 生成5-6个不同场景的查询
- 每个查询要包含具体的业务背景
- 使用自然、口语化的表达方式
- 避免纯技术术语，要有业务语境
- 体现真实的开发痛点和需求

**严格格式要求：**
只返回JSON数组，格式如下：
[
  {"intent": "implementation", "query": "我需要实现用户权限管理功能，当用户登录时如何验证和分配权限？"},
  {"intent": "debugging", "query": "用户点击支付按钮后页面卡住，可能是支付流程哪里出了问题？"}
]

意图类型：implementation, debugging, optimization, integration, understanding, testing"""

    def _setup_logger(self, log_level: str):
        """设置日志配置"""
        self.logger = logging.getLogger(f"EnhancedQueryGenerator_{id(self)}")
        self.logger.setLevel(getattr(logging, log_level.upper(), logging.INFO))

        # 清理现有的handler
        self.logger.handlers.clear()

        # 创建格式化器
        formatter = logging.Formatter(
            "[%(asctime)s] %(levelname)s - %(funcName)s:%(lineno)d - %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S",
        )

        # 控制台输出
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)

        # 文件输出（可选）
        log_file = Path(".query_generation.log")
        file_handler = logging.FileHandler(log_file, encoding="utf-8")
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)

        self.logger.info(
            f"查询生成器初始化完成 - 模型: {self.model}, 批大小: {self.batch_size}, 最大并发: {self.max_concurrent}"
        )

    def _start_timing(self, operation_name: str) -> float:
        """开始计时操作"""
        start_time = time.time()
        self.logger.debug(f"开始{operation_name}...")
        return start_time

    def _end_timing(
        self, operation_name: str, start_time: float, extra_info: str = ""
    ) -> float:
        """结束计时操作并记录"""
        end_time = time.time()
        duration = end_time - start_time
        log_msg = f"{operation_name}完成 - 耗时: {duration:.3f}s"
        if extra_info:
            log_msg += f" - {extra_info}"
        self.logger.info(log_msg)
        return duration

    def _record_api_call_timing(
        self, operation: str, duration: float, success: bool, token_count: int = 0
    ):
        """记录API调用时间统计"""
        self._timing_stats["llm_api_calls"].append(
            {
                "operation": operation,
                "duration": duration,
                "success": success,
                "token_count": token_count,
                "timestamp": datetime.now().isoformat(),
            }
        )

        status = "成功" if success else "失败"
        self.logger.info(
            f"API调用 [{operation}] {status} - 耗时: {duration:.3f}s, Token数: {token_count}"
        )

    def get_timing_stats(self) -> dict[str, Any]:
        """获取详细的时间统计信息"""
        stats = self._timing_stats.copy()

        # 计算总时间
        if stats["total_start_time"] and stats["total_end_time"]:
            stats["total_duration"] = (
                stats["total_end_time"] - stats["total_start_time"]
            )

        # API调用统计
        api_calls = stats["llm_api_calls"]
        if api_calls:
            successful_calls = [call for call in api_calls if call["success"]]
            stats["api_call_summary"] = {
                "total_calls": len(api_calls),
                "successful_calls": len(successful_calls),
                "failed_calls": len(api_calls) - len(successful_calls),
                "total_api_time": sum(call["duration"] for call in api_calls),
                "average_api_time": sum(call["duration"] for call in api_calls)
                / len(api_calls),
                "total_tokens": sum(call["token_count"] for call in api_calls),
            }

        # 函数处理统计
        func_times = stats["function_processing_times"]
        if func_times:
            stats["function_processing_summary"] = {
                "total_functions": len(func_times),
                "total_processing_time": sum(func_times),
                "average_processing_time": sum(func_times) / len(func_times),
                "min_processing_time": min(func_times),
                "max_processing_time": max(func_times),
            }

        # 缓存统计
        total_cache_operations = stats["cache_hits"] + stats["cache_misses"]
        if total_cache_operations > 0:
            stats["cache_summary"] = {
                "cache_hit_rate": stats["cache_hits"] / total_cache_operations,
                "cache_miss_rate": stats["cache_misses"] / total_cache_operations,
                "total_operations": total_cache_operations,
            }

        return stats

    def load_dataset(self, input_file: str) -> list[dict[str, Any]]:
        """加载数据集"""
        dataset = []
        with open(input_file, "r", encoding="utf-8") as f:
            for line in f:
                if line.strip():
                    dataset.append(json.loads(line.strip()))
        return dataset

    def generate_queries_for_function(
        self,
        function: dict[str, Any],
        file_context: dict[str, Any],
        project_context: ProjectContext,
    ) -> dict[str, Any]:
        """为单个函数生成查询（增强版：包含日志和计时）"""
        func_name = function.get("name", "unknown")
        start_time = self._start_timing(f"函数 {func_name} 查询生成")

        try:
            # 检查缓存
            cache_key = f"{file_context.get('repo', 'unknown')}_{func_name}_{hash(function.get('body', '')[:100])}"
            if cache_key in self._context_cache:
                self.logger.debug(f"命中上下文缓存: {func_name}")
                self._timing_stats["cache_hits"] += 1
                cached_result = self._context_cache[cache_key].copy()
                cached_result["generated_at"] = datetime.now().isoformat()
                duration = self._end_timing(
                    f"函数 {func_name} 查询生成", start_time, "缓存命中"
                )
                self._timing_stats["function_processing_times"].append(duration)
                return cached_result

            self._timing_stats["cache_misses"] += 1
            self.logger.debug(f"缓存未命中，开始生成查询: {func_name}")

            # 提取函数详细上下文
            context_start = self._start_timing("函数上下文提取")
            func_context = self.project_analyzer.extract_function_context(function)
            self._end_timing("函数上下文提取", context_start)

            # 构建增强的上下文信息（包含文档理解）
            enhanced_context_start = self._start_timing("增强上下文构建")
            enhanced_context = self._build_enhanced_context(
                function, file_context, project_context, func_context
            )
            self._end_timing("增强上下文构建", enhanced_context_start)

            # 构建提示词
            messages = [
                {"role": "system", "content": self.system_prompt},
                {
                    "role": "user",
                    "content": f"""请基于以下代码和项目信息，生成真实开发者在实际工作中会搜索的查询：

{enhanced_context}

**查询生成指导：**
请模拟以下真实开发场景：

1. **新手开发者** - 刚加入团队，需要学习业务功能
   例："这个权限检查的功能是怎么工作的？"

2. **产品经理提需求** - 需要实现新的业务功能
   例："需要增加用户个人信息修改功能，可以参考类似的代码吗？"

3. **用户反馈Bug** - 需要快速定位和解决问题
   例："用户点击登录后没有反应，可能是哪里的问题？"

4. **代码审查** - 需要检查代码质量和规范
   例："这个网络请求的处理有没有更好的写法？"

5. **技术选型** - 需要研究技术方案
   例："如何在HarmonyOS应用中集成支付功能？"

6. **接手项目** - 需要快速理解代码逻辑
   例："这个数据缓存的逻辑是怎么设计的？"

**查询要求：**
- 使用自然语言，不要太正式
- 包含具体的业务场景和用户操作
- 体现实际开发中的痛点和需求
- 避免纯技术术语，要有业务背景
- 可以包含不同角色的视角（开发者、测试、产品等）

请严格按照以下格式返回标准JSON数组：
[
  {{"intent": "implementation", "query": "具体的实现查询内容"}},
  {{"intent": "debugging", "query": "具体的调试查询内容"}},
  {{"intent": "optimization", "query": "具体的优化查询内容"}}
]

注意：
- 只返回JSON数组，不要其他文本
- query字段必须是完整的查询句子，不要包含JSON片段
- 生成3-6个查询，意图类型包括：implementation, debugging, optimization, integration, understanding, testing""",
                },
            ]

            # 调用LLM API
            api_start = self._start_timing(f"LLM API调用 - {func_name}")
            try:
                response = self._call_llm_api(messages)
                api_duration = self._end_timing(
                    f"LLM API调用 - {func_name}", api_start, "成功"
                )
                self._record_api_call_timing("query_generation", api_duration, True)

                # 解析响应
                parse_start = self._start_timing("响应解析")
                queries = self._parse_llm_response(response, function, file_context)
                self._end_timing("响应解析", parse_start)

                # 缓存结果
                self._context_cache[cache_key] = queries.copy()
                self._timing_stats["successful_generations"] += 1
                self.logger.info(
                    f"成功生成 {len(queries.get('generated_queries', []))} 个查询: {func_name}"
                )

                duration = self._end_timing(
                    f"函数 {func_name} 查询生成",
                    start_time,
                    f"成功 - {len(queries.get('generated_queries', []))}个查询",
                )
                self._timing_stats["function_processing_times"].append(duration)
                return queries

            except Exception as api_error:
                api_duration = self._end_timing(
                    f"LLM API调用 - {func_name}", api_start, f"失败: {str(api_error)}"
                )
                self._record_api_call_timing("query_generation", api_duration, False)
                raise api_error

        except Exception as e:
            self.logger.warning(f"查询生成失败: {func_name} - {str(e)}，使用备用方案")
            self._timing_stats["fallback_generations"] += 1

            # 备用方案
            fallback_start = self._start_timing("备用查询生成")
            fallback_result = self._generate_enhanced_fallback_queries(
                function, file_context, {}, project_context
            )
            self._end_timing("备用查询生成", fallback_start)

            duration = self._end_timing(
                f"函数 {func_name} 查询生成", start_time, "备用方案"
            )
            self._timing_stats["function_processing_times"].append(duration)
            return fallback_result

    def _extract_business_keywords(self, body: str) -> list[str]:
        """提取业务关键词"""
        keywords = []
        body_lower = body.lower()

        business_terms = {
            "login": "用户登录",
            "register": "用户注册",
            "payment": "支付处理",
            "order": "订单管理",
            "user": "用户管理",
            "profile": "个人资料",
            "setting": "设置配置",
            "notification": "消息通知",
            "camera": "拍照功能",
            "location": "位置服务",
            "share": "分享功能",
            "download": "下载功能",
            "upload": "上传功能",
            "search": "搜索功能",
            "filter": "筛选功能",
            "favorite": "收藏功能",
            "cart": "购物车",
            "comment": "评论功能",
            "rating": "评分功能",
        }

        for eng, chn in business_terms.items():
            if eng in body_lower:
                keywords.append(chn)

        return keywords[:3]  # 最多返回3个关键词

    def _extract_user_actions(self, body: str) -> list[str]:
        """提取用户操作"""
        actions = []
        body_lower = body.lower()

        action_patterns = {
            "click": "点击操作",
            "tap": "点击操作",
            "swipe": "滑动操作",
            "scroll": "滚动操作",
            "input": "输入操作",
            "select": "选择操作",
            "submit": "提交操作",
            "cancel": "取消操作",
            "confirm": "确认操作",
            "refresh": "刷新操作",
            "back": "返回操作",
            "close": "关闭操作",
            "open": "打开操作",
            "save": "保存操作",
            "delete": "删除操作",
            "edit": "编辑操作",
        }

        for pattern, action in action_patterns.items():
            if pattern in body_lower:
                actions.append(action)

        return list(set(actions))[:3]  # 去重并最多返回3个

    def _identify_technical_challenges(self, body: str) -> list[str]:
        """识别技术挑战"""
        challenges = []
        body_lower = body.lower()

        challenge_indicators = {
            "async": "异步处理",
            "await": "异步等待",
            "promise": "异步Promise",
            "try": "异常处理",
            "catch": "错误捕获",
            "timeout": "超时处理",
            "retry": "重试机制",
            "cache": "缓存机制",
            "validate": "数据验证",
            "encrypt": "数据加密",
            "compress": "数据压缩",
            "thread": "多线程",
            "lock": "线程锁",
            "memory": "内存管理",
            "performance": "性能优化",
            "security": "安全控制",
        }

        for indicator, challenge in challenge_indicators.items():
            if indicator in body_lower:
                challenges.append(challenge)

        return list(set(challenges))[:3]  # 去重并最多返回3个

    def _build_enhanced_context(
        self,
        function: dict[str, Any],
        file_context: dict[str, Any],
        project_context: ProjectContext,
        func_context: dict[str, Any],
    ) -> str:
        """构建增强的上下文信息（包含文档理解）"""

        # 提取业务相关信息
        business_keywords = self._extract_business_keywords(function.get("body", ""))
        user_actions = self._extract_user_actions(function.get("body", ""))
        technical_challenges = self._identify_technical_challenges(
            function.get("body", "")
        )

        # 获取项目文档摘要
        doc_summary = self.project_analyzer.get_project_documentation_summary()

        return f"""
**项目背景：**
- 项目名称：{project_context.repo_name}
- 技术栈：ArkTS, HarmonyOS{", " + ", ".join(project_context.technology_stack[:3]) if project_context.technology_stack else ""}
- 架构模式：{", ".join(project_context.architecture_patterns)}
- 主要业务域：{", ".join(project_context.business_domains)}
- 常用UI组件：{", ".join(project_context.ui_components[:5])}

**项目文档信息：**
{doc_summary}

**主要功能特性：**
{", ".join(project_context.main_features[:5]) if project_context.main_features else "无相关信息"}

**函数详情：**
- 函数名：{function.get("name", "未知")}
- 函数类型：{function.get("type", "未知")}
- 文件路径：{function.get("source_file", file_context.get("filepath", "未知"))}
- 代码质量评分：{function.get("final_score", 0):.1f}/100

**业务特征分析：**
- 主要职责：{func_context["main_responsibility"]}
- 业务领域：{func_context["business_domain"]}
- 复杂度等级：{func_context["complexity_level"]}
- 关键业务词：{", ".join(business_keywords) if business_keywords else "通用功能"}
- 用户操作：{", ".join(user_actions) if user_actions else "后台处理"}
- 技术挑战：{", ".join(technical_challenges) if technical_challenges else "标准实现"}

**使用场景：**
{", ".join(func_context["usage_scenarios"])}

**相关UI组件：**
{", ".join(func_context["ui_components"]) if func_context["ui_components"] else "无UI交互"}

**代码片段（核心逻辑）：**
```arkts
{function.get("body", "")[:600]}{"..." if len(function.get("body", "")) > 600 else ""}
```

**开发上下文提示：**
- 这是一个{func_context["complexity_level"]}的{func_context["main_responsibility"]}功能
- 主要服务于{func_context["business_domain"]}场景
- 在{project_context.repo_name}项目中属于{", ".join(project_context.common_patterns) if project_context.common_patterns else "基础功能"}类型
- 开发者可能需要理解、修改、调试、优化或测试此功能
- 考虑实际业务需求和用户体验优化"""

    def _call_llm_api(self, messages: list[dict[str, str]]) -> str:
        """调用LLM API"""
        if not self.api_key:
            raise ValueError("未提供API密钥")

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
        }

        data = {
            "model": self.model,
            "messages": messages,
            "temperature": 0.9,  # 增加创造力和随机性
            "max_tokens": 1500,  # 增加输出长度
            "top_p": 0.95,  # 增加多样性
            "frequency_penalty": 0.3,  # 避免重复
            "presence_penalty": 0.2,  # 鼓励新内容
        }

        response = requests.post(
            f"{self.base_url}/chat/completions", headers=headers, json=data, timeout=45
        )

        response.raise_for_status()
        return response.json()["choices"][0]["message"]["content"]

    def _parse_llm_response(
        self, response: str, function: dict[str, Any], file_context: dict[str, Any]
    ) -> dict[str, Any]:
        """解析LLM响应"""
        try:
            # 尝试解析JSON
            if response.strip().startswith("["):
                queries_list = json.loads(response)
            elif response.strip().startswith("{"):
                data = json.loads(response)
                queries_list = data.get("queries", data.get("generated_queries", []))
            else:
                # 解析文本格式
                queries_list = self._extract_queries_from_text_enhanced(response)

            return {
                "function_id": f"{file_context.get('repo', 'unknown')}_{function.get('name', 'unknown')}",
                "original_function": function,
                "generated_queries": queries_list,
                "generated_at": datetime.now().isoformat(),
                "generation_method": "enhanced_llm",
            }

        except Exception as e:
            print(f"解析响应失败: {e}")
            return self._generate_enhanced_fallback_queries(function, file_context, {})

    def _extract_queries_from_text_enhanced(self, text: str) -> list[dict[str, str]]:
        """从文本中提取查询（增强版）"""
        queries = []

        # 首先尝试直接解析完整的JSON数组
        try:
            # 清理文本，移除多余的换行和空格
            cleaned_text = re.sub(r"\s+", " ", text.strip())

            # 查找JSON数组模式
            json_array_match = re.search(r"\[\s*({.*?})\s*\]", cleaned_text, re.DOTALL)
            if json_array_match:
                json_content = "[" + json_array_match.group(1) + "]"
                # 修复常见的JSON格式问题
                json_content = re.sub(r"},\s*}", "}}", json_content)  # 修复多余的逗号
                json_content = re.sub(
                    r'(?<!\\)"([^"]*?)(?<!\\)"', r'"\1"', json_content
                )  # 确保引号正确

                parsed_queries = json.loads(json_content)
                if isinstance(parsed_queries, list):
                    for q in parsed_queries:
                        if isinstance(q, dict) and "query" in q and "intent" in q:
                            # 清理query字段，移除JSON片段
                            clean_query = self._clean_query_text(q["query"])
                            if clean_query and len(clean_query) > 5:
                                queries.append(
                                    {
                                        "intent": q["intent"],
                                        "query": clean_query,
                                        "type": "enhanced_natural_language",
                                    }
                                )
                if queries:
                    return queries[:8]
        except Exception as e:
            print(f"JSON解析失败: {e}")

        # 逐行解析模式
        lines = text.strip().split("\n")
        current_query = {}

        for line in lines:
            line = line.strip()
            if not line or line.startswith("#") or line.startswith("//"):
                continue

            # 尝试解析单行JSON对象
            if line.startswith("{") and line.endswith("}"):
                try:
                    query_obj = json.loads(line)
                    if "query" in query_obj and "intent" in query_obj:
                        clean_query = self._clean_query_text(query_obj["query"])
                        if clean_query and len(clean_query) > 5:
                            queries.append(
                                {
                                    "intent": query_obj["intent"],
                                    "query": clean_query,
                                    "type": "enhanced_natural_language",
                                }
                            )
                        continue
                except:
                    pass

            # 提取intent和query字段（更严格的模式）
            intent_match = re.search(
                r'["\']intent["\']\s*:\s*["\']([^"\'\'\n]+)["\']', line
            )
            if intent_match and len(intent_match.group(1)) < 50:  # 避免捕获长文本
                current_query["intent"] = intent_match.group(1)

            query_match = re.search(
                r'["\']query["\']\s*:\s*["\']([^"\'\'\n]+)["\']', line
            )
            if query_match:
                clean_query = self._clean_query_text(query_match.group(1))
                if clean_query and len(clean_query) > 5:
                    current_query["query"] = clean_query
                    current_query["type"] = "enhanced_natural_language"

                    if "intent" in current_query:
                        queries.append(current_query.copy())
                        current_query = {}

            # 处理自然语言查询（避免包含JSON语法的行）
            if (
                any(
                    keyword in line.lower()
                    for keyword in [
                        "实现",
                        "如何",
                        "怎么",
                        "查找",
                        "优化",
                        "修复",
                        "集成",
                    ]
                )
                and not any(
                    json_char in line for json_char in ['":', '"', "intent", "query"]
                )
                and len(line) > 10
            ):
                intent = self._infer_intent_from_query(line)
                clean_query = line.strip('- ."')
                if len(clean_query) > 5:
                    queries.append(
                        {
                            "intent": intent,
                            "query": clean_query,
                            "type": "enhanced_natural_language",
                        }
                    )

        return queries[:8]  # 最多返回8个查询

    def _clean_query_text(self, query_text: str) -> str:
        """清理查询文本，移除JSON片段和多余字符"""
        if not query_text:
            return ""

        # 移除常见的JSON片段
        cleaned = re.sub(r'intent["\']?\s*:\s*["\'].*?["\'],?', "", query_text)
        cleaned = re.sub(r'query["\']?\s*:\s*["\']', "", cleaned)
        cleaned = re.sub(r'type["\']?\s*:\s*["\'].*?["\'],?', "", cleaned)

        # 移除多余的引号和标点
        cleaned = re.sub(r'^["\'\']+|["\'\']+$', "", cleaned)
        cleaned = re.sub(r"^[,\s]+|[,\s]+$", "", cleaned)

        # 移除JSON语法字符
        cleaned = re.sub(r'[{}\[\]"\':,]', "", cleaned)

        # 清理多余空格
        cleaned = re.sub(r"\s+", " ", cleaned).strip()

        return cleaned

    def _infer_intent_from_query(self, query: str) -> str:
        """从查询内容推断意图"""
        query_lower = query.lower()

        if any(
            word in query_lower
            for word in ["如何实现", "怎么做", "实现方式", "代码示例"]
        ):
            return "implementation"
        elif any(
            word in query_lower for word in ["错误", "异常", "问题", "修复", "调试"]
        ):
            return "debugging"
        elif any(word in query_lower for word in ["优化", "性能", "改进", "最佳实践"]):
            return "optimization"
        elif any(word in query_lower for word in ["集成", "接口", "对接", "api"]):
            return "integration"
        elif any(word in query_lower for word in ["理解", "原理", "工作方式", "解释"]):
            return "understanding"
        elif any(word in query_lower for word in ["测试", "验证", "用例"]):
            return "testing"
        else:
            return "general"

    def _generate_enhanced_fallback_queries(
        self,
        function: dict[str, Any],
        file_context: dict[str, Any],
        func_context: dict[str, Any],
        project_context: Optional[ProjectContext] = None,
    ) -> dict[str, Any]:
        """生成增强的备用查询（性能优化）"""
        # 性能优化：检查缓存
        name = function.get("name", "未知函数")
        repo = file_context.get("repo", "项目")
        cache_key = f"fallback_{repo}_{name}_{hash(function.get('body', '')[:100])}"

        if cache_key in self._fallback_cache:
            cached_result = self._fallback_cache[cache_key].copy()
            cached_result["generated_at"] = datetime.now().isoformat()
            return cached_result

        # 生成增强的备用查询（包含项目文档信息）
        responsibility = func_context.get("main_responsibility", "功能")
        business = func_context.get("business_domain", "业务")

        # 使用项目文档中的信息增强查询（如果可用）
        project_features = []
        tech_stack = []
        repo_name = repo

        if project_context:
            project_features = (
                project_context.main_features[:2]
                if project_context.main_features
                else []
            )
            tech_stack = (
                project_context.technology_stack[:2]
                if project_context.technology_stack
                else []
            )
            repo_name = project_context.repo_name or repo

        # 提取业务关键词和用户操作（使用缓存优化的方法）
        business_keywords = list(
            self._extract_business_keywords(function.get("body", ""))
        )
        user_actions = list(self._extract_user_actions(function.get("body", "")))

        # 根据文档信息构建更准确的上下文
        business_context = business_keywords[0] if business_keywords else business
        action_context = user_actions[0] if user_actions else "操作"
        feature_context = project_features[0] if project_features else "功能"
        tech_context = tech_stack[0] if tech_stack else "ArkTS"

        queries = [
            {
                "intent": "implementation",
                "query": f"我需要实现{business_context}相关的{feature_context}，用户可以进行{action_context}，有没有类似{name}的{tech_context}代码参考？",
                "type": "enhanced_fallback",
            },
            {
                "intent": "debugging",
                "query": f"用户在使用{repo_name}项目的{business_context}功能时，{action_context}后出现异常，可能是{name}功能哪里的问题？",
                "type": "enhanced_fallback",
            },
            {
                "intent": "optimization",
                "query": f"{business_context}模块中的{name}功能性能不够好，用户{action_context}时卡顿，如何在{tech_context}中优化？",
                "type": "enhanced_fallback",
            },
            {
                "intent": "integration",
                "query": f"在{repo}项目中，如何将{name}的{business_context}功能与{feature_context}模块集成？",
                "type": "enhanced_fallback",
            },
            {
                "intent": "understanding",
                "query": f"这个{repo_name}项目中{business_context}相关的{name}功能是怎么实现的，为什么要这样设计？",
                "type": "enhanced_fallback",
            },
            {
                "intent": "testing",
                "query": f"如何验证{tech_context}中{business_context}相关的{name}功能的正确性，用户{action_context}的各种情况都覆盖到了吗？",
                "type": "enhanced_fallback",
            },
        ]

        result = {
            "function_id": f"{repo}_{name}",
            "original_function": function,
            "generated_queries": queries,
            "generated_at": datetime.now().isoformat(),
            "generation_method": "enhanced_fallback",
            "note": "使用增强备用查询生成",
        }

        # 缓存结果
        self._fallback_cache[cache_key] = result.copy()
        return result

    def clear_caches(self):
        """清理所有缓存（性能优化）"""
        self._context_cache.clear()
        self._fallback_cache.clear()
        print("已清理所有缓存")

    def get_cache_stats(self) -> dict[str, int]:
        """获取缓存统计信息"""
        return {
            "context_cache_size": len(self._context_cache),
            "fallback_cache_size": len(self._fallback_cache),
            "total_cache_entries": len(self._context_cache) + len(self._fallback_cache),
        }

    def enhance_existing_dataset(
        self,
        input_file: str,
        output_file: Optional[str] = None,
        max_functions: Optional[int] = None,
    ) -> dict[str, Any]:
        """增强现有数据集，直接在函数对象上添加查询信息（包含日志和计时）"""
        # 开始总计时
        self._timing_stats["total_start_time"] = time.time()
        self.logger.info(f"开始处理数据集: {input_file}")

        # 准备输出文件路径
        if output_file is None:
            output_file = input_file.replace(".jsonl", "_with_queries.jsonl")

        # 加载和预处理数据集
        dataset = self._load_and_preprocess_dataset(input_file, max_functions)

        # 分析项目上下文（支持多项目）
        project_contexts = self._analyze_project_contexts(dataset)

        # 处理数据集函数
        enhanced_dataset = self._process_dataset_functions(dataset, project_contexts)

        # 保存增强后的数据集
        self._save_enhanced_dataset(enhanced_dataset, output_file)

        # 生成并返回统计报告
        return self._generate_enhancement_stats(
            input_file, output_file, dataset, enhanced_dataset, project_contexts
        )

    def _load_and_preprocess_dataset(
        self, input_file: str, max_functions: Optional[int] = None
    ) -> list[dict[str, Any]]:
        """加载和预处理数据集"""
        load_start = self._start_timing("数据集加载")
        dataset = self.load_dataset(input_file)
        self._end_timing("数据集加载", load_start, f"{len(dataset)}个文件")

        if max_functions:
            dataset = self._limit_dataset_functions(dataset, max_functions)

        return dataset

    def _limit_dataset_functions(
        self, dataset: list[dict[str, Any]], max_functions: int
    ) -> list[dict[str, Any]]:
        """限制数据集中的函数数量（用于测试），使用随机采样"""
        # 统计过滤前的数据分布
        original_stats = self._calculate_dataset_distribution(dataset, "过滤前")

        total_functions = sum(len(item.get("functions", [])) for item in dataset)
        self.logger.info(
            f"原始数据集包含 {total_functions} 个函数，限制为 {max_functions} 个"
        )

        if total_functions <= max_functions:
            self.logger.info("无需过滤，返回原数据集")
            return dataset

        # 收集所有函数及其来源文件信息
        all_functions_with_file = []
        for file_index, item in enumerate(dataset):
            functions = item.get("functions", [])
            for func_index, func in enumerate(functions):
                all_functions_with_file.append(
                    {
                        "file_index": file_index,
                        "func_index": func_index,
                        "function": func,
                        "file_item": item,
                    }
                )

        # 随机选择函数
        import random

        selected_functions = random.sample(all_functions_with_file, max_functions)

        # 按文件重新组织
        file_function_map = {}
        for selected in selected_functions:
            file_index = selected["file_index"]
            if file_index not in file_function_map:
                file_function_map[file_index] = {
                    "item": selected["file_item"].copy(),
                    "functions": [],
                }
            file_function_map[file_index]["functions"].append(selected["function"])

        # 构建新的数据集
        filtered_dataset = []
        for file_index in sorted(file_function_map.keys()):
            file_data = file_function_map[file_index]
            file_data["item"]["functions"] = file_data["functions"]
            filtered_dataset.append(file_data["item"])

        # 统计过滤后的数据分布
        filtered_stats = self._calculate_dataset_distribution(
            filtered_dataset, "过滤后"
        )

        # 输出对比统计
        self._log_distribution_comparison(original_stats, filtered_stats)

        sampled_count = sum(len(item.get("functions", [])) for item in filtered_dataset)
        self.logger.info(f"随机采样后数据集包含 {sampled_count} 个函数")
        return filtered_dataset

    def _calculate_dataset_distribution(
        self, dataset: list[dict[str, Any]], stage: str
    ) -> dict[str, Any]:
        """计算数据集的分布统计"""
        stats = {
            "stage": stage,
            "total_files": len(dataset),
            "total_functions": 0,
            "files_per_repo": {},
            "functions_per_repo": {},
            "functions_per_file": [],
            "file_types": {},
            "avg_functions_per_file": 0,
            "max_functions_per_file": 0,
            "min_functions_per_file": float("inf"),
        }

        for item in dataset:
            functions = item.get("functions", [])
            func_count = len(functions)
            stats["total_functions"] += func_count
            stats["functions_per_file"].append(func_count)

            # 更新最大最小值
            if func_count > 0:
                stats["max_functions_per_file"] = max(
                    stats["max_functions_per_file"], func_count
                )
                stats["min_functions_per_file"] = min(
                    stats["min_functions_per_file"], func_count
                )

            # 项目统计
            repo = item.get("repo", "unknown")
            stats["files_per_repo"][repo] = stats["files_per_repo"].get(repo, 0) + 1
            stats["functions_per_repo"][repo] = (
                stats["functions_per_repo"].get(repo, 0) + func_count
            )

            # 文件类型统计
            filepath = item.get("filepath", "")
            if filepath:
                file_ext = filepath.split(".")[-1] if "." in filepath else "no_ext"
                stats["file_types"][file_ext] = stats["file_types"].get(file_ext, 0) + 1

        # 计算平均值
        if stats["total_files"] > 0:
            stats["avg_functions_per_file"] = (
                stats["total_functions"] / stats["total_files"]
            )

        # 处理空数据集的情况
        if stats["min_functions_per_file"] == float("inf"):
            stats["min_functions_per_file"] = 0

        return stats

    def _log_distribution_comparison(
        self, original_stats: dict[str, Any], filtered_stats: dict[str, Any]
    ) -> None:
        """输出过滤前后的数据分布对比"""
        self.logger.info("\n" + "=" * 60)
        self.logger.info("数据采样统计对比")
        self.logger.info("=" * 60)

        # 基本统计
        self.logger.info(
            f"文件数量: {original_stats['total_files']} -> {filtered_stats['total_files']} (减少 {original_stats['total_files'] - filtered_stats['total_files']})"
        )
        self.logger.info(
            f"函数数量: {original_stats['total_functions']} -> {filtered_stats['total_functions']} (减少 {original_stats['total_functions'] - filtered_stats['total_functions']})"
        )

        # 每个文件的函数统计
        self.logger.info(
            f"平均每文件函数: {original_stats['avg_functions_per_file']:.2f} -> {filtered_stats['avg_functions_per_file']:.2f}"
        )
        self.logger.info(
            f"最大每文件函数: {original_stats['max_functions_per_file']} -> {filtered_stats['max_functions_per_file']}"
        )
        self.logger.info(
            f"最小每文件函数: {original_stats['min_functions_per_file']} -> {filtered_stats['min_functions_per_file']}"
        )

        # 项目分布对比
        self.logger.info("\n项目分布对比:")
        all_repos = set(original_stats["files_per_repo"].keys()) | set(
            filtered_stats["files_per_repo"].keys()
        )
        for repo in sorted(all_repos):
            orig_files = original_stats["files_per_repo"].get(repo, 0)
            filt_files = filtered_stats["files_per_repo"].get(repo, 0)
            orig_funcs = original_stats["functions_per_repo"].get(repo, 0)
            filt_funcs = filtered_stats["functions_per_repo"].get(repo, 0)

            retention_rate = (filt_funcs / orig_funcs * 100) if orig_funcs > 0 else 0
            self.logger.info(
                f"  {repo}: {orig_files}->{filt_files} 文件, {orig_funcs}->{filt_funcs} 函数 (保留率: {retention_rate:.1f}%)"
            )

        # 文件类型分布
        self.logger.info("\n文件类型分布:")
        all_types = set(original_stats["file_types"].keys()) | set(
            filtered_stats["file_types"].keys()
        )
        for file_type in sorted(all_types):
            orig_count = original_stats["file_types"].get(file_type, 0)
            filt_count = filtered_stats["file_types"].get(file_type, 0)
            self.logger.info(f"  .{file_type}: {orig_count} -> {filt_count}")

        self.logger.info("=" * 60)

    def _analyze_project_contexts(
        self, dataset: list[dict[str, Any]]
    ) -> dict[str, "ProjectContext"]:
        """分析多项目上下文，只使用README和缓存信息，不依赖采样数据"""
        project_start = self._start_timing("项目上下文分析")
        self.logger.info("分析多项目上下文...")

        # 按项目分组数据集
        project_groups = self._group_dataset_by_project(dataset)
        project_contexts = {}

        for repo_name, project_data in project_groups.items():
            self.logger.info(f"分析项目: {repo_name} ({len(project_data)} 个文件)")

            # 为每个项目推断项目路径
            repo_path = self._infer_repo_path(project_data)

            # 只使用README和缓存信息生成项目上下文，不依赖采样数据
            project_context = self._create_project_context_from_docs(
                repo_name, repo_path
            )
            project_contexts[repo_name] = project_context

            self.logger.info(
                f"项目 {repo_name} 分析完成："
                f"发现 {len(project_context.business_domains)} 个业务域, "
                f"{len(project_context.common_patterns)} 种常用模式, "
                f"{len(project_context.main_features or [])} 个主要功能"
            )

        self._end_timing("项目上下文分析", project_start)
        self.logger.info(f"总共分析了 {len(project_contexts)} 个项目的上下文")

        return project_contexts

    def _create_project_context_from_docs(
        self, repo_name: str, repo_path: Optional[str]
    ) -> "ProjectContext":
        """只基于README和缓存信息创建项目上下文，不依赖采样数据"""
        # 只使用文档分析的结果
        doc_analysis = {}
        if repo_path:
            self.logger.debug(f"开始分析项目文档: {repo_path}")
            doc_analysis = (
                self.project_analyzer.doc_reader.analyze_project_documentation(
                    repo_path
                )
            )
        else:
            self.logger.warning(f"项目 {repo_name} 无法推断路径，使用默认上下文")

        # 创建基于文档的项目上下文
        context = ProjectContext(
            repo_name=repo_name,
            # 使用基本的ArkTS/HarmonyOS模式，不依赖采样数据
            common_patterns=["ArkUI组件开发", "状态管理", "组件构建"],
            business_domains=doc_analysis.get("business_domains", ["移动应用开发"]),
            ui_components=[
                "Column",
                "Row",
                "Button",
                "Text",
                "Image",
                "List",
                "Navigation",
            ],  # 常见ArkUI组件
            architecture_patterns=["ArkUI", "ArkTS", "HarmonyOS"],
            # 文档分析结果
            project_summary=doc_analysis.get("project_summary", ""),
            main_features=doc_analysis.get("main_features", []),
            technology_stack=doc_analysis.get(
                "technology_stack", ["ArkTS", "HarmonyOS"]
            ),
        )

        self.logger.debug(
            f"项目 {repo_name} 上下文创建完成: "
            f"{len(context.main_features or [])} 个功能, "
            f"{len(context.technology_stack or [])} 个技术栈"
        )

        return context

    def _group_dataset_by_project(
        self, dataset: list[dict[str, Any]]
    ) -> dict[str, list[dict[str, Any]]]:
        """按项目分组数据集"""
        project_groups = {}

        for item in dataset:
            # 使用统一的项目检测方法
            repo_name = self._get_file_project(item)

            if repo_name not in project_groups:
                project_groups[repo_name] = []
            project_groups[repo_name].append(item)

        return project_groups

    def _infer_repo_path(self, dataset: list[dict[str, Any]]) -> Optional[str]:
        """从数据集中推断项目路径"""
        if not dataset:
            return None

        first_item = dataset[0]
        if "filepath" not in first_item:
            return None

        file_path = Path(first_item["filepath"])

        # 如果数据集中有repo字段，尝试基于repo名称和filepath推断项目路径
        if "repo" in first_item and first_item["repo"]:
            repo_name = first_item["repo"]

            # 在文件路径中查找repo名称，定位项目根目录
            path_parts = file_path.parts
            for i, part in enumerate(path_parts):
                if part == repo_name:
                    # 找到匹配的repo名称，构建项目根路径
                    repo_root = Path(*path_parts[: i + 1])
                    if repo_root.exists():
                        return str(repo_root)

        # 回退到原来的方法：向上查找包含.git或README.md的目录
        current = file_path.parent
        while current != current.parent:
            if (current / ".git").exists() or (current / "README.md").exists():
                return str(current)
            current = current.parent

        return None

    def _process_dataset_functions(
        self,
        dataset: list[dict[str, Any]],
        project_contexts: dict[str, "ProjectContext"],
    ) -> list[dict[str, Any]]:
        """处理数据集中的所有函数（支持多项目）"""
        total_functions = sum(len(item.get("functions", [])) for item in dataset)
        processed_functions = 0

        self.logger.info(f"开始为 {total_functions} 个函数生成增强查询...")
        self.logger.info(f"性能优化：使用批处理（批大小: {self.batch_size}）和缓存")
        self.logger.info(
            f"数据集包含 {len(project_contexts)} 个项目：{list(project_contexts.keys())}"
        )

        enhanced_dataset = []

        for file_idx, file_item in enumerate(dataset):
            self.logger.info(f"处理文件 {file_idx + 1}/{len(dataset)}")

            # 确定该文件属于哪个项目
            file_repo = self._get_file_project(file_item)
            project_context = project_contexts.get(file_repo)

            if not project_context:
                # 如果找不到对应的项目上下文，使用默认的或第一个
                self.logger.warning(
                    f"未找到文件 {file_item.get('filepath', 'unknown')} 对应的项目上下文，使用默认上下文"
                )
                project_context = (
                    next(iter(project_contexts.values())) if project_contexts else None
                )

            if project_context:
                enhanced_item = self._process_file_functions(file_item, project_context)
            else:
                self.logger.error(
                    f"无法获取项目上下文，跳过文件: {file_item.get('filepath', 'unknown')}"
                )
                enhanced_item = file_item.copy()  # 直接复制原文件

            enhanced_dataset.append(enhanced_item)

            # 更新处理进度
            file_function_count = len(file_item.get("functions", []))
            processed_functions += file_function_count

            if processed_functions % 5 == 0:  # 更频繁的进度更新
                self.logger.info(
                    f"已处理 {processed_functions}/{total_functions} 个函数"
                )

        return enhanced_dataset

    def _get_file_project(self, file_item: dict[str, Any]) -> str:
        """确定文件属于哪个项目"""
        # 优先使用 repo 字段
        repo_name = file_item.get("repo")
        if repo_name:
            return repo_name

        # 如果没有 repo 字段，从 filepath 推断
        filepath = file_item.get("filepath", "")
        if filepath:
            path_parts = Path(filepath).parts
            # 尝试找到最可能的项目名（反向查找，从文件名往上找）
            skip_dirs = {
                "/",
                "src",
                "main",
                "java",
                "kotlin",
                "swift",
                "ets",
                "pages",
                "components",
                "views",
                "utils",
            }

            # 从后往前查找，跳过文件名和常见目录
            for i in range(len(path_parts) - 2, -1, -1):  # 跳过文件名
                part = path_parts[i]
                if part and not part.startswith(".") and part not in skip_dirs:
                    # 检查是否是项目名（一般包含大写字母或数字）
                    if any(c.isupper() or c.isdigit() for c in part) or len(part) > 3:
                        return part

            # 如果没找到合适的，选择最后一个非常见目录的部分
            for part in reversed(path_parts[:-1]):  # 跳过文件名
                if part and not part.startswith(".") and part not in skip_dirs:
                    return part

        return "unknown"

    def _process_file_functions(
        self, file_item: dict[str, Any], project_context: "ProjectContext"
    ) -> dict[str, Any]:
        """处理单个文件中的所有函数"""
        enhanced_item = file_item.copy()
        enhanced_functions = []
        functions = file_item.get("functions", [])

        # 批处理函数
        for batch_start in range(0, len(functions), self.batch_size):
            batch_end = min(batch_start + self.batch_size, len(functions))
            batch_functions = functions[batch_start:batch_end]

            # 处理当前批次
            for func in batch_functions:
                enhanced_func = self._process_single_function(
                    func, file_item, project_context
                )
                enhanced_functions.append(enhanced_func)

        enhanced_item["functions"] = enhanced_functions
        return enhanced_item

    def _process_single_function(
        self,
        func: dict[str, Any],
        file_item: dict[str, Any],
        project_context: "ProjectContext",
    ) -> dict[str, Any]:
        """处理单个函数"""
        try:
            # 生成查询（使用缓存优化）
            query_data = self.generate_queries_for_function(
                func, file_item, project_context
            )

            # 将查询信息直接添加到函数对象中
            enhanced_func = func.copy()
            enhanced_func["generated_queries"] = query_data.get("generated_queries", [])
            enhanced_func["query_generation_method"] = query_data.get(
                "generation_method", "unknown"
            )
            enhanced_func["query_generated_at"] = query_data.get("generated_at")

            return enhanced_func

        except Exception as e:
            self.logger.error(f"处理函数 {func.get('name', 'unknown')} 失败: {e}")
            # 保留原函数但标记失败
            enhanced_func = func.copy()
            enhanced_func["query_generation_error"] = str(e)
            return enhanced_func

    def _save_enhanced_dataset(
        self, enhanced_dataset: list[dict[str, Any]], output_file: str
    ) -> None:
        """保存增强后的数据集"""
        save_start = self._start_timing("数据集保存")
        output_path = Path(output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)

        with open(output_path, "w", encoding="utf-8") as f:
            for item in enhanced_dataset:
                f.write(json.dumps(item, ensure_ascii=False) + "\n")

        self._end_timing("数据集保存", save_start, f"{len(enhanced_dataset)}个文件")

    def _generate_enhancement_stats(
        self,
        input_file: str,
        output_file: str,
        dataset: list[dict[str, Any]],
        enhanced_dataset: list[dict[str, Any]],
        project_contexts: dict[str, "ProjectContext"],
    ) -> dict[str, Any]:
        """生成增强统计报告（支持多项目）"""
        # 结束总计时
        self._timing_stats["total_end_time"] = time.time()

        # 生成统计报告
        stats_start = self._start_timing("统计报告生成")

        total_functions = sum(len(item.get("functions", [])) for item in dataset)
        total_queries = sum(
            len(func.get("generated_queries", []))
            for item in enhanced_dataset
            for func in item.get("functions", [])
        )
        successful_functions = sum(
            1
            for item in enhanced_dataset
            for func in item.get("functions", [])
            if "generated_queries" in func and func["generated_queries"]
        )

        # 获取详细的时间统计
        timing_stats = self.get_timing_stats()

        # 生成多项目上下文统计
        project_stats = {}
        for repo_name, context in project_contexts.items():
            project_stats[repo_name] = {
                "repo_name": context.repo_name,
                "business_domains": context.business_domains,
                "common_patterns": context.common_patterns,
                "main_features": context.main_features or [],
                "technology_stack": context.technology_stack or [],
            }

        stats = {
            "input_file": input_file,
            "output_file": str(Path(output_file)),
            "total_files": len(dataset),
            "total_functions": total_functions,
            "functions_with_queries": successful_functions,
            "total_queries_generated": total_queries,
            "success_rate": successful_functions / total_functions
            if total_functions > 0
            else 0,
            "avg_queries_per_function": total_queries / successful_functions
            if successful_functions > 0
            else 0,
            "project_contexts": project_stats,  # 多项目上下文
            "total_projects": len(project_contexts),
            "generated_at": datetime.now().isoformat(),
            "performance_stats": timing_stats,
        }

        self._end_timing("统计报告生成", stats_start)
        self._log_enhancement_results(stats, timing_stats)

        return stats

    def _log_enhancement_results(
        self, stats: dict[str, Any], timing_stats: dict[str, Any]
    ) -> None:
        """记录增强结果日志（支持多项目）"""
        self.logger.info("\n=== 查询生成完成 ===")
        self.logger.info(f"处理文件: {stats['total_files']} 个")
        self.logger.info(f"处理函数: {stats['total_functions']} 个")
        self.logger.info(f"成功生成查询的函数: {stats['functions_with_queries']} 个")
        self.logger.info(f"总查询数: {stats['total_queries_generated']} 个")
        self.logger.info(f"成功率: {stats['success_rate']:.1%}")
        self.logger.info(f"平均每函数查询数: {stats['avg_queries_per_function']:.1f}")
        self.logger.info(f"输出文件: {stats['output_file']}")

        # 显示多项目信息
        if "project_contexts" in stats and stats["project_contexts"]:
            self.logger.info(f"\n=== 项目信息 ({stats['total_projects']} 个项目) ===")
            for repo_name, context in stats["project_contexts"].items():
                self.logger.info(f"项目: {repo_name}")
                if context.get("business_domains"):
                    domains = ", ".join(context["business_domains"][:3])  # 显示前3个
                    self.logger.info(f"  业务域: {domains}")
                if context.get("main_features"):
                    features = ", ".join(context["main_features"][:3])  # 显示前3个
                    self.logger.info(f"  主要功能: {features}")

    def _log_performance_stats(self, timing_stats: dict[str, Any]) -> None:
        """记录性能统计日志"""
        if "total_duration" in timing_stats:
            self.logger.info(f"\n=== 性能统计 ===")
            self.logger.info(f"总时间: {timing_stats['total_duration']:.2f}s")

            if "api_call_summary" in timing_stats:
                api_stats = timing_stats["api_call_summary"]
                self.logger.info(
                    f"API调用: {api_stats['total_calls']}次 (成功: {api_stats['successful_calls']}, 失败: {api_stats['failed_calls']})"
                )
                self.logger.info(
                    f"API总时间: {api_stats['total_api_time']:.2f}s (平均: {api_stats['average_api_time']:.2f}s)"
                )

            if "function_processing_summary" in timing_stats:
                func_stats = timing_stats["function_processing_summary"]
                self.logger.info(
                    f"函数处理平均时间: {func_stats['average_processing_time']:.3f}s"
                )
                self.logger.info(
                    f"函数处理时间范围: {func_stats['min_processing_time']:.3f}s - {func_stats['max_processing_time']:.3f}s"
                )

            if "cache_summary" in timing_stats:
                cache_stats = timing_stats["cache_summary"]
                self.logger.info(
                    f"缓存命中率: {cache_stats['cache_hit_rate']:.1%} ({timing_stats['cache_hits']}/{cache_stats['total_operations']})"
                )


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="增强版LLM查询生成器")
    parser.add_argument("--input", "-i", help="输入的数据集文件")
    parser.add_argument("--output", "-o", help="输出文件路径")
    parser.add_argument("--api-key", help="API密钥")
    parser.add_argument(
        "--base-url", default="https://api.deepseek.com", help="API基础URL"
    )
    parser.add_argument("--model", default="deepseek-chat", help="使用的模型")
    parser.add_argument(
        "--max-functions", type=int, help="最大处理的函数数量（用于测试）"
    )
    parser.add_argument(
        "--dry-run", action="store_true", help="模拟运行，不实际调用API"
    )
    # 性能优化选项
    parser.add_argument(
        "--batch-size", type=int, default=5, help="批处理大小（默认: 5）"
    )
    parser.add_argument(
        "--max-concurrent", type=int, default=3, help="最大并发数（默认: 3）"
    )
    # 日志配置选项
    parser.add_argument(
        "--log-level",
        default="INFO",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        help="日志级别（默认: INFO）",
    )

    args = parser.parse_args()

    # 原有的主功能逻辑
    # args.input = "evaluation/benchmark/test/arkts_high_quality_dataset.jsonl"
    # args.max_functions = 300
    if not args.input:
        parser.error("需要指定 --input 参数")

    if args.dry_run:
        print("干运行模式 - 显示配置信息")
        print(f"输入文件: {args.input}")
        print(
            f"输出文件: {args.output or args.input.replace('.jsonl', '_with_queries.jsonl')}"
        )
        print(f"API基础URL: {args.base_url}")
        print(f"模型: {args.model}")
        return

    if not args.api_key and not os.getenv("DEEPSEEK_API_KEY"):
        print("错误: 请提供API密钥或设置DEEPSEEK_API_KEY环境变量")
        return

    # 创建生成器（包含性能优化参数和日志配置）
    generator = EnhancedLLMQueryGenerator(
        api_key=args.api_key,
        base_url=args.base_url,
        model=args.model,
        batch_size=args.batch_size,
        max_concurrent=args.max_concurrent,
        log_level=args.log_level,
    )

    try:
        stats = generator.enhance_existing_dataset(
            args.input, args.output, args.max_functions
        )

        # 保存统计信息（包含详细的时间统计）
        stats_file = (
            Path(
                args.output or args.input.replace(".jsonl", "_with_queries.jsonl")
            ).parent
            / "query_generation_stats.json"
        )
        with open(stats_file, "w", encoding="utf-8") as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)

        generator.logger.info(f"\n统计信息已保存到: {stats_file}")

    except Exception as e:
        print(f"运行失败: {e}")
        return 1

    return 0


if __name__ == "__main__":
    main()
