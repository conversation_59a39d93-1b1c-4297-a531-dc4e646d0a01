"""
ArkTS数据处理Pipeline配置文件
配置整个数据处理流程的参数和路径
"""

from pathlib import Path

# 基础配置
BASE_DIR = Path(__file__).parent
OUTPUT_DIR = BASE_DIR / "pipeline_output"

# 确保目录存在
OUTPUT_DIR.mkdir(exist_ok=True)

# 数据收集配置
DATA_COLLECTION_CONFIG = {
    "repos_dir": "/Users/<USER>/projects/hmos_projects/oh-example-composer",
    "allowed_file_types": {"ets"},
    "exclude_dirs": {".git", "node_modules", "dist", "build", "test", "tests"},
    "max_token_num": 8 * 1024,
    "min_file_lines": 8,
    "max_file_lines": 1000,
    "max_line_length": 220,
    "empty_line_ratio": 0.85
}

# 数据过滤配置
FILTER_CONFIG = {
    "target_size": 200,
    "top_percent": 0.1,
    "random_seed": 42,
    "quality_weights": {
        "complexity": 0.20,
        "diversity": 0.20,
        "quality": 0.20,
        "representativeness": 0.15,
        "completeness": 0.10,
        "unique_id_complexity": 0.15
    }
}

# 函数采样配置
SAMPLING_CONFIG = {
    "target_size": 300,
    "min_per_file": 1,
    "max_per_file": 3,
    "random_seed": 42,
    "quality_weights": {
        "complexity": 0.25,
        "naming": 0.20,
        "quality": 0.25,
        "representative": 0.20,
        "context": 0.10
    }
}

