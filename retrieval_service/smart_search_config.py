"""
智能检索配置
管理多引擎检索的各种参数和阈值
"""

# 结果质量评估配置
QUALITY_THRESHOLDS = {
    # 质量评分阈值
    "min_symbol_quality": 0.6,  # 符号检索最低质量要求
    "min_overall_quality": 0.5,  # 整体结果最低质量要求
    # 结果数量阈值
    "min_results_ratio": 0.5,  # 最少结果数量比例 (相对于top_k)
    "sufficient_results_ratio": 0.8,  # 充足结果数量比例
    # 质量评分权重
    "count_weight": 0.3,  # 结果数量权重
    "diversity_weight": 0.3,  # 结果多样性权重
    "content_weight": 0.4,  # 内容质量权重
}

# 引擎权重配置
ENGINE_WEIGHTS = {
    "symbol": 1.0,  # 符号检索基础权重
    "ripgrep": 0.8,  # ripgrep检索基础权重
    "embedding": 0.6,  # embedding检索基础权重
    # 匹配加成权重
    "keyword_match_bonus": 0.2,  # 关键词匹配加成
    "exact_match_bonus": 0.3,  # 精确匹配加成
}

# 检索策略配置
SEARCH_STRATEGIES = {
    "auto": {
        "enable_entity_detection": True,
        "enable_quality_evaluation": True,
        "enable_smart_fallback": True,
        "max_engines": 3,
    },
    "symbol_first": {
        "symbol_threshold": 0.5,  # 符号检索结果阈值
        "fallback_to_embedding": True,
    },
    "embedding_first": {
        "supplement_with_symbol": True,
        "symbol_supplement_ratio": 0.3,  # 符号补充比例
    },
    "hybrid": {
        "parallel_execution": True,
        "result_fusion": True,
        "max_concurrent_engines": 3,
    },
}

# 实体检测配置
ENTITY_DETECTION = {
    # 符号模式权重
    "pattern_weights": {
        "class": 1.0,  # 类名权重最高
        "function": 0.9,  # 函数名次之
        "constant": 0.8,  # 常量
        "module": 0.7,  # 模块名
        "special": 0.6,  # 特殊符号
    },
    # 实体提取限制
    "max_entities": 8,  # 最大实体数量
    "min_entity_length": 2,  # 最小实体长度
    "max_entities_per_query": 5,  # 每个查询最大实体数
    # 常见词过滤
    "filter_common_words": True,
    "min_meaningful_length": 3,  # 有意义词汇最小长度
}

# 性能配置
PERFORMANCE_CONFIG = {
    # 超时设置
    "search_timeout": 30.0,  # 搜索超时时间(秒)
    "engine_timeout": 10.0,  # 单个引擎超时时间(秒)
    # 并发控制
    "max_concurrent_searches": 5,  # 最大并发搜索数
    "max_results_per_engine": 50,  # 每个引擎最大结果数
    # 缓存配置
    "enable_result_cache": True,
    "cache_ttl": 300,  # 缓存TTL(秒)
    "max_cache_size": 1000,  # 最大缓存条目数
}

# 日志配置
LOGGING_CONFIG = {
    "log_search_details": True,  # 记录搜索详情
    "log_engine_performance": True,  # 记录引擎性能
    "log_quality_scores": False,  # 记录质量评分(调试用)
    "log_entity_detection": False,  # 记录实体检测(调试用)
}

# 统计配置
STATS_CONFIG = {
    "track_engine_usage": True,  # 跟踪引擎使用情况
    "track_quality_metrics": True,  # 跟踪质量指标
    "track_performance_metrics": True,  # 跟踪性能指标
    "stats_window_size": 1000,  # 统计窗口大小
}


def get_strategy_config(strategy: str) -> dict:
    """获取指定策略的配置"""
    return SEARCH_STRATEGIES.get(strategy, SEARCH_STRATEGIES["auto"])


def get_quality_threshold(threshold_name: str) -> float:
    """获取质量阈值"""
    return QUALITY_THRESHOLDS.get(threshold_name, 0.5)


def get_engine_weight(engine_name: str) -> float:
    """获取引擎权重"""
    return ENGINE_WEIGHTS.get(engine_name, 0.5)


def should_log(log_type: str) -> bool:
    """检查是否应该记录特定类型的日志"""
    return LOGGING_CONFIG.get(log_type, False)


# 动态配置更新
class SmartSearchConfig:
    """智能搜索配置管理器"""

    def __init__(self):
        self.quality_thresholds = QUALITY_THRESHOLDS.copy()
        self.engine_weights = ENGINE_WEIGHTS.copy()
        self.search_strategies = SEARCH_STRATEGIES.copy()
        self.entity_detection = ENTITY_DETECTION.copy()
        self.performance_config = PERFORMANCE_CONFIG.copy()

    def update_quality_threshold(self, name: str, value: float):
        """更新质量阈值"""
        if 0.0 <= value <= 1.0:
            self.quality_thresholds[name] = value
        else:
            raise ValueError(f"质量阈值必须在0-1之间: {value}")

    def update_engine_weight(self, engine: str, weight: float):
        """更新引擎权重"""
        if weight >= 0.0:
            self.engine_weights[engine] = weight
        else:
            raise ValueError(f"引擎权重必须非负: {weight}")

    def get_adaptive_thresholds(self, search_history: list) -> dict:
        """根据搜索历史自适应调整阈值"""
        if not search_history:
            return self.quality_thresholds.copy()

        # 简单的自适应逻辑
        recent_searches = search_history[-100:]  # 最近100次搜索

        # 计算平均质量分数
        avg_symbol_quality = sum(
            s.get("symbol_quality", 0) for s in recent_searches
        ) / len(recent_searches)

        # 动态调整阈值
        adaptive_thresholds = self.quality_thresholds.copy()

        if avg_symbol_quality > 0.8:
            # 如果符号检索质量普遍较高，提高阈值
            adaptive_thresholds["min_symbol_quality"] = min(
                0.8, avg_symbol_quality - 0.1
            )
        elif avg_symbol_quality < 0.4:
            # 如果符号检索质量普遍较低，降低阈值
            adaptive_thresholds["min_symbol_quality"] = max(
                0.3, avg_symbol_quality + 0.1
            )

        return adaptive_thresholds


# 全局配置实例
config = SmartSearchConfig()
