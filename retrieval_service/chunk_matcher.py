"""
分片匹配器
将搜索结果匹配到具体的代码分片
"""

from typing import Optional

from utils.interfaces import IChunkMatcher, IRepoIndex
from utils.models import CodeChunk, SearchResult


class ChunkMatcher(IChunkMatcher):
    """分片匹配器实现"""

    def __init__(self):
        self.match_tolerance = 5  # 行号匹配容差

    def match_chunks(
        self, search_results: list[SearchResult], index: IRepoIndex
    ) -> list[CodeChunk]:
        """将搜索结果匹配到代码分片"""
        matched_chunks = []
        seen_chunk_ids = set()

        for result in search_results:
            chunks = self._find_matching_chunks(result, index)

            for chunk in chunks:
                if chunk.id not in seen_chunk_ids:
                    matched_chunks.append(chunk)
                    seen_chunk_ids.add(chunk.id)

        return matched_chunks

    def _find_matching_chunks(
        self, result: SearchResult, index: IRepoIndex
    ) -> list[CodeChunk]:
        """为单个搜索结果找到匹配的分片"""
        # 获取该文件的所有分片
        file_chunks = index.get_chunks_by_file(result.file_path)

        if not file_chunks:
            return []

        matching_chunks = []

        for chunk in file_chunks:
            if self._is_chunk_matching(result, chunk):
                matching_chunks.append(chunk)

        # 如果没有精确匹配，尝试模糊匹配
        if not matching_chunks:
            matching_chunks = self._fuzzy_match_chunks(result, file_chunks)

        return matching_chunks

    def _is_chunk_matching(self, result: SearchResult, chunk: CodeChunk) -> bool:
        """检查搜索结果是否与分片匹配"""
        # 检查行号是否在分片范围内
        if chunk.start_line <= result.line_number <= chunk.end_line:
            return True

        # 检查是否在容差范围内
        if (
            abs(result.line_number - chunk.start_line) <= self.match_tolerance
            or abs(result.line_number - chunk.end_line) <= self.match_tolerance
        ):
            return True

        return False

    def _fuzzy_match_chunks(
        self, result: SearchResult, chunks: list[CodeChunk]
    ) -> list[CodeChunk]:
        """模糊匹配分片"""
        # 计算每个分片与搜索结果的匹配分数
        chunk_scores = []

        for chunk in chunks:
            score = self._calculate_match_score(result, chunk)
            if score > 0:
                chunk_scores.append((chunk, score))

        # 按分数排序
        chunk_scores.sort(key=lambda x: x[1], reverse=True)

        # 返回分数最高的分片（如果分数足够高）
        if chunk_scores and chunk_scores[0][1] > 0.3:
            return [chunk_scores[0][0]]

        return []

    def _calculate_match_score(self, result: SearchResult, chunk: CodeChunk) -> float:
        """计算匹配分数"""
        score = 0.0

        # 1. 行号距离分数（越近分数越高）
        line_distance = min(
            abs(result.line_number - chunk.start_line),
            abs(result.line_number - chunk.end_line),
        )

        if line_distance == 0:
            score += 1.0
        elif line_distance <= self.match_tolerance:
            score += 1.0 - (line_distance / self.match_tolerance) * 0.5

        # 2. 内容匹配分数
        if result.matched_text.strip() in chunk.content:
            score += 0.5

        # 3. 关键词匹配分数
        if chunk.keywords:
            matched_keywords = 0
            result_words = set(result.matched_text.lower().split())

            for keyword in chunk.keywords:
                if keyword.lower() in result_words:
                    matched_keywords += 1

            if matched_keywords > 0:
                score += (matched_keywords / len(chunk.keywords)) * 0.3

        return score

    def match_chunks_with_scores(
        self, search_results: list[SearchResult], index: IRepoIndex
    ) -> list[tuple[CodeChunk, float]]:
        """匹配分片并返回匹配分数"""
        chunk_scores = []
        seen_chunk_ids = set()

        for result in search_results:
            file_chunks = index.get_chunks_by_file(result.file_path)

            for chunk in file_chunks:
                if chunk.id in seen_chunk_ids:
                    continue

                score = self._calculate_match_score(result, chunk)
                if score > 0:
                    chunk_scores.append((chunk, score))
                    seen_chunk_ids.add(chunk.id)

        # 按分数排序
        chunk_scores.sort(key=lambda x: x[1], reverse=True)

        return chunk_scores

    def get_best_matching_chunk(
        self, result: SearchResult, index: IRepoIndex
    ) -> Optional[CodeChunk]:
        """获取最佳匹配的分片"""
        file_chunks = index.get_chunks_by_file(result.file_path)

        if not file_chunks:
            return None

        best_chunk = None
        best_score = 0.0

        for chunk in file_chunks:
            score = self._calculate_match_score(result, chunk)
            if score > best_score:
                best_score = score
                best_chunk = chunk

        # 只返回分数足够高的匹配
        if best_score > 0.3:
            return best_chunk

        return None

    def group_results_by_file(
        self, search_results: list[SearchResult]
    ) -> dict[str, list[SearchResult]]:
        """按文件分组搜索结果"""
        file_groups = {}

        for result in search_results:
            if result.file_path not in file_groups:
                file_groups[result.file_path] = []
            file_groups[result.file_path].append(result)

        return file_groups

    def merge_overlapping_chunks(self, chunks: list[CodeChunk]) -> list[CodeChunk]:
        """合并重叠的分片"""
        if not chunks:
            return []

        # 按文件和起始行排序
        sorted_chunks = sorted(chunks, key=lambda c: (c.file_path, c.start_line))

        merged_chunks = []
        current_chunk = sorted_chunks[0]

        for next_chunk in sorted_chunks[1:]:
            # 如果是同一个文件且有重叠
            if (
                current_chunk.file_path == next_chunk.file_path
                and current_chunk.end_line >= next_chunk.start_line - 1
            ):
                # 合并分片（选择范围更大的）
                if next_chunk.end_line > current_chunk.end_line:
                    current_chunk = next_chunk
            else:
                merged_chunks.append(current_chunk)
                current_chunk = next_chunk

        merged_chunks.append(current_chunk)

        return merged_chunks

    def filter_chunks_by_relevance(
        self,
        chunks: list[CodeChunk],
        search_results: list[SearchResult],
        min_score: float = 0.3,
    ) -> list[CodeChunk]:
        """根据相关性过滤分片"""
        relevant_chunks = []

        for chunk in chunks:
            max_score = 0.0

            for result in search_results:
                if result.file_path == chunk.relative_path:
                    score = self._calculate_match_score(result, chunk)
                    max_score = max(max_score, score)

            if max_score >= min_score:
                relevant_chunks.append(chunk)

        return relevant_chunks

    def get_match_statistics(
        self, search_results: list[SearchResult], matched_chunks: list[CodeChunk]
    ) -> dict:
        """获取匹配统计信息"""
        return {
            "total_search_results": len(search_results),
            "matched_chunks": len(matched_chunks),
            "match_rate": len(matched_chunks) / len(search_results)
            if search_results
            else 0,
            "unique_files_in_results": len({r.file_path for r in search_results}),
            "unique_files_in_chunks": len({c.relative_path for c in matched_chunks}),
            "avg_chunks_per_file": len(matched_chunks)
            / len({c.relative_path for c in matched_chunks})
            if matched_chunks
            else 0,
        }
