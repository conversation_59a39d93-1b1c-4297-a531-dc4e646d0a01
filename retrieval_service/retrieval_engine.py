"""
检索引擎
集成完整的检索流程，包括代码分片检索和符号检索
"""

import logging
import time

from config import ModelType, model_manager
from index_manager.repo_index import RepoIndex
from retrieval_service.chunk_matcher import ChunkMatcher
from retrieval_service.embedding_retriever import EmbeddingRetriever
from retrieval_service.llm_filter import LLMFilter
from retrieval_service.query_rewriter import QueryRewriter
from retrieval_service.result_processor import ResultProcessor
from retrieval_service.ripgrep_executor import RipgrepExecutor
from symbol_processor.symbol_retriever import SymbolRetriever
from utils.models import RetrievalResult, SymbolQuery


logger = logging.getLogger(__name__)


class RetrievalEngine:
    """检索引擎 - 集成代码分片检索和符号检索"""

    def __init__(self, repo_index: RepoIndex, repo_path: str, agent_mode: bool = False):
        self.repo_index = repo_index
        self.repo_path = repo_path
        self.agent_mode = agent_mode

        # 初始化检索组件
        self.query_rewriter = QueryRewriter()
        self.ripgrep_executor = RipgrepExecutor()
        self.chunk_matcher = ChunkMatcher()
        self.embedding_retriever = EmbeddingRetriever()
        self.llm_filter = LLMFilter()
        self.result_processor = ResultProcessor()
        
        # 初始化Agent查询处理器
        self.agent_query_processor = None
        if self.agent_mode:
            try:
                from retrieval_service.agent_query_processor import AgentQueryProcessor
                self.agent_query_processor = AgentQueryProcessor(self, repo_path)
                logger.info("Agent查询处理器已初始化")
            except Exception as e:
                logger.warning(f"Agent查询处理器初始化失败: {e}")
                self.agent_mode = False

        # 初始化符号检索组件
        self.symbol_retriever = None
        if hasattr(repo_index, "symbol_index") and repo_index.symbol_index is not None:
            try:
                self.symbol_retriever = SymbolRetriever(repo_index.symbol_index)
                logger.info("符号检索组件已初始化")
            except Exception as e:
                logger.warning(f"符号检索组件初始化失败: {e}")
        else:
            logger.info("符号索引不可用，符号检索功能将被禁用")

        # 执行统计
        self.search_stats = {
            "total_searches": 0,
            "avg_response_time": 0.0,
            "symbol_search_rate": 0.0,
            "embedding_fallback_rate": 0.0,
            "engine_usage": {
                "symbol_only": 0,
                "symbol_ripgrep": 0,
                "symbol_embedding": 0,
                "ripgrep_only": 0,
                "embedding_only": 0,
                "all_engines": 0,
            },
        }

    async def search(
        self,
        query: str,
        top_k: int = 10,
        use_llm_filter: bool = True,
        enable_symbol_search: bool = True,
    ) -> list[RetrievalResult]:
        """智能多引擎检索流程"""
        start_time = time.time()
        search_context = {
            "symbol_used": False,
            "ripgrep_used": False,
            "embedding_used": False,
            "fallback_reason": None,
        }

        enable_symbol_search = False # TODO: 暂时禁用符号检索

        try:
            # 如果启用了Agent模式，优先使用Agent处理
            if self.agent_mode and self.agent_query_processor:
                logger.info(f"🤖 使用Agent模式处理查询: {query}")
                try:
                    return await self.agent_query_processor.process_query(query, top_k * 2)
                except Exception as e:
                    logger.warning(f"Agent模式处理失败，降级到传统模式: {e}")
                    # 继续执行传统检索流程
            
            logger.info(f"开始传统检索: {query}")
            logger.debug(f"🔍 检索参数: top_k={top_k}, use_llm_filter={use_llm_filter}, enable_symbol_search={enable_symbol_search}")

            # 阶段1: 实体检测和符号检索
            symbol_results = []
            has_entities = False

            if enable_symbol_search and self.symbol_retriever:
                entities = self._extract_symbol_entities(query)
                has_entities = len(entities) > 0
                logger.debug(f"🎯 实体检测结果: 发现 {len(entities)} 个符号实体: {entities}")

                if has_entities:
                    logger.info(f"检测到符号实体: {entities}")
                    symbol_results = await self._search_symbols(query, top_k)
                    search_context["symbol_used"] = True
                    logger.info(f"符号检索: {len(symbol_results)} 个结果")
                    details = [
                        f"{getattr(r, 'file_path', 'unknown')}:{getattr(r, 'start_line', '?')}"
                        for r in symbol_results[:3]
                    ]
                    logger.debug("📋 符号检索结果详情: %s", details)
                else:
                    logger.debug("🚫 未检测到符号实体，跳过符号检索")
            else:
                if not enable_symbol_search:
                    logger.debug("⚠️  符号检索被禁用")
                else:
                    logger.debug("⚠️  符号检索器不可用")

            # 阶段2: 评估符号检索结果质量
            symbol_quality_score = self._evaluate_results_quality(symbol_results)
            logger.debug(f"📊 符号检索质量评分: {symbol_quality_score:.3f}")

            # 阶段3: 决策是否需要ripgrep兜底
            ripgrep_results = []
            need_ripgrep = has_entities and (
                len(symbol_results) < top_k or symbol_quality_score < 0.6
            )
            logger.debug(f"🤔 Ripgrep决策分析: has_entities={has_entities}, symbol_count={len(symbol_results)}, quality={symbol_quality_score:.3f}, threshold={top_k//2}, need_ripgrep={need_ripgrep}")

            if need_ripgrep:
                logger.info("符号检索结果不足，启用ripgrep兜底")
                ripgrep_results = await self._search_ripgrep(query, top_k * 2)
                search_context["ripgrep_used"] = True
                search_context["fallback_reason"] = "low_symbol_quality"
                logger.info(f"Ripgrep检索: {len(ripgrep_results)} 个结果")
                ripgrep_details = [
                    f"{getattr(r, 'file_path', 'unknown')}:{getattr(r, 'start_line', '?')}"
                    for r in ripgrep_results[:3]
                ]
                logger.debug("📋 Ripgrep检索结果详情: %s", ripgrep_details)
            else:
                logger.debug("✅ 符号检索结果充足，跳过Ripgrep")

            # 阶段4: 合并当前结果并评估
            current_results = symbol_results + ripgrep_results
            current_results = self._deduplicate_chunks(current_results)
            current_quality = self._evaluate_results_quality(current_results)
            logger.debug(f"🔄 阶段4结果合并: symbol({len(symbol_results)}) + ripgrep({len(ripgrep_results)}) = {len(current_results)} (去重后), 质量评分: {current_quality:.3f}")

            # 阶段5: 决策是否需要embedding检索
            embedding_results = []
            need_embedding = (
                len(current_results) < top_k
                or current_quality < 0.5
                or not has_entities  # 无实体查询直接用embedding
            )
            logger.debug(f"🤔 Embedding决策分析: current_count={len(current_results)}, quality={current_quality:.3f}, has_entities={has_entities}, threshold={top_k//2}, need_embedding={need_embedding}")

            if need_embedding:
                reason = "no_entities" if not has_entities else "insufficient_results"
                logger.info(f"启用embedding检索 (原因: {reason})")
                embedding_results = await self._search_embeddings(query, top_k * 2)
                search_context["embedding_used"] = True
                if not search_context["fallback_reason"]:
                    search_context["fallback_reason"] = reason
                logger.info(f"Embedding检索: {len(embedding_results)} 个结果")
                embedding_details = [
                    f"{getattr(r[0], 'file_path', 'unknown')}:{getattr(r[0], 'start_line', '?')}"
                    for r in embedding_results[:3]
                ]
                logger.debug("📋 Embedding检索结果详情: %s", embedding_details)
            else:
                logger.debug("✅ 当前结果充足，跳过Embedding检索")

            # 阶段6: 结果融合和去重
            all_results = self._merge_and_rank_results(
                symbol_results, ripgrep_results, embedding_results, query
            )
            logger.debug(f"🔀 阶段6结果融合: 融合后得到 {len(all_results)} 个结果")
            if all_results:
                logger.debug(f"📊 融合结果score分布: {[f'{score:.3f}' for _, score in all_results[:5]]}")

            # 阶段7: LLM筛选
            if use_llm_filter: # and len(all_results) > top_k:
                logger.debug(f"🤖 启用LLM筛选: {len(all_results)}, {top_k}")
                filtered_results = await self.llm_filter.filter_chunks(
                    query, [chunk for chunk, _ in all_results]
                )
                chunk_scores = filtered_results
                search_context["llm_filter_used"] = True
                logger.debug(f"🎯 LLM筛选完成: {len(chunk_scores)} 个结果")
            else:
                logger.debug(f"⏭️  跳过LLM筛选: 结果数量 {len(all_results)} <= {top_k}")
                chunk_scores = all_results

            # 阶段8: 最终结果处理
            logger.debug(f"🎁 开始最终结果处理: {len(chunk_scores)} -> top {top_k}")
            final_results = self._process_final_results(chunk_scores, top_k)

            # 更新统计
            total_time = (time.time() - start_time) * 1000
            self._update_search_stats(total_time, search_context)

            logger.info(
                f"智能检索完成: 返回 {len(final_results)} 个结果，"
                f"使用引擎: {self._get_engines_used(search_context)}，"
                f"总耗时 {total_time:.2f}ms"
            )
            logger.debug(f"🎯 最终结果详情: {[f'{r.file_path}:{r.start_line}' for r in final_results[:5]]}")
            logger.debug(f"📊 检索流程统计: 符号({len(symbol_results)}) + Ripgrep({len(ripgrep_results)}) + Embedding({len(embedding_results)}) -> 融合({len(all_results)}) -> 筛选({len(chunk_scores)}) -> 最终({len(final_results)})")
            
            return final_results

        except Exception as e:
            logger.exception(f"智能检索过程异常: {e}")
            return []

    async def _search_symbols(self, query: str, top_k: int) -> list:
        """符号检索"""
        if not self.symbol_retriever:
            return []

        try:
            # 检测查询中的符号实体
            symbol_entities = self._extract_symbol_entities(query)
            logger.debug(f"符号实体: {symbol_entities}")
            if not symbol_entities:
                return []

            symbol_chunks = []
            for entity in symbol_entities:
                symbol_query = SymbolQuery(
                    query=entity, max_results=top_k, exact_match=False
                )

                symbol_results = await self.symbol_retriever.search_symbols(
                    symbol_query
                )

                # 将符号结果转换为代码分片
                for result in symbol_results:
                    chunk = self._symbol_to_chunk(result.symbol, result.score)
                    if chunk:
                        symbol_chunks.append(chunk)

            return symbol_chunks[:top_k]

        except Exception as e:
            logger.warning(f"符号检索失败: {e}")
            return []

    async def _search_embeddings(self, query: str, top_k: int) -> list:
        """嵌入检索"""
        try:
            embedding_query = self.query_rewriter.rewrite_to_embedding_query(query)
            logger.debug(f"🔄 Query重写(Embedding): '{query}' -> '{embedding_query}'")
            
            embedding_results = await self.embedding_retriever.search_by_query(
                embedding_query, self.repo_index, top_k
            )
            logger.debug(f"📊 Embedding检索详情: 查询='{embedding_query}', 结果数={len(embedding_results)}")
            
            # 直接返回 (chunk, similarity) 对，保留原始相似度
            return embedding_results
        except Exception as e:
            logger.warning(f"嵌入检索失败: {e}")
            return []

    async def _search_ripgrep(self, query: str, top_k: int) -> list:
        """Ripgrep文本检索"""
        try:
            # 重写查询为ripgrep模式
            patterns = await self.query_rewriter.rewrite_to_ripgrep(
                query,
                "",
                [],  # 可以传入repo insight和关键词
            )
            logger.debug(f"🔄 Query重写(Ripgrep): '{query}' -> {len(patterns)} 个模式: {patterns}")

            all_chunks = []
            logger.debug(f"📝 Ripgrep搜索模式: {patterns}")
            for i, pattern in enumerate(patterns[:3]):  # 执行最多3个模式，覆盖简单关键字
                try:
                    logger.debug(f"🔍 执行第{i+1}个ripgrep搜索，模式: '{pattern}'")
                    # 执行ripgrep搜索
                    ripgrep_results = self.ripgrep_executor.execute_search(
                        [pattern], self.repo_path
                    )
                    logger.debug(f"📊 第{i+1}个模式搜索结果: {len(ripgrep_results)} 个匹配")

                    # 将ripgrep结果转换为chunks
                    converted_count = 0
                    for result in ripgrep_results:
                        chunk = self._ripgrep_result_to_chunk(result)
                        if chunk:
                            all_chunks.append(chunk)
                            converted_count += 1
                    
                    logger.debug(f"✅ 第{i+1}个模式成功转换: {converted_count}/{len(ripgrep_results)} 个结果")

                except Exception as e:
                    logger.debug(f"❌ Ripgrep模式 '{pattern}' 搜索失败: {e}")
                    continue

            logger.debug(f"🎯 Ripgrep检索汇总: 总共得到 {len(all_chunks)} 个chunks，返回前 {min(len(all_chunks), top_k)} 个")
            return all_chunks[:top_k]

        except Exception as e:
            logger.warning(f"Ripgrep检索失败: {e}")
            return []

    def _ripgrep_result_to_chunk(self, ripgrep_result):
        """将ripgrep结果转换为代码分片"""
        try:
            # 根据ripgrep结果的文件路径和行号，从repo_index获取对应的chunk
            file_path = ripgrep_result.file_path
            line_number = ripgrep_result.line_number

            logger.debug(f"处理ripgrep结果: {file_path}:{line_number}")

            # 确保使用相对路径
            if file_path.startswith(self.repo_path):
                relative_path = file_path[len(self.repo_path) :].lstrip("/")
                logger.debug(f"转换为相对路径: {file_path} -> {relative_path}")
                file_path = relative_path

            # 获取文件的所有chunks
            chunks = self.repo_index.get_chunks_by_file(file_path)

            # 找到包含该行号的chunk
            for chunk in chunks:
                if chunk.start_line <= line_number <= chunk.end_line:
                    return chunk

            # 如果没找到匹配的chunk，记录调试信息
            logger.debug(f"未找到匹配的chunk: {file_path}:{line_number}")
            logger.debug(f"文件共有 {len(chunks)} 个chunks")
            if chunks:
                logger.debug(
                    f"文件chunks范围: {[(c.start_line, c.end_line) for c in chunks[:3]]}"
                )

            return None

            return None

        except Exception as e:
            logger.debug(f"Ripgrep结果转换失败: {e}")
            return None

    def _detect_language(self, file_path: str) -> str:
        """根据文件扩展名检测编程语言"""
        ext_map = {
            ".py": "python",
            ".js": "javascript",
            ".ts": "typescript",
            ".java": "java",
            ".cpp": "cpp",
            ".c": "c",
            ".go": "go",
            ".rs": "rust",
            ".php": "php",
            ".rb": "ruby",
            ".swift": "swift",
            ".kt": "kotlin",
            ".scala": "scala",
            ".sh": "bash",
            ".sql": "sql",
            ".html": "html",
            ".css": "css",
            ".json": "json",
            ".xml": "xml",
            ".yaml": "yaml",
            ".yml": "yaml",
            ".md": "markdown",
            ".txt": "text",
        }

        import os

        _, ext = os.path.splitext(file_path.lower())
        return ext_map.get(ext, "unknown")

    def _evaluate_results_quality(self, results: list) -> float:
        """评估检索结果质量"""
        if not results:
            return 0.0

        # 基础质量指标
        quality_score = 0.0

        # 1. 结果数量评分 (0-0.3)
        result_count = len(results)
        if result_count >= 5:
            quality_score += 0.3
        elif result_count >= 3:
            quality_score += 0.2
        elif result_count >= 1:
            quality_score += 0.1

        # 2. 结果多样性评分 (0-0.3)
        unique_files = set()
        for result in results:
            if hasattr(result, "file_path"):
                unique_files.add(result.file_path)

        file_diversity = len(unique_files) / max(len(results), 1)
        quality_score += file_diversity * 0.3

        # 3. 内容质量评分 (0-0.4)
        content_scores = []
        for result in results:
            content = getattr(result, "content", "")
            if content:
                # 简单的内容质量指标
                content_score = min(1.0, len(content) / 500)  # 内容长度
                if any(
                    keyword in content.lower()
                    for keyword in ["def ", "class ", "function", "method"]
                ):
                    content_score += 0.2  # 包含代码结构
                content_scores.append(content_score)

        if content_scores:
            avg_content_score = sum(content_scores) / len(content_scores)
            quality_score += avg_content_score * 0.4

        return min(1.0, quality_score)

    def _merge_and_rank_results(
        self,
        symbol_results: list,
        ripgrep_results: list,
        embedding_results: list,
        query: str,
    ) -> list:
        """融合和排序多个引擎的结果"""
        merged_results = []

        # 为不同来源的结果分配权重
        source_weights = {
            "symbol": 1.0,  # 符号检索权重最高
            "ripgrep": 0.8,  # ripgrep次之
            "embedding": 0.6,  # embedding权重较低
        }
        logger.debug(f"📊 结果融合权重配置: {source_weights}")

        # 添加符号检索结果
        symbol_scores = []
        for result in symbol_results:
            score = source_weights["symbol"]
            # 如果结果包含查询中的关键词，提升权重
            if self._result_matches_query(result, query):
                score += 0.2
                logger.debug(f"🔝 符号结果匹配加分: {getattr(result, 'file_path', 'unknown')}:{getattr(result, 'start_line', '?')} score={score:.3f}")
            symbol_scores.append(score)
            merged_results.append((result, score))

        # 添加ripgrep结果
        ripgrep_scores = []
        for result in ripgrep_results:
            score = source_weights["ripgrep"]
            if self._result_matches_query(result, query):
                score += 0.2
                logger.debug(f"🔝 Ripgrep结果匹配加分: {getattr(result, 'file_path', 'unknown')}:{getattr(result, 'start_line', '?')} score={score:.3f}")
            ripgrep_scores.append(score)
            merged_results.append((result, score))

        # 添加embedding结果（使用原始相似度分数）
        embedding_scores = []
        for item in embedding_results:
            # 兼容旧格式（仅chunk）与新格式（chunk, similarity）
            if isinstance(item, tuple) and len(item) == 2:
                chunk, similarity = item
                score = float(similarity)
                # 轻微的关键词匹配加成，但不超过 1.0
                if self._result_matches_query(chunk, query):
                    score = min(1.0, score + 0.05)
                    logger.debug(
                        f"🔝 Embedding结果匹配加分: {getattr(chunk, 'file_path', 'unknown')}:{getattr(chunk, 'start_line', '?')} score={score:.3f}"
                    )
                embedding_scores.append(score)
                merged_results.append((chunk, score))
            else:
                # 只有chunk时退化为固定权重（向后兼容）
                chunk = item
                score = source_weights["embedding"]
                if self._result_matches_query(chunk, query):
                    score += 0.1
                    logger.debug(
                        f"🔝 Embedding结果匹配加分: {getattr(chunk, 'file_path', 'unknown')}:{getattr(chunk, 'start_line', '?')} score={score:.3f}"
                    )
                embedding_scores.append(score)
                merged_results.append((chunk, score))

        logger.debug(f"📈 分数统计: Symbol平均={sum(symbol_scores)/max(1, len(symbol_scores)):.3f}, Ripgrep平均={sum(ripgrep_scores)/max(1, len(ripgrep_scores)):.3f}, Embedding平均={sum(embedding_scores)/max(1, len(embedding_scores)):.3f}")

        # 去重（基于文件路径和行号）
        unique_results = self._deduplicate_scored_results(merged_results)
        logger.debug(f"🔄 去重处理: {len(merged_results)} -> {len(unique_results)} 个结果")

        # 按分数排序
        unique_results.sort(key=lambda x: x[1], reverse=True)
        if unique_results:
            logger.debug(f"🏆 Top5结果分数: {[f'{score:.3f}' for _, score in unique_results[:5]]}")

        return unique_results

    def _result_matches_query(self, result, query: str) -> bool:
        """检查结果是否匹配查询关键词"""
        if not hasattr(result, "content") or not result.content:
            return False

        content_lower = result.content.lower()
        # query_lower = query.lower()

        # 提取查询中的关键词
        import re

        keywords = re.findall(r"\b[a-zA-Z_][a-zA-Z0-9_]*\b", query)
        chinese_words = re.findall(r"[\u4e00-\u9fff]+", query)

        all_keywords = keywords + chinese_words

        # 检查是否包含关键词
        matches = 0
        for keyword in all_keywords:
            if keyword.lower() in content_lower:
                matches += 1

        return matches > 0

    def _deduplicate_scored_results(self, scored_results: list) -> list:
        """去重评分结果，保留最高分的"""
        result_map = {}

        for result, score in scored_results:
            # 生成唯一标识
            key = self._generate_result_key(result)

            # 保留分数更高的结果
            if key not in result_map or result_map[key][1] < score:
                result_map[key] = (result, score)

        return list(result_map.values())

    def _generate_result_key(self, result) -> str:
        """为结果生成唯一标识"""
        if hasattr(result, "id"):
            return str(result.id)
        elif hasattr(result, "file_path") and hasattr(result, "start_line"):
            return f"{result.file_path}:{result.start_line}"
        else:
            return str(hash(str(result)))

    def _get_engines_used(self, search_context: dict) -> str:
        """获取使用的检索引擎列表"""
        engines = []
        if search_context.get("symbol_used"):
            engines.append("symbol")
        if search_context.get("ripgrep_used"):
            engines.append("ripgrep")
        if search_context.get("embedding_used"):
            engines.append("embedding")
        if search_context.get("llm_filter_used"):
            engines.append("llm_filter")
        return "+".join(engines) if engines else "none"

    def _extract_symbol_entities(self, query: str) -> list[str]:
        """从查询中提取可能的符号实体"""
        import re

        # 预编译的正则表达式模式
        if not hasattr(self, "_symbol_patterns"):
            self._symbol_patterns = {
                # 类名: 大写字母开头，至少2个字符
                "class": re.compile(r"\b[A-Z][a-zA-Z0-9_]{1,}\b"),
                # 函数/方法名: 小写字母开头，包含下划线或驼峰，至少2个字符
                "function": re.compile(r"\b[a-z][a-zA-Z0-9_]{1,}\b"),
                # 常量: 全大写，包含下划线
                "constant": re.compile(r"\b[A-Z][A-Z0-9_]{1,}\b"),
                # 模块/包名: 小写，可能包含点号
                "module": re.compile(r"\b[a-z][a-z0-9_.]{1,}\b"),
                # 特殊符号: 以特殊字符开头的标识符
                "special": re.compile(r"\b[_$][a-zA-Z0-9_]+\b"),
            }

            # 扩展的常见词汇过滤列表
            self._common_words = {
                # 英文常见词
                "the",
                "and",
                "or",
                "in",
                "on",
                "at",
                "to",
                "for",
                "of",
                "with",
                "by",
                "is",
                "are",
                "was",
                "were",
                "be",
                "been",
                "have",
                "has",
                "had",
                "do",
                "does",
                "did",
                "will",
                "would",
                "could",
                "should",
                "may",
                "might",
                "can",
                "this",
                "that",
                "these",
                "those",
                "a",
                "an",
                "as",
                "if",
                "when",
                "where",
                "why",
                "how",
                "what",
                "which",
                "who",
                "whom",
                # 编程相关常见词
                "get",
                "set",
                "add",
                "remove",
                "delete",
                "update",
                "create",
                "new",
                "old",
                "first",
                "last",
                "next",
                "prev",
                "current",
                "default",
                "main",
                "test",
                "debug",
                "info",
                "error",
                "warning",
                "log",
                "data",
                "value",
                "key",
                "name",
                "type",
                "size",
                "length",
                "count",
                "index",
                "item",
                "list",
                "array",
                "object",
                "string",
                "number",
                "boolean",
                "true",
                "false",
                "null",
                "undefined",
                "void",
                # 中文常见词（拼音）
                "de",
                "shi",
                "zai",
                "you",
                "he",
                "ta",
                "wo",
                "ni",
                "men",
                "le",
                "ma",
                "ba",
                "ne",
                "ya",
                "la",
            }

        entities = []

        # 按优先级提取不同类型的符号
        for pattern_type, pattern in self._symbol_patterns.items():
            matches = pattern.findall(query)
            for match in matches:
                # 过滤长度和常见词
                if (
                    len(match) >= 2
                    and match.lower() not in self._common_words
                    and not match.isdigit()
                ):
                    entities.append((match, pattern_type))

        # 去重，保持顺序和类型信息
        seen = set()
        unique_entities = []
        for entity, entity_type in entities:
            if entity not in seen:
                seen.add(entity)
                unique_entities.append((entity, entity_type))

        # 按类型优先级排序：类名 > 函数名 > 常量 > 模块 > 特殊符号
        type_priority = {
            "class": 0,
            "function": 1,
            "constant": 2,
            "module": 3,
            "special": 4,
        }
        unique_entities.sort(key=lambda x: (type_priority.get(x[1], 5), x[0]))

        # 返回实体名称，动态限制数量（根据查询长度调整）
        max_entities = min(8, max(3, len(query.split()) // 2))
        result = [entity for entity, _ in unique_entities[:max_entities]]

        return result

    def _symbol_to_chunk(self, symbol, score: float):
        """将符号转换为代码分片"""
        try:
            # 将符号的绝对路径转换为相对路径
            import os

            if os.path.isabs(symbol.file_path):
                relative_path = os.path.relpath(symbol.file_path, self.repo_path)
            else:
                relative_path = symbol.file_path

            # 从repo_index中获取对应的代码分片
            chunks = self.repo_index.get_chunks_by_file(relative_path)

            # 找到包含该符号的分片
            for chunk in chunks:
                if (
                    chunk.start_line <= symbol.start_line <= chunk.end_line
                    and chunk.symbol_name == symbol.name
                ):
                    return chunk

            # 如果没有找到匹配的分片，尝试按行号范围匹配
            for chunk in chunks:
                if chunk.start_line <= symbol.start_line <= chunk.end_line:
                    return chunk

            # 如果仍然没有找到匹配的分片，返回None
            logger.debug(
                f"未找到符号 {symbol.name} 对应的分片，文件: {relative_path}, 行号: {symbol.start_line}"
            )
            return None

        except Exception as e:
            logger.warning(f"符号转换分片失败: {e}")
            return None

    def _deduplicate_chunks(self, chunks: list) -> list:
        """去重代码分片"""
        seen = set()
        unique_chunks = []

        for chunk in chunks:
            chunk_id = getattr(chunk, "id", f"{chunk.file_path}:{chunk.start_line}")
            if chunk_id not in seen:
                seen.add(chunk_id)
                unique_chunks.append(chunk)

        return unique_chunks

    def _process_final_results(
        self, chunk_scores: list, top_k: int
    ) -> list[RetrievalResult]:
        """处理最终结果"""
        # 排序和去重
        original_count = len(chunk_scores)
        sorted_results = self.result_processor.sort_and_deduplicate(chunk_scores)
        dedup_count = len(sorted_results)
        removed_by_dedup = original_count - dedup_count
        if removed_by_dedup > 0:
            logger.info(
                f"🔄 排序去重: {original_count} -> {dedup_count} (移除重复 {removed_by_dedup})"
            )

        # 多样化结果（每文件最多3个）
        diversified_results = self.result_processor.diversify_by_file(
            sorted_results, max_per_file=3
        )
        diversified_count = len(diversified_results)
        removed_by_diversify = dedup_count - diversified_count
        if removed_by_diversify > 0:
            logger.info(
                f"📁 文件多样化限制: {dedup_count} -> {diversified_count} (每文件≤3, 移除 {removed_by_diversify})"
            )

        # 格式化输出并按top_k截断
        formatted_results = self.result_processor.format_results(
            diversified_results, top_k
        )
        if diversified_count > top_k:
            logger.info(
                f"🎯 结果截断: {diversified_count} -> {len(formatted_results)} (top_k={top_k})"
            )

        return formatted_results

    async def smart_search(
        self, query: str, top_k: int = 10, strategy: str = "auto"
    ) -> list[RetrievalResult]:
        """
        智能搜索接口 - 根据查询类型自动选择最佳检索策略

        Args:
            query: 搜索查询
            top_k: 返回结果数量
            strategy: 检索策略 ("auto", "symbol_first", "embedding_first", "hybrid")
        """
        if strategy == "auto":
            # 自动策略：使用新的智能检索流程
            return await self.search(
                query, top_k, use_llm_filter=True, enable_symbol_search=True
            )

        elif strategy == "symbol_first":
            # 符号优先策略
            return await self._symbol_first_search(query, top_k)

        elif strategy == "embedding_first":
            # 嵌入优先策略
            return await self._embedding_first_search(query, top_k)

        elif strategy == "hybrid":
            # 混合策略：并行执行所有检索引擎
            return await self._hybrid_search(query, top_k)

        else:
            raise ValueError(f"不支持的检索策略: {strategy}")

    async def _symbol_first_search(
        self, query: str, top_k: int
    ) -> list[RetrievalResult]:
        """符号优先检索策略"""
        # 1. 尝试符号检索
        if self.symbol_retriever:
            symbol_results = await self._search_symbols(query, top_k)
            if len(symbol_results) >= top_k // 2:
                # 符号检索结果足够，直接返回
                chunk_scores = [(chunk, 0.9) for chunk in symbol_results]
                return self._process_final_results(chunk_scores, top_k)

        # 2. 符号检索不足，补充embedding检索
        embedding_results = await self._search_embeddings(query, top_k)

        # 合并分数：符号固定高分，embedding使用相似度，去重保留更高分
        score_map = {}
        for chunk in symbol_results:
            score_map[getattr(chunk, 'id', f"{chunk.file_path}:{chunk.start_line}")] = (
                chunk,
                0.9,
            )
        for item in embedding_results:
            if isinstance(item, tuple) and len(item) == 2:
                chunk, sim = item
                key = getattr(chunk, 'id', f"{chunk.file_path}:{chunk.start_line}")
                prev = score_map.get(key)
                if prev is None or sim > prev[1]:
                    score_map[key] = (chunk, float(sim))
            else:
                chunk = item
                key = getattr(chunk, 'id', f"{chunk.file_path}:{chunk.start_line}")
                score_map.setdefault(key, (chunk, 0.8))

        chunk_scores = list(score_map.values())
        return self._process_final_results(chunk_scores, top_k)

    async def _embedding_first_search(
        self, query: str, top_k: int
    ) -> list[RetrievalResult]:
        """嵌入优先检索策略"""
        # 直接使用embedding检索
        embedding_results = await self._search_embeddings(query, top_k * 2)

        # 可选：如果结果不足，补充符号检索
        score_map = {}
        for item in embedding_results:
            if isinstance(item, tuple) and len(item) == 2:
                chunk, sim = item
                score_map[getattr(chunk, 'id', f"{chunk.file_path}:{chunk.start_line}")] = (
                    chunk,
                    float(sim),
                )
            else:
                chunk = item
                score_map[getattr(chunk, 'id', f"{chunk.file_path}:{chunk.start_line}")] = (
                    chunk,
                    0.8,
                )

        if len(score_map) < top_k and self.symbol_retriever:
            symbol_results = await self._search_symbols(query, top_k // 2)
            for chunk in symbol_results:
                key = getattr(chunk, 'id', f"{chunk.file_path}:{chunk.start_line}")
                score_map.setdefault(key, (chunk, 0.85))

        chunk_scores = list(score_map.values())
        return self._process_final_results(chunk_scores, top_k)

    async def _hybrid_search(self, query: str, top_k: int) -> list[RetrievalResult]:
        """混合检索策略 - 并行执行所有引擎"""
        import asyncio

        # 并行执行所有检索引擎
        tasks = []

        if self.symbol_retriever:
            tasks.append(self._search_symbols(query, top_k))

        tasks.extend(
            [self._search_ripgrep(query, top_k), self._search_embeddings(query, top_k)]
        )

        # 等待所有任务完成
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 注意：各检索结果类型不同，此处不再合并为纯chunk列表，后续走融合流程

        # 使用融合排序
        if len(results) >= 3:
            symbol_results = results[0] if isinstance(results[0], list) else []
            ripgrep_results = results[1] if isinstance(results[1], list) else []
            embedding_results = results[2] if isinstance(results[2], list) else []

            scored_results = self._merge_and_rank_results(
                symbol_results, ripgrep_results, embedding_results, query
            )
        else:
            # 退化处理：简单拼接后赋默认分
            flat_chunks = []
            for res in results:
                if isinstance(res, list):
                    # 展开 (chunk, score) 或 chunk
                    for item in res:
                        if isinstance(item, tuple) and len(item) == 2:
                            flat_chunks.append(item[0])
                        else:
                            flat_chunks.append(item)
            unique_chunks = self._deduplicate_chunks(flat_chunks)
            scored_results = [(chunk, 0.8) for chunk in unique_chunks]

        return self._process_final_results(scored_results, top_k)

    async def search_with_fallback(
        self, query: str, top_k: int = 10
    ) -> list[RetrievalResult]:
        """带降级策略的检索（保持向后兼容）"""
        try:
            # 使用新的智能搜索
            results = await self.smart_search(query, top_k, strategy="auto")
            if results:
                return results

            logger.warning("智能搜索无结果，尝试降级策略...")

            # 降级策略1: 嵌入优先
            results = await self.smart_search(query, top_k, strategy="embedding_first")
            if results:
                return results

            # 降级策略2: 纯嵌入检索
            embedding_results = await self.embedding_retriever.search_by_query(
                query, self.repo_index, top_k
            )
            if embedding_results:
                return self.result_processor.format_results(embedding_results, top_k)

            return []

        except Exception as e:
            logger.error(f"降级检索也失败: {e}")
            return []

    def get_index_stats(self) -> dict:
        """获取索引统计信息"""
        return self.repo_index.get_stats()

    def get_detailed_stats(self) -> dict:
        """获取详细统计信息"""
        index_stats = self.repo_index.get_stats()

        return {
            "index_stats": index_stats,
            "search_stats": self.search_stats,
            "component_status": {
                "symbol_retriever": "available"
                if self.symbol_retriever
                else "unavailable",
                "embedding_calculator": "available",
                "llm_filter": "available",
            },
        }

    async def refresh_index(self) -> dict:
        """刷新索引 - 增量更新"""
        from index_manager.incremental_updater import IncrementalUpdater

        updater = IncrementalUpdater(self.repo_index)
        update_stats = await updater.update_index(self.repo_path)

        return {
            "status": "completed",
            "message": "索引刷新完成",
            "update_stats": update_stats,
        }

    async def reindex(self) -> dict:
        """重新索引 - 完全重建"""
        return {
            "status": "not_implemented",
            "message": "完全重建索引功能需要在主程序中实现",
        }

    def _update_search_stats(self, response_time: float, search_context: dict):
        """更新搜索统计"""
        self.search_stats["total_searches"] += 1

        # 更新平均响应时间
        total_searches = self.search_stats["total_searches"]
        current_avg = self.search_stats["avg_response_time"]
        self.search_stats["avg_response_time"] = (
            current_avg * (total_searches - 1) + response_time
        ) / total_searches

        # 更新符号搜索使用率
        if search_context.get("symbol_used"):
            current_symbol_count = self.search_stats["symbol_search_rate"] * (
                total_searches - 1
            )
            self.search_stats["symbol_search_rate"] = (
                current_symbol_count + 1
            ) / total_searches

        # 更新embedding降级使用率
        if search_context.get("embedding_used"):
            current_fallback_count = self.search_stats["embedding_fallback_rate"] * (
                total_searches - 1
            )
            self.search_stats["embedding_fallback_rate"] = (
                current_fallback_count + 1
            ) / total_searches

        # 确保engine_usage统计存在
        if "engine_usage" not in self.search_stats:
            self.search_stats["engine_usage"] = {
                "symbol_only": 0,
                "symbol_ripgrep": 0,
                "symbol_embedding": 0,
                "ripgrep_only": 0,
                "embedding_only": 0,
                "all_engines": 0,
            }

        # 统计引擎组合使用情况
        engines_used = []
        if search_context.get("symbol_used"):
            engines_used.append("symbol")
        if search_context.get("ripgrep_used"):
            engines_used.append("ripgrep")
        if search_context.get("embedding_used"):
            engines_used.append("embedding")

        engine_combo = "_".join(sorted(engines_used)) if engines_used else "none"
        if engine_combo == "symbol":
            self.search_stats["engine_usage"]["symbol_only"] += 1
        elif engine_combo == "ripgrep_symbol":
            self.search_stats["engine_usage"]["symbol_ripgrep"] += 1
        elif engine_combo == "embedding_symbol":
            self.search_stats["engine_usage"]["symbol_embedding"] += 1
        elif engine_combo == "ripgrep":
            self.search_stats["engine_usage"]["ripgrep_only"] += 1
        elif engine_combo == "embedding":
            self.search_stats["engine_usage"]["embedding_only"] += 1
        elif len(engines_used) == 3:
            self.search_stats["engine_usage"]["all_engines"] += 1

    async def test_components(self) -> dict:
        """测试各个组件的可用性"""
        test_results = {}

        # 测试符号检索
        try:
            if self.symbol_retriever:
                symbol_query = SymbolQuery(query="test", max_results=1)
                await self.symbol_retriever.search_symbols(symbol_query)
                test_results["symbol_retriever"] = "ok"
            else:
                test_results["symbol_retriever"] = "unavailable"
        except Exception as e:
            test_results["symbol_retriever"] = f"error: {e}"

        # 测试嵌入检索
        try:
            await self.embedding_retriever.search_by_query("test", self.repo_index, 1)
            test_results["embedding_retriever"] = "ok"
        except Exception as e:
            test_results["embedding_retriever"] = f"error: {e}"

        # 测试LLM筛选
        try:
            # 创建一个测试分片
            test_chunk = type(
                "TestChunk",
                (),
                {"id": "test", "content": "test content", "file_path": "test.py"},
            )()
            await self.llm_filter.filter_chunks("test", [test_chunk])
            test_results["llm_filter"] = "ok"
        except Exception as e:
            test_results["llm_filter"] = f"error: {e}"

        return test_results
