"""
智能查询处理器
通过LLM分析用户查询并制定检索策略
"""

import logging
import json
import asyncio
import time
import traceback
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

from config import model_manager, ModelType, ignore_config
from utils.models import RetrievalResult
from retrieval_service.ripgrep_executor import RipgrepExecutor
from utils.helpers import get_file_language
from utils.path_filter import PathFilter, create_common_path_filters

logger = logging.getLogger(__name__)


@dataclass
class SearchTool:
    """检索工具定义"""
    name: str
    description: str
    parameters: Dict[str, Any]
    

@dataclass
class SearchPlan:
    """检索计划"""
    reasoning: str  # LLM的分析推理
    tools: List[Dict[str, Any]]  # 要使用的工具和参数
    priority: str  # 优先级: high/medium/low
    path_scope: Dict[str, Any]  # 路径范围信息


class AgentQueryProcessor:
    """智能查询处理器"""
    
    def __init__(self, retrieval_engine, repo_path: str):
        self.retrieval_engine = retrieval_engine
        self.repo_path = repo_path
        self.ripgrep_executor = RipgrepExecutor()
        
        # 定义可用的检索工具
        self.available_tools = [
            SearchTool(
                name="embedding_search",
                description="基于语义相似度的嵌入检索，适合概念性查询和功能描述",
                parameters={
                    "query": "搜索查询文本",
                    "top_k": "返回结果数量",
                    "language_filter": "编程语言过滤（可选）",
                    "include_patterns": "包含路径模式列表（可选）",
                    "exclude_patterns": "排除路径模式列表（可选）"
                }
            ),
            SearchTool(
                name="ripgrep_search", 
                description="基于关键字的文本检索，适合精确的符号名称、字符串匹配",
                parameters={
                    "patterns": "搜索模式列表",
                    "file_pattern": "文件类型过滤（可选）",
                    "case_sensitive": "是否大小写敏感",
                    "include_patterns": "包含路径模式列表（可选）", 
                    "exclude_patterns": "排除路径模式列表（可选）"
                }
            ),
            SearchTool(
                name="symbol_search",
                description="符号检索，适合查找函数、类、变量等代码符号",
                parameters={
                    "symbol_name": "符号名称",
                    "symbol_types": "符号类型过滤（function/class/variable等）",
                    "exact_match": "是否精确匹配",
                    "include_patterns": "包含路径模式列表（可选）",
                    "exclude_patterns": "排除路径模式列表（可选）"
                }
            ),
            SearchTool(
                name="file_path_search",
                description="基于文件路径的检索，适合查找特定文件或目录",
                parameters={
                    "path_pattern": "路径模式",
                    "recursive": "是否递归搜索",
                    "include_patterns": "包含路径模式列表（可选）",
                    "exclude_patterns": "排除路径模式列表（可选）"
                }
            ),
            SearchTool(
                name="keyword_search",
                description="基于关键词的检索，适合查找包含特定关键词的代码片段",
                parameters={
                    "keywords": "关键词列表",
                    "match_all": "是否需要匹配所有关键词",
                    "include_patterns": "包含路径模式列表（可选）",
                    "exclude_patterns": "排除路径模式列表（可选）"
                }
            )
        ]
        
        # 常用路径筛选器预设
        self.common_filters = create_common_path_filters()
    
    def _get_relative_path(self, file_path: str) -> str:
        """将绝对路径转换为相对路径"""
        rel_path = file_path
        if rel_path.startswith(self.repo_path + '/'):
            rel_path = rel_path[len(self.repo_path + '/'):]
        elif rel_path.startswith(self.repo_path):
            rel_path = rel_path[len(self.repo_path):].lstrip('/')
        return rel_path
    
    async def process_query(self, user_query: str, top_k: int = 10) -> List[RetrievalResult]:
        """处理用户查询，返回检索结果"""
        try:
            logger.info(f"🤖 Agent模式处理查询: {user_query}")
            
            # 1. 获取项目代码库洞察
            codebase_insight = await self._get_codebase_insight()
            
            # 2. 让LLM制定检索计划
            search_plan = await self._create_search_plan(user_query, codebase_insight)
            
            # 3. 执行检索计划
            all_results = await self._execute_search_plan(search_plan, top_k, user_query)
            
            # 4. 聚合和去重结果
            final_results = await self._aggregate_results(all_results, user_query, top_k)
            
            # 容错机制：如果路径筛选过严导致结果为0，尝试放宽筛选条件
            original_include = search_plan.path_scope.get('include_patterns', [])
            original_exclude = search_plan.path_scope.get('exclude_patterns', [])
            
            if len(final_results) == 0 and (original_include or original_exclude):
                logger.warning("🔄 路径筛选过严，结果为0，尝试放宽筛选条件...")
                logger.info(f"📁 原始筛选 - include: {original_include}, exclude: {original_exclude}")
                
                # 创建更宽松的路径筛选器
                relaxed_filter = PathFilter(
                    include_patterns=[],  # 移除include限制，允许全库搜索
                    exclude_patterns=original_exclude  # 保留exclude模式，仍排除无关文件
                )
                
                logger.info(f"📁 放宽后筛选 - include: [], exclude: {original_exclude}")
                
                # 重新执行检索任务（只对embedding_search，因为它最可靠）
                try:
                    relaxed_results = await self._execute_embedding_search(
                        {"query": user_query, "top_k": top_k}, 
                        "embedding_search_relaxed", 
                        relaxed_filter
                    )
                    
                    if relaxed_results:
                        logger.info(f"✅ 放宽筛选策略成功，找到 {len(relaxed_results)} 个候选结果")
                        # 使用放宽后的结果
                        relaxed_all_results = {"embedding_search_relaxed": relaxed_results}
                        final_results = await self._aggregate_results(relaxed_all_results, user_query, top_k)
                        logger.info(f"📊 最终筛选聚合后: {len(final_results)} 个结果")
                    else:
                        logger.warning("⚠️ 即使放宽筛选条件，仍未找到结果")
                        
                except Exception as e:
                    logger.warning(f"❌ 放宽筛选失败: {e}")
            
            logger.info(f"🎯 Agent模式检索完成: 找到 {len(final_results)} 个结果")
            return final_results
            
        except Exception as e:
            logger.exception(f"Agent查询处理失败: {e}")
            # 降级到传统检索
            logger.info("🔄 降级到传统检索模式")
            return await self.retrieval_engine.search(user_query, top_k)
    
    async def _get_codebase_insight(self) -> str:
        """获取代码库洞察信息"""
        try:
            if hasattr(self.retrieval_engine, 'repo_index') and self.retrieval_engine.repo_index:
                stats = self.retrieval_engine.repo_index.get_stats()
                
                # 获取文件类型分布
                file_types = {}
                for file_path in self.retrieval_engine.repo_index.file_chunks.keys():
                    lang = get_file_language(file_path)
                    if lang:  # get_file_language returns None for unknown types
                        file_types[lang] = file_types.get(lang, 0) + 1
                
                # 获取主要目录结构（前10个）
                directories = set()
                for file_path in list(self.retrieval_engine.repo_index.file_chunks.keys())[:50]:
                    parts = file_path.split('/')
                    if len(parts) > 1:
                        directories.add(parts[0])
                
                # 分析目录层次结构
                directory_structure = {}
                for file_path in list(self.retrieval_engine.repo_index.file_chunks.keys())[:100]:
                    parts = file_path.split('/')
                    if len(parts) > 1:
                        top_dir = parts[0]
                        if top_dir not in directory_structure:
                            directory_structure[top_dir] = set()
                        if len(parts) > 2:
                            directory_structure[top_dir].add(parts[1])
                
                # 构建目录描述
                dir_descriptions = []
                for top_dir, sub_dirs in directory_structure.items():
                    if sub_dirs:
                        sub_list = list(sub_dirs)[:3]
                        desc = f"{top_dir}/ (包含: {', '.join(sub_list)}{'...' if len(sub_dirs) > 3 else ''})"
                    else:
                        desc = f"{top_dir}/"
                    dir_descriptions.append(desc)
                
                # 检测关键配置文件
                config_files = []
                all_files = list(self.retrieval_engine.repo_index.file_chunks.keys())
                for file_path in all_files:
                    if any(name in file_path.lower() for name in ['config', 'setting', 'scheduler']):
                        config_files.append(file_path)
                
                insight = f"""项目结构分析:
- 总文件数: {stats.get('total_files', 0)}，总代码块数: {stats.get('total_chunks', 0)}
- 主要编程语言: {', '.join(list(file_types.keys())[:5])}
- 项目根路径: {self.repo_path}

目录结构概览:
{chr(10).join([f"  {desc}" for desc in dir_descriptions[:12]])}

关键配置文件:
{chr(10).join([f"  {f}" for f in config_files[:8]]) if config_files else "  无明显配置文件"}

核心模块推测:
- index_manager/: 索引管理相关功能（包含scheduler.py定时任务调度）
- retrieval_service/: 检索服务核心逻辑  
- codebase_processor/: 代码库处理和解析
- web_service/: Web API服务层
- symbol_processor/: 符号处理和索引
- embedding_processor/: 向量嵌入计算
- utils/: 通用工具和辅助函数
- config.py: 项目核心配置文件（如存在）
- main.py, agent.py: 主程序入口文件"""
                
                return insight
            else:
                return f"项目路径: {self.repo_path}"
                
        except Exception as e:
            logger.warning(f"获取代码库洞察失败: {e}")
            return f"项目路径: {self.repo_path}"
    
    async def _create_search_plan(self, user_query: str, codebase_insight: str) -> SearchPlan:
        """让LLM制定检索计划"""
        
        tools_desc = "\n".join([
            f"- {tool.name}: {tool.description}\n  参数: {tool.parameters}"
            for tool in self.available_tools
        ])
        
        # 添加常用筛选器说明
        filter_presets = "\n".join([
            f"- {name}: include={filter_obj.include_patterns}, exclude={filter_obj.exclude_patterns}"
            for name, filter_obj in self.common_filters.items()
        ])
        
        # 使用.format()避免f-string中的大括号冲突
        json_template = '''{{
    "reasoning": "查询类型分析+为什么选择这些工具",
    "tools": [{{"tool_name": "工具", "parameters": {{"精确参数": "值"}}, "reason": "选择理由"}}],
    "path_scope": {{
        "include_patterns": ["config.py", "相关目录/**/*.py"],
        "exclude_patterns": ["tests/**", "**/__pycache__/**"],
        "strategy": "moderate"
    }},
    "priority": "high"
}}'''
        
        prompt = """你是专业的代码检索专家，需要精确分析用户查询意图。

代码库: {}...

用户查询: "{}"

查询类型识别指导:
1. **模型/架构查询** (关键词: 大模型、LLM、几种、多少、API、模型名称)
   → 重点: config.py的ModelManager和model_name配置
2. **配置查询** (关键词: 配置、设置、修改、参数)
   → 重点: config.py和相关配置文件  
3. **类/函数查询** (关键词: 类、函数、方法、实现)
   → 使用: symbol_search精确定位
4. **功能查询** (关键词: 流程、逻辑、如何工作)
   → 使用: ripgrep + keyword_search组合
5. **概念语义查询** (关键词: 原理、机制、设计模式、算法、优化)
   → 使用: embedding_search语义检索
6. **探索性查询** (模糊描述、自然语言、业务逻辑描述)
   → 结合: embedding_search + keyword_search

工具选择策略 ⚠️重要规则⚠️:
- **embedding_search**: 必须包含！语义相似度检索，适合所有类型查询的语义理解，参数用"query": "自然语言描述"
- ripgrep_search: 精确文本匹配，适合已知符号名称，参数用"patterns": ["模式1", "模式2"]  
- symbol_search: 符号检索，适合查找具体的类/函数，参数用"symbol_name": "确切名称"
- keyword_search: 关键词组合，适合多个相关概念，参数用"keywords": ["词1", "词2"]
- 路径包含: 必须包含最相关的核心文件 (如config.py)

🔥强制要求🔥: 每个查询都必须包含embedding_search工具，即使是精确查询也要用embedding补充语义理解

当前查询分析要点:
- 如果问"几种"、"多少"、"哪些"→ 这是数量统计查询，必须搜索config.py中的具体模型名称
- 如果问"大模型"、"LLM"→ 这是模型查询，搜索"doubao"+"Qwen"具体模型名
- 如果问"调用了"、"使用了"→ 这是使用情况查询，重点是ModelManager配置
- 如果问"修改"、"配置"、"设置"→ 这是配置修改查询，必须找到包含实际配置参数值的代码片段
- 抽象问题需要具体化：将"几种大模型"转换为搜索具体模型名称
- ⚠️重点⚠️：配置查询要找包含具体配置值的代码，不是类定义

关键策略：抽象查询必须转换为具体的技术搜索词

查询示例处理 - 🔥注意：每个查询都必须包含embedding_search🔥:

**精确查询** (仍需embedding补充语义):
"该工具调用了几种不同的大模型？" → ripgrep_search: {{"patterns": ["doubao", "Qwen", "model_name"]}} + embedding_search: {{"query": "大模型配置 模型调用"}}
"AgentQueryProcessor类" → symbol_search: {{"symbol_name": "AgentQueryProcessor", "symbol_types": ["class"]}} + embedding_search: {{"query": "Agent查询处理器 代理查询"}}

**配置修改查询** (优先embedding理解意图):
"我想修改当前大模型配置" → embedding_search: {{"query": "修改大模型配置 doubao模型名称 Qwen配置参数 model_name设置"}} + ripgrep_search: {{"patterns": ["model_name", "doubao", "Qwen", "ModelManager"]}} 
"如何修改模型参数？" → embedding_search: {{"query": "模型参数配置 API密钥 base_url模型设置"}} + keyword_search: {{"keywords": ["配置", "参数", "设置"]}} 

**语义/概念查询**:
"代码检索的工作原理是什么？" → embedding_search: {{"query": "代码检索 工作原理 语义搜索"}} + keyword_search: {{"keywords": ["检索", "原理", "搜索"]}}
"如何优化检索性能？" → embedding_search: {{"query": "检索性能优化 索引 缓存"}} + keyword_search: {{"keywords": ["性能", "优化", "索引"]}}

**强制规则**: 无论查询类型，都必须包含embedding_search作为语义理解基础

直接返回JSON:
{}""".format(codebase_insight[:400], user_query, json_template)

        try:
            response = await model_manager.chat_completion(
                prompt, 
                ModelType.FLASH,
                max_tokens=4096,
                temperature=0.1
            )
            
            # 增强响应检查和日志
            logger.debug(f"LLM响应原始内容: {repr(response)}")
            
            # 检查响应有效性
            if not response:
                logger.warning("LLM返回空响应，可能是网络或服务端问题")
                raise ValueError("LLM返回空响应")
            
            if len(response.strip()) < 10:  # 响应太短也不正常
                logger.warning(f"LLM响应异常短: {repr(response)}")
                raise ValueError(f"LLM响应异常短: {response}")
            
            plan_data = self._parse_llm_json_response(response)
            
            return SearchPlan(
                reasoning=plan_data.get("reasoning", ""),
                tools=plan_data.get("tools", []),
                priority=plan_data.get("priority", "medium"),
                path_scope=plan_data.get("path_scope", {})
            )
            
        except Exception as e:
            # 详细记录异常信息，便于诊断
            error_msg = str(e)
            exception_type = type(e).__name__
            logger.warning(f"LLM生成检索计划失败 [{exception_type}]: {error_msg}")
            
            # 对于不同类型的异常给出更详细的诊断信息
            if "空响应" in error_msg:
                logger.warning("诊断：LLM服务可能暂时不可用，网络连接问题或API限制")
            elif "JSON" in error_msg:
                logger.warning("诊断：LLM返回格式异常，可能是prompt过于复杂或模型处理异常")
            else:
                logger.warning(f"诊断：未知异常类型，详细信息: {traceback.format_exc()}")
            
            logger.info("启动智能fallback机制...")
            # 返回默认计划 - 基于基础分析的智能fallback
            # 简单分析查询中的关键词，确定可能的相关目录
            query_lower = user_query.lower()
            likely_dirs = []
            
            # 基于查询内容推测相关目录
            if any(word in query_lower for word in ['索引', 'index', '增量', 'incremental']):
                likely_dirs.extend(["index_manager/**/*.py", "main.py", "agent.py"])
            if any(word in query_lower for word in ['检索', 'retrieval', '搜索', 'search']):
                likely_dirs.extend(["retrieval_service/**/*.py"])
            if any(word in query_lower for word in ['解析', 'parse', '代码库', 'codebase']):
                likely_dirs.extend(["codebase_processor/**/*.py"])
            if any(word in query_lower for word in ['api', '接口', 'web', '服务']):
                likely_dirs.extend(["web_service/**/*.py"])
            if any(word in query_lower for word in ['符号', 'symbol']):
                likely_dirs.extend(["symbol_processor/**/*.py"])
            if any(word in query_lower for word in ['配置', 'config', '设置', 'setting']):
                likely_dirs.extend(["config.py", "**/*config*.py", "**/*.json", "**/*.yaml"])
            if any(word in query_lower for word in ['修改', 'modify', '触发', 'trigger', '频率', 'frequency', '间隔', 'interval', '定时', 'schedule']):
                likely_dirs.extend(["config.py", "index_manager/scheduler.py", "main.py", "**/*config*.py"])
            if any(word in query_lower for word in ['调度', 'scheduler', '扫描', 'scan']):
                likely_dirs.extend(["index_manager/scheduler.py", "config.py", "main.py"])
            if any(word in query_lower for word in ['大模型', 'llm', 'model', '模型', '几种', '多少', 'api调用', 'modelmanager']):
                likely_dirs.extend(["config.py", "**/*config*.py", "agent.py", "main.py"])
            
            # 如果没有匹配到特定目录，使用源码文件筛选
            if not likely_dirs:
                likely_dirs = ["**/*.py"]
            
            # 去重
            likely_dirs = list(dict.fromkeys(likely_dirs))
            
            # 智能工具选择基于查询类型
            tools = []
            
            # 检测查询类型并选择最适合的工具组合
            if any(word in query_lower for word in ['类', 'class', '方法', 'method', '函数', 'function']):
                # 具体符号查询 - 优先使用symbol_search，但必须包含embedding
                symbol_name = user_query
                for remove_word in ['类', '的实现', '实现代码', '的代码', 'class', '的实现代码', '类的实现']:
                    symbol_name = symbol_name.replace(remove_word, '')
                symbol_name = symbol_name.strip()
                
                tools.extend([
                    {
                        "tool_name": "embedding_search",
                        "parameters": {"query": user_query, "top_k": 5},
                        "reason": "必须包含的语义搜索，理解查询意图"
                    },
                    {
                        "tool_name": "symbol_search", 
                        "parameters": {"symbol_name": symbol_name, "symbol_types": ["class", "function"]},
                        "reason": "符号搜索精确定位类和函数"
                    }
                ])
            elif any(word in query_lower for word in ['大模型', 'llm', 'model', '模型', '几种', '多少', 'api调用', '修改', '配置', '设置', '参数']):
                # 模型/配置查询 - 优先embedding理解用户意图，然后精确匹配
                tools.extend([
                    {
                        "tool_name": "embedding_search",
                        "parameters": {"query": user_query, "top_k": 6},
                        "reason": "必须优先的语义搜索，理解配置修改意图"
                    },
                    {
                        "tool_name": "ripgrep_search",
                        "parameters": {"patterns": ["model_name", "ModelType", "ModelManager", "doubao", "Qwen", "config"], "case_sensitive": False},
                        "reason": "精确匹配模型配置相关代码"
                    },
                    {
                        "tool_name": "keyword_search",
                        "parameters": {"keywords": ["大模型", "LLM", "model", "模型", "配置", "修改"]},
                        "reason": "关键词匹配补充"
                    }
                ])
            elif any(word in query_lower for word in ['文件', 'file', '.py', '.js', '.ts']):
                # 文件查询
                tools.extend([
                    {
                        "tool_name": "embedding_search",
                        "parameters": {"query": user_query, "top_k": 5},
                        "reason": "必须包含的语义搜索，理解文件查询意图"
                    },
                    {
                        "tool_name": "file_path_search", 
                        "parameters": {"path_pattern": user_query.replace('文件', '').strip()},
                        "reason": "文件路径查询最直接"
                    },
                    {
                        "tool_name": "ripgrep_search",
                        "parameters": {"patterns": [user_query], "case_sensitive": False},
                        "reason": "文本匹配补充"
                    }
                ])
            elif any(word in query_lower for word in ['如何', '怎么', '原理', '机制', '设计', '算法', '优化', '性能', '工作流程', '处理流程']):
                # 概念性/探索性查询 - 优先使用embedding语义搜索
                tools.extend([
                    {
                        "tool_name": "embedding_search",
                        "parameters": {"query": user_query, "top_k": 7},
                        "reason": "语义检索最适合概念性和探索性查询"
                    },
                    {
                        "tool_name": "keyword_search", 
                        "parameters": {"keywords": user_query.split()[:4]},  # 取前4个词作为关键词
                        "reason": "关键词组合补充"
                    }
                ])
            else:
                # 一般查询 - embedding优先，精确匹配补充
                tools.extend([
                    {
                        "tool_name": "embedding_search",
                        "parameters": {"query": user_query, "top_k": 6},
                        "reason": "必须优先的语义检索，理解查询意图"
                    },
                    {
                        "tool_name": "ripgrep_search",
                        "parameters": {"patterns": [user_query], "case_sensitive": False},
                        "reason": "精确匹配补充"
                    },
                    {
                        "tool_name": "keyword_search",
                        "parameters": {"keywords": user_query.split()[:3]},
                        "reason": "关键词匹配补充"
                    }
                ])
            
            return SearchPlan(
                reasoning=f"LLM分析失败，基于查询类型智能选择工具组合，推测相关目录: {', '.join(likely_dirs)}",
                tools=tools,
                priority="medium",
                path_scope={
                    "reasoning": f"基于查询内容分析，推测相关目录为: {', '.join(likely_dirs)}", 
                    "include_patterns": likely_dirs, 
                    "exclude_patterns": ["**/test/**", "**/tests/**", "**/__pycache__/**", "**/node_modules/**"], 
                    "scope_type": "智能推测",
                    "strategy": "conservative"
                }
            )
    
    def _parse_llm_json_response(self, response: str) -> dict:
        """
        鲁棒地解析LLM返回的JSON响应
        支持处理markdown格式的JSON（```json...```）和纯JSON格式
        """
        if not response or not response.strip():
            raise ValueError("LLM返回了空响应")
        
        # 移除首尾空白字符
        response = response.strip()
        
        # 尝试提取markdown格式的JSON
        if "```json" in response and "```" in response:
            # 查找第一个```json和最后一个```
            start_marker = "```json"
            end_marker = "```"
            
            start_idx = response.find(start_marker)
            if start_idx != -1:
                start_idx += len(start_marker)
                # 从start_marker之后查找结束标记
                end_idx = response.find(end_marker, start_idx)
                if end_idx != -1:
                    json_content = response[start_idx:end_idx].strip()
                else:
                    # 如果没有找到结束标记，取从start_marker到最后一个```
                    last_end = response.rfind(end_marker)
                    if last_end > start_idx:
                        json_content = response[start_idx:last_end].strip()
                    else:
                        json_content = response[start_idx:].strip()
        else:
            # 如果没有markdown标记，直接使用原始response
            json_content = response
        
        # 尝试解析JSON
        try:
            return json.loads(json_content)
        except json.JSONDecodeError as e:
            logger.warning(f"JSON解析失败: {e}")
            logger.warning(f"原始响应: {response[:200]}...")
            logger.warning(f"提取的JSON内容: {json_content[:200]}...")
            
            # 尝试修复常见的JSON问题
            json_content = self._fix_common_json_issues(json_content)
            try:
                return json.loads(json_content)
            except json.JSONDecodeError as e2:
                raise ValueError(f"JSON解析失败，即使在修复后也无法解析: {e2}")
    
    def _fix_common_json_issues(self, json_str: str) -> str:
        """修复JSON字符串的常见问题"""
        # 移除不必要的换行符和空格
        json_str = json_str.strip()
        
        # 如果字符串以非{开头，尝试找到第一个{
        if not json_str.startswith('{'):
            start_idx = json_str.find('{')
            if start_idx != -1:
                json_str = json_str[start_idx:]
        
        # 如果字符串以非}结尾，尝试找到最后一个}
        if not json_str.endswith('}'):
            end_idx = json_str.rfind('}')
            if end_idx != -1:
                json_str = json_str[:end_idx+1]
        
        # 移除注释（// 或 /* */)
        import re
        json_str = re.sub(r'//.*?\n', '\n', json_str)
        json_str = re.sub(r'/\*.*?\*/', '', json_str, flags=re.DOTALL)
        
        return json_str
    
    async def _execute_search_plan(self, plan: SearchPlan, top_k: int, user_query: str = "") -> Dict[str, List[Any]]:
        """执行检索计划"""
        logger.info(f"🧠 LLM分析: {plan.reasoning}")
        logger.info(f"🔧 执行 {len(plan.tools)} 个检索工具:")
        for tool in plan.tools:
            logger.info(f"🔧 {tool.get('tool_name')}: {tool.get('parameters', {})}, {tool.get('reason', '')}")
        
        # 记录路径筛选信息
        path_scope = plan.path_scope
        if path_scope:
            strategy = path_scope.get('strategy', 'moderate')
            logger.info(f"📁 路径范围: {path_scope.get('scope_type', '全库检索')} (策略: {strategy})")
            if path_scope.get('include_patterns'):
                logger.info(f"📁 包含模式: {path_scope.get('include_patterns')}")
            if path_scope.get('exclude_patterns'):
                logger.info(f"📁 排除模式: {path_scope.get('exclude_patterns')}")
            logger.debug(f"📁 路径分析: {path_scope.get('reasoning', '')}")
            
            # 策略合理性检查
            if user_query and strategy == 'strict' and any(keyword in user_query.lower() for keyword in ['功能', 'function', '在哪里', 'where']):
                logger.warning("⚠️  检测到功能性查询使用strict策略，可能导致遗漏重要文件")
            elif strategy == 'conservative' and len(path_scope.get('include_patterns', [])) > 5:
                logger.warning("⚠️  conservative策略配置了过多include模式，可能过于严格")
        
        # 创建路径筛选器
        path_filter = PathFilter(
            include_patterns=path_scope.get('include_patterns', []),
            exclude_patterns=path_scope.get('exclude_patterns', [])
        )
        
        results = {}
        
        # 并行执行检索工具
        tool_coros = []
        for tool_config in plan.tools:
            tool_name = tool_config.get("tool_name")
            parameters = tool_config.get("parameters", {})
            reason = tool_config.get("reason", "")
            
            logger.debug(f"📝 {tool_name}: {reason}")
            
            if tool_name == "embedding_search":
                coro = self._execute_embedding_search(parameters, tool_name, path_filter)
            elif tool_name == "ripgrep_search":
                coro = self._execute_ripgrep_search(parameters, tool_name, path_filter)
            elif tool_name == "symbol_search":
                coro = self._execute_symbol_search(parameters, tool_name, path_filter)
            elif tool_name == "file_path_search":
                coro = self._execute_file_path_search(parameters, tool_name, path_filter)
            elif tool_name == "keyword_search":
                coro = self._execute_keyword_search(parameters, tool_name, path_filter)
            else:
                coro = None
            
            if coro is not None:
                tool_coros.append((tool_name, coro))
        
        # 并行执行所有工具，记录详细时间（每个工具独立计时）
        if tool_coros:
            async def run_with_timing(name, coro):
                start = time.perf_counter()
                try:
                    result = await coro
                    elapsed = time.perf_counter() - start
                    return name, result, elapsed
                except Exception as e:
                    elapsed = time.perf_counter() - start
                    return name, e, elapsed
            
            tools_start_time = time.perf_counter()
            logger.info(f"⚡ 开始并行执行 {len(tool_coros)} 个工具...")
            
            tasks = [asyncio.create_task(run_with_timing(name, coro)) for name, coro in tool_coros]
            task_results = await asyncio.gather(*tasks, return_exceptions=False)
            
            tools_total_time = time.perf_counter() - tools_start_time
            logger.info(f"⚡ 所有工具执行完成，总耗时: {tools_total_time:.2f}秒")
            
            # 详细记录每个工具的执行情况和耗时
            summary_entries = []
            for name, result, elapsed in task_results:
                if isinstance(result, Exception):
                    logger.warning(f"❌ {name} 执行失败: {result} (耗时{elapsed:.2f}秒)")
                    results[name] = []
                    summary_entries.append(f"{name}: 失败 {elapsed:.2f}s")
                else:
                    results[name] = result
                    result_count = len(result)
                    logger.info(f"⚡ {name} 执行耗时: {elapsed:.2f}秒")
                    logger.info(f"✅ {name}: {result_count} 个结果")
                    summary_entries.append(f"{name}: {result_count}个, {elapsed:.2f}s")
            
            # 总结所有工具执行情况
        
        return results
    
    async def _execute_embedding_search(self, params: Dict, tool_name: str, path_filter: PathFilter) -> List[Any]:
        """执行嵌入检索"""
        try:
            query = params.get("query", "")
            top_k = params.get("top_k", 10)
            
            if not query:
                return []
            
            # 🚀 性能优化：复用传统检索的高效路径，避免重复的路径过滤开销
            if not path_filter.include_patterns and not path_filter.exclude_patterns:
                # 无路径筛选时，直接使用传统模式的高效路径
                embedding_results = await self.retrieval_engine._search_embeddings(query, min(top_k + 3, 10))
                results = [(chunk, float(score), tool_name) for chunk, score in embedding_results]
            else:
                # 有路径筛选时，优化筛选逻辑
                embedding_results = await self.retrieval_engine._search_embeddings(query, min(top_k + 3, 10))
                
                # 应用路径筛选并转换为统一格式（优化版本）
                results = []
                for item in embedding_results:
                    if isinstance(item, tuple) and len(item) == 2:
                        chunk, score = item
                        # 快速路径检查：避免对明显不匹配的路径进行昂贵的glob匹配
                        file_path = chunk.file_path
                        
                        # 快速排除检查
                        should_skip = False
                        for exclude_pattern in path_filter.exclude_patterns:
                            if exclude_pattern.replace('**/', '').replace('/**', '') in file_path:
                                should_skip = True
                                break
                        
                        if not should_skip:
                            rel_file_path = self._get_relative_path(file_path)
                            if path_filter.should_include(rel_file_path):
                                results.append((chunk, float(score), tool_name))
                                if len(results) >= top_k:  # 提前退出，避免不必要的处理
                                    break
                    else:
                        chunk = item
                        file_path = chunk.file_path
                        
                        # 快速排除检查
                        should_skip = False
                        for exclude_pattern in path_filter.exclude_patterns:
                            if exclude_pattern.replace('**/', '').replace('/**', '') in file_path:
                                should_skip = True
                                break
                        
                        if not should_skip:
                            rel_file_path = self._get_relative_path(file_path)
                            if path_filter.should_include(rel_file_path):
                                results.append((chunk, 0.8, tool_name))
                                if len(results) >= top_k:
                                    break
            
            return results[:top_k]  # 返回筛选后的top_k结果
            
        except Exception as e:
            logger.warning(f"嵌入检索失败: {e}")
            return []
    
    async def _execute_ripgrep_search(self, params: Dict, tool_name: str, path_filter: PathFilter) -> List[Any]:
        """执行ripgrep检索"""
        try:
            # 兼容不同的参数格式
            patterns = params.get("patterns", []) or params.get("pattern", [])
            if isinstance(patterns, str):
                patterns = [patterns]
            
            if not patterns:
                logger.warning(f"ripgrep_search 没有收到有效的搜索模式: {params}")
                return []
            
            # 调用现有的ripgrep检索 - 性能优化版本
            ripgrep_results = await self.retrieval_engine._search_ripgrep(patterns[0], 15)  # 多获取一些，预期筛选会过滤掉部分
            
            # 优化路径筛选逻辑
            results = []
            if not path_filter.include_patterns and not path_filter.exclude_patterns:
                # 无路径筛选时，直接使用结果，避免路径转换开销
                results = [(chunk, 0.9, tool_name) for chunk in ripgrep_results]
            else:
                # 有路径筛选时，使用快速筛选
                for chunk in ripgrep_results:
                    # 快速排除检查
                    file_path = chunk.file_path
                    should_skip = False
                    for exclude_pattern in path_filter.exclude_patterns:
                        if exclude_pattern.replace('**/', '').replace('/**', '') in file_path:
                            should_skip = True
                            break
                    
                    if not should_skip:
                        rel_file_path = self._get_relative_path(file_path)
                        if path_filter.should_include(rel_file_path):
                            results.append((chunk, 0.9, tool_name))
                            if len(results) >= 10:  # 控制结果数量
                                break
            
            return results
            
        except Exception as e:
            logger.warning(f"Ripgrep检索失败: {e}")
            return []
    
    async def _execute_symbol_search(self, params: Dict, tool_name: str, path_filter: PathFilter) -> List[Any]:
        """执行符号检索"""
        try:
            symbol_name = params.get("symbol_name", "")
            if not symbol_name:
                return []
            
            # 调用现有的符号检索 - 优化：减少获取数量提升速度  
            symbol_results = await self.retrieval_engine._search_symbols(symbol_name, 10)  # 获取适量结果，平衡质量与速度
            
            # 应用路径筛选并转换为统一格式  
            results = []
            for chunk in symbol_results:
                rel_file_path = self._get_relative_path(chunk.file_path)
                if path_filter.should_include(rel_file_path):
                    results.append((chunk, 0.95, tool_name))
            
            return results
            
        except Exception as e:
            logger.warning(f"符号检索失败: {e}")
            return []
    
    async def _execute_file_path_search(self, params: Dict, tool_name: str, path_filter: PathFilter) -> List[Any]:
        """执行文件路径检索"""
        try:
            path_pattern = params.get("path_pattern", "")
            if not path_pattern:
                return []
            
            # 基于路径模式在索引中查找文件
            results = []
            if hasattr(self.retrieval_engine, 'repo_index'):
                for file_path in self.retrieval_engine.repo_index.file_chunks.keys():
                    rel_file_path = self._get_relative_path(file_path)
                    if path_pattern.lower() in rel_file_path.lower() and path_filter.should_include(rel_file_path):
                        chunks = self.retrieval_engine.repo_index.get_chunks_by_file(file_path)
                        for chunk in chunks[:3]:  # 每个匹配文件最多3个chunks
                            results.append((chunk, 0.85, tool_name))
            
            return results
            
        except Exception as e:
            logger.warning(f"文件路径检索失败: {e}")
            return []
    
    async def _execute_keyword_search(self, params: Dict, tool_name: str, path_filter: PathFilter) -> List[Any]:
        """执行关键词检索（直接调用原生ripgrep；并发执行；源头限域加速）"""
        try:
            keywords = params.get("keywords", [])
            if isinstance(keywords, str):
                keywords = [keywords]
            
            if not keywords:
                return []
            
            # 源头限域：将路径筛选直接下推到ripgrep，减少扫描范围
            include_globs = list(path_filter.include_patterns) if path_filter.include_patterns else []
            exclude_globs = list(getattr(ignore_config, 'all_ignore_patterns', [])) + list(path_filter.exclude_patterns)
            
            # 并发执行多个关键词的ripgrep，等待最慢的一个完成（总体更快）
            trimmed_keywords = [kw.strip() for kw in keywords[:4] if isinstance(kw, str) and kw.strip()]
            if not trimmed_keywords:
                return []
            
            import asyncio
            tasks = [
                asyncio.to_thread(
                    self.ripgrep_executor.execute_search,
                    [kw],
                    self.repo_path,
                    include_globs,
                    exclude_globs,
                )
                for kw in trimmed_keywords
            ]
            task_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 汇总并转换为chunks（文件级去重+限量）
            all_chunks = []
            processed_files = set()
            for result in task_results:
                if isinstance(result, Exception):
                    logger.debug(f"关键词ripgrep并发任务失败: {result}")
                    continue
                for ripgrep_result in result[:8]:  # 每个关键词最多取8条
                    try:
                        chunk = self.retrieval_engine._ripgrep_result_to_chunk(ripgrep_result)
                        if chunk and chunk.file_path not in processed_files:
                            processed_files.add(chunk.file_path)
                            all_chunks.append(chunk)
                            if len(all_chunks) >= 12:
                                break
                    except Exception as e:
                        logger.debug(f"转换ripgrep结果失败: {e}")
                        continue
                if len(all_chunks) >= 12:
                    break
            
            ripgrep_results = all_chunks
            
            # 复用ripgrep_search的高效路径过滤逻辑
            results = []
            if not path_filter.include_patterns and not path_filter.exclude_patterns:
                # 无路径筛选时，直接使用结果，避免路径转换开销
                results = [(chunk, 0.8, tool_name) for chunk in ripgrep_results]
            else:
                # 有路径筛选时，使用快速筛选（复用ripgrep_search逻辑）
                for chunk in ripgrep_results:
                    # 快速排除检查
                    file_path = chunk.file_path
                    should_skip = False
                    for exclude_pattern in path_filter.exclude_patterns:
                        if exclude_pattern.replace('**/', '').replace('/**', '') in file_path:
                            should_skip = True
                            break
                    
                    if not should_skip:
                        rel_file_path = self._get_relative_path(file_path)
                        if path_filter.should_include(rel_file_path):
                            results.append((chunk, 0.8, tool_name))
                            if len(results) >= 10:  # 提前退出，控制结果数量
                                break
            
            return results[:10]  # 总共最多返回10个结果，减少处理时间
            
        except Exception as e:
            logger.warning(f"关键词检索失败: {e}")
            return []
    
    async def _aggregate_results(self, all_results: Dict[str, List[Any]], user_query: str, top_k: int) -> List[RetrievalResult]:
        """聚合和去重检索结果"""
        try:
            # 合并所有结果
            merged_results = []
            # 强化embedding语义检索 - embedding优先策略
            tool_weights = {
                "embedding_search": 1.2,     # 大幅提升embedding权重，语义理解优先
                "symbol_search": 1.1,        # 符号搜索高权重，精确定位
                "ripgrep_search": 1.0,       # ripgrep精确匹配，标准权重
                "file_path_search": 0.9,     # 文件路径搜索
                "keyword_search": 0.8,       # 关键词搜索
            }
            
            for tool_name, results in all_results.items():
                weight = tool_weights.get(tool_name, 0.5)
                for chunk, score, source in results:
                    # 调整分数权重
                    adjusted_score = float(score) * weight
                    merged_results.append((chunk, adjusted_score, source))
            
            # 改进去重策略：优化但不过度激进
            unique_results = {}
            file_count = {}  # 统计每个文件的结果数量
            
            for chunk, score, source in merged_results:
                chunk_id = getattr(chunk, 'id', f"{chunk.file_path}:{chunk.start_line}")
                file_path = chunk.file_path
                
                # 基础去重：相同chunk ID只保留最高分
                if chunk_id not in unique_results or unique_results[chunk_id][1] < score:
                    unique_results[chunk_id] = (chunk, score, source)
                
                # 统计文件结果数
                file_count[file_path] = file_count.get(file_path, 0) + 1

            # 与传统检索一致：当候选数量超过 top_k 时，走 LLM 筛选
            candidates = list(unique_results.values())  # (chunk, score, source)
            if len(candidates) > top_k:
                logger.info(f"🤖 Agent聚合启用LLM筛选: 候选 {len(candidates)} > top_k {top_k}")
                chunks_for_llm = [chunk for chunk, _, _ in candidates]
                try:
                    filtered_results = await self.retrieval_engine.llm_filter.filter_chunks(
                        user_query, chunks_for_llm
                    )
                    logger.info(f"🎯 LLM筛选完成(Agent聚合): {len(filtered_results)} 个结果")
                except Exception as e:
                    logger.warning(f"LLM筛选失败(Agent聚合)，回退到规则分数: {e}")
                    # 回退为已加权分数
                    filtered_results = [(chunk, score) for chunk, score, _ in candidates]

                # 最终结果处理
                final_results = self.retrieval_engine._process_final_results(filtered_results, top_k)
                return final_results
            
            # 如果同一文件结果过多（>3个），只保留分数最高的3个
            file_chunks = {}
            for chunk_id, (chunk, score, source) in unique_results.items():
                file_path = chunk.file_path
                if file_path not in file_chunks:
                    file_chunks[file_path] = []
                file_chunks[file_path].append((chunk_id, chunk, score, source))
            
            # 严格去重：每个文件只保留1个最高分结果
            unique_results = {}
            for file_path, chunks in file_chunks.items():
                # 按分数排序，只保留最高分的1个
                chunks.sort(key=lambda x: x[2], reverse=True)  # 按score排序
                if chunks:  # 确保有结果
                    chunk_id, chunk, score, source = chunks[0]  # 只取第1个
                    unique_results[chunk_id] = (chunk, score, source)
            
            # 按分数排序
            sorted_results = sorted(unique_results.values(), key=lambda x: x[1], reverse=True)
            
            # 使用现有的结果处理器进行最终处理
            chunk_scores = [(chunk, score) for chunk, score, _ in sorted_results]
            final_results = self.retrieval_engine._process_final_results(chunk_scores, top_k)
            
            # 记录结果来源统计
            source_stats = {}
            for _, _, source in sorted_results[:top_k]:
                source_stats[source] = source_stats.get(source, 0) + 1
            
            logger.info(f"📊 结果来源统计: {source_stats}")
            
            return final_results
            
        except Exception as e:
            logger.warning(f"结果聚合失败: {e}")
            return []
