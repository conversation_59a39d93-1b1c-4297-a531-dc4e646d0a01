"""
查询重写器
将自然语言查询转换为ripgrep命令和嵌入检索查询
"""

import logging
import re

from config import RETRIEVAL_CONFIG, ModelType, model_manager
from utils.interfaces import IQueryRewriter

logger = logging.getLogger(__name__)


class QueryRewriter(IQueryRewriter):
    """查询重写器实现"""

    def __init__(self):
        self.max_rewrite_attempts = RETRIEVAL_CONFIG["max_query_rewrite_attempts"]

        # 常见的编程概念映射
        self.concept_mappings = {
            "函数": ["function", "def", "fn"],
            "方法": ["method", "function", "def"],
            "类": ["class", "struct", "interface"],
            "变量": ["var", "let", "const", "variable"],
            "接口": ["interface", "trait", "protocol"],
            "配置": ["config", "setting", "option"],
            "工具": ["util", "helper", "tool"],
            "测试": ["test", "spec", "unittest"],
            "错误": ["error", "exception", "err"],
            "日志": ["log", "logger", "logging"],
            "数据库": ["db", "database", "sql"],
            "网络": ["http", "net", "request", "response"],
            "文件": ["file", "io", "stream"],
            "字符串": ["string", "str", "text"],
            "数组": ["array", "list", "vector"],
            "对象": ["object", "obj", "instance"],
            "初始化": ["init", "initialize", "setup"],
            "创建": ["create", "new", "make"],
            "删除": ["delete", "remove", "destroy"],
            "更新": ["update", "modify", "change"],
            "获取": ["get", "fetch", "retrieve"],
            "设置": ["set", "put", "assign"],
            "检查": ["check", "validate", "verify"],
            "处理": ["process", "handle", "deal"],
            "解析": ["parse", "decode", "analyze"],
            "格式化": ["format", "stringify", "serialize"],
            "转换": ["convert", "transform", "map"],
            "计算": ["calculate", "compute", "eval"],
            "排序": ["sort", "order", "arrange"],
            "搜索": ["search", "find", "query"],
            "过滤": ["filter", "select", "where"],
        }

    async def rewrite_to_ripgrep(
        self, query: str, repo_insight: str, keyword_list: list[str] = None
    ) -> list[str]:
        """将查询重写为ripgrep命令"""
        try:
            # 构建重写提示词，包含关键词列表
            prompt = self._build_ripgrep_rewrite_prompt(
                query, repo_insight, keyword_list
            )

            # 调用LLM进行重写
            response = await model_manager.chat_completion(
                prompt, model_type=ModelType.NORMAL
            )

            # 解析LLM响应
            ripgrep_commands = self._parse_ripgrep_response(response)

            # 如果LLM重写失败，使用规则重写
            if not ripgrep_commands:
                logger.debug("LLM重写失败，使用规则重写")
                ripgrep_commands = self._rule_based_rewrite(query, keyword_list)

            return ripgrep_commands

            # 如果LLM重写失败，使用规则重写
            if not ripgrep_commands:
                ripgrep_commands = self._rule_based_rewrite(query, keyword_list)

            return ripgrep_commands

        except Exception as e:
            logger.warning(f"LLM查询重写失败: {e}")
            # 降级到规则重写
            return self._rule_based_rewrite(query, keyword_list)

    def _build_ripgrep_rewrite_prompt(
        self, query: str, repo_insight: str, keyword_list: list[str] = None
    ) -> str:
        """构建ripgrep重写提示词"""
        # keyword_context = ""
        # if keyword_list:
        #     # 限制关键词数量，避免提示词过长
        #     relevant_keywords = keyword_list[:20]
        #     keyword_context = f"\n相关关键词：{', '.join(relevant_keywords)}\n"

        prompt = f"""你是一个代码搜索专家，需要将用户的自然语言查询转换为有效的ripgrep搜索命令。

仓库信息：
{repo_insight}

用户查询：
{query}

请将用户查询转换为1-3个ripgrep搜索模式，每个模式一行。搜索模式应该：
1. 分别搜索类名和方法名，不要组合成 ClassName.method_name 的形式
2. 如果查询包含方法名（如_extract_symbol_entities），直接搜索方法名
3. 如果查询包含类名（如RetrievalEngine），直接搜索类名
4. 生成从具体到宽泛的模式：先搜索精确名称，再搜索相关关键词
5. 不用输出引号，直接输出搜索模式

输出格式（每行一个搜索模式）：
pattern1
pattern2
pattern3

搜索模式：
"""
        return prompt

    def _parse_ripgrep_response(self, response: str) -> list[str]:
        """解析LLM的ripgrep响应"""
        if not response:
            return []

        # 提取搜索模式
        patterns = []
        lines = response.strip().split("\n")

        for line in lines:
            line = line.strip()
            if line and not line.startswith("#") and not line.startswith("//"):
                # 清理可能的格式标记
                # 1) 去除编号前缀: "1. ", "2) ", "(3) ", "- ", "* " 等
                pattern = re.sub(r"^\s*\d+[\.)]\s*", "", line)  # 1. xxx 或 1) xxx
                pattern = re.sub(
                    r"^\s*[\-\*\•\–]\s*", "", pattern
                )  # - xxx, * xxx, • xxx, – xxx
                pattern = re.sub(r"^\s*\(\d+\)\s*", "", pattern)  # (1) xxx
                pattern = pattern.strip("`\"'")

                if pattern and len(pattern) > 2:
                    patterns.append(pattern)

        return patterns[:3]  # 最多返回3个模式

    def _rule_based_rewrite(
        self, query: str, keyword_list: list[str] = None
    ) -> list[str]:
        """基于规则的查询重写"""
        patterns = []
        query_lower = query.lower()

        # 1. 提取关键词
        keywords = self._extract_keywords_from_query(query)

        # 2. 概念映射 - 添加英文对应词
        mapped_terms = []
        for chinese_term, english_terms in self.concept_mappings.items():
            if chinese_term in query:
                mapped_terms.extend(english_terms)

        # 合并关键词和映射词汇
        all_terms = keywords + mapped_terms

        # 3. 如果提供了关键词列表，添加相关的关键词
        if keyword_list:
            query_words = set(query_lower.split())
            # 更宽松的匹配策略
            for kw in keyword_list:
                kw_lower = kw.lower()
                # 检查是否有任何查询词包含在关键词中，或关键词包含在查询词中
                if any(
                    word in kw_lower or kw_lower in word
                    for word in query_words
                    if len(word) > 2
                ):
                    all_terms.append(kw)
                    if len(all_terms) >= 10:  # 限制数量
                        break

        # 4. 生成简单的搜索模式（避免复杂的正则表达式）

        # 模式1: 单个关键词搜索（最有效）
        for term in all_terms[:3]:  # 只取前3个最相关的词
            if len(term) >= 2:  # 避免太短的词
                patterns.append(term)

        # 模式2: 如果查询中包含明确的类名或函数名，添加更具体的模式
        if any(word[0].isupper() for word in query.split() if len(word) > 2):
            # 提取可能的类名
            class_names = [
                word for word in query.split() if len(word) > 2 and word[0].isupper()
            ]
            for class_name in class_names[:2]:
                patterns.append(f"class {class_name}")
                patterns.append(f"def {class_name}")  # 可能是函数名

        # 模式3: 如果查询中包含方法名（以下划线开头），单独搜索方法名
        method_names = [
            word for word in query.split() if word.startswith("_") and len(word) > 2
        ]
        for method_name in method_names[:2]:
            patterns.append(f"def {method_name}")
            patterns.append(method_name)  # 也搜索方法调用

        # 模式2: 如果是坐标转换相关查询，添加特定模式
        if any(
            word in query_lower
            for word in ["坐标", "转换", "coordinate", "transform", "convert"]
        ):
            coordinate_terms = [
                "transform",
                "coordinate",
                "convert",
                "translate",
                "x",
                "y",
                "screen",
                "source",
            ]
            for term in coordinate_terms:
                if term not in patterns:
                    patterns.append(term)
                    if len(patterns) >= 5:
                        break

        # 模式3: 如果没有找到任何模式，使用查询中的有意义词汇
        if not patterns:
            # 提取查询中的英文单词和中文词汇
            english_words = re.findall(r"\b[a-zA-Z]{2,}\b", query)
            chinese_words = re.findall(r"[\u4e00-\u9fff]{2,}", query)

            for word in english_words + chinese_words:
                if word.lower() not in [
                    "的",
                    "是",
                    "在",
                    "有",
                    "和",
                    "或",
                    "但",
                    "如何",
                    "什么",
                    "哪个",
                    "怎么",
                ]:
                    patterns.append(word)
                    if len(patterns) >= 3:
                        break

        # 确保至少有一个搜索模式
        if not patterns:
            patterns = ["transform", "coordinate", "convert"]  # 默认模式

        return patterns[:3]  # 最多返回3个模式

    def _extract_keywords_from_query(self, query: str) -> list[str]:
        """从查询中提取关键词"""
        # 移除常见的停用词
        stop_words = {
            "的",
            "是",
            "在",
            "有",
            "和",
            "或",
            "但",
            "如何",
            "什么",
            "哪个",
            "怎么",
            "实现",
            "代码",
            "程序",
            "系统",
            "应用",
            "项目",
            "文件",
            "模块",
            "the",
            "is",
            "in",
            "and",
            "or",
            "but",
            "how",
            "what",
            "which",
            "code",
            "program",
            "system",
            "application",
            "project",
            "file",
            "module",
        }

        # 提取英文单词
        english_words = re.findall(r"\b[a-zA-Z_][a-zA-Z0-9_]*\b", query)

        # 先在原始查询中移除中文停用词，保留有意义片段
        query_cn_cleaned = query
        for sw in stop_words:
            # 仅移除包含中文字符的停用词（英文停用词用英文规则处理）
            if re.search(r"[\u4e00-\u9fff]", sw):
                query_cn_cleaned = query_cn_cleaned.replace(sw, "")

        # 清理后提取中文连续片段
        chinese_sequences = re.findall(r"[\u4e00-\u9fff]+", query_cn_cleaned)

        keywords = []

        # 过滤英文关键词
        for word in english_words:
            if len(word) >= 2 and word.lower() not in stop_words:
                keywords.append(word)

        # 过滤中文关键词
        for seq in chinese_sequences:
            if len(seq) >= 2 and seq not in stop_words:
                keywords.append(seq)

        return keywords[:5]  # 最多返回5个关键词

    def rewrite_to_embedding_query(self, query: str) -> str:
        """将查询重写为嵌入检索查询"""
        # 对于嵌入检索，通常直接使用原始查询效果更好
        # 但可以进行一些清理和增强

        # 移除无意义的词汇
        cleaned_query = query

        # 移除疑问词（中英文，包含中文场景下的无词边界）
        question_words = [
            "如何实现",
            "如何",
            "怎么",
            "什么",
            "哪个",
            "在哪里",
            "哪里",
            "为什么",
            "how",
            "what",
            "which",
            "why",
            "where",
            "?",
            "？",
        ]
        for qw in question_words:
            # 中文疑问词：直接移除（不使用\b边界）
            if re.search(r"[\u4e00-\u9fff]", qw):
                cleaned_query = cleaned_query.replace(qw, "")
            else:
                # 英文疑问词：使用单词边界
                cleaned_query = re.sub(
                    rf"\b{re.escape(qw)}\b", "", cleaned_query, flags=re.IGNORECASE
                )

        # 清理多余空格
        cleaned_query = " ".join(cleaned_query.split())

        # 如果查询太短，尝试扩展
        if len(cleaned_query.split()) < 3:
            # 添加上下文信息
            if "函数" in query:
                cleaned_query += " 函数定义 方法实现"
            elif "类" in query:
                cleaned_query += " 类定义 对象结构"
            elif "配置" in query:
                cleaned_query += " 配置文件 设置参数"

        return cleaned_query.strip() or query  # 如果清理后为空，返回原查询

    def generate_fallback_patterns(self, query: str) -> list[str]:
        """生成降级搜索模式"""
        patterns = []

        # 提取所有可能的标识符
        identifiers = re.findall(r"\b[a-zA-Z_][a-zA-Z0-9_]*\b", query)

        if identifiers:
            # 简单的标识符搜索
            for identifier in identifiers[:2]:
                patterns.append(identifier)

        # 如果没有英文标识符，尝试中文关键词
        if not patterns:
            chinese_words = re.findall(r"[\u4e00-\u9fff]+", query)
            for word in chinese_words[:2]:
                if len(word) >= 2:
                    patterns.append(word)

        return patterns or [query]  # 最后降级到原查询
