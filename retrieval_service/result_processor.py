"""
结果处理器
负责结果排序、去重和格式化输出
"""

from utils.models import CodeChunk, RetrievalResult


class ResultProcessor:
    """结果处理器"""

    def __init__(self):
        self.max_content_length = 2000  # 输出内容的最大长度

    def sort_and_deduplicate(
        self, chunk_scores: list[tuple[CodeChunk, float]]
    ) -> list[tuple[CodeChunk, float]]:
        """排序和去重"""
        if not chunk_scores:
            return []

        # 去重：使用chunk ID作为唯一标识
        seen_chunks = set()
        unique_results = []

        for chunk, score in chunk_scores:
            if chunk.id not in seen_chunks:
                unique_results.append((chunk, score))
                seen_chunks.add(chunk.id)

        # 按分数排序
        unique_results.sort(key=lambda x: x[1], reverse=True)

        return unique_results

    def merge_scores(
        self,
        results_list: list[list[tuple[CodeChunk, float]]],
        weights: list[float] = None,
    ) -> list[tuple[CodeChunk, float]]:
        """合并多个结果列表的分数"""
        if not results_list:
            return []

        if weights is None:
            weights = [1.0] * len(results_list)

        # 收集所有分片的分数
        chunk_scores = {}

        for i, results in enumerate(results_list):
            weight = weights[i]

            for chunk, score in results:
                if chunk.id not in chunk_scores:
                    chunk_scores[chunk.id] = {
                        "chunk": chunk,
                        "scores": [],
                        "weights": [],
                    }

                chunk_scores[chunk.id]["scores"].append(score)
                chunk_scores[chunk.id]["weights"].append(weight)

        # 计算加权平均分数
        merged_results = []
        for _, data in chunk_scores.items():
            chunk = data["chunk"]
            scores = data["scores"]
            weights_list = data["weights"]

            # 加权平均
            weighted_sum = sum(
                score * weight for score, weight in zip(scores, weights_list)
            )
            total_weight = sum(weights_list)

            if total_weight > 0:
                final_score = weighted_sum / total_weight
                merged_results.append((chunk, final_score))

        # 排序
        merged_results.sort(key=lambda x: x[1], reverse=True)

        return merged_results

    def boost_by_file_type(
        self,
        chunk_scores: list[tuple[CodeChunk, float]],
        preferred_languages: list[str] = None,
        boost_factor: float = 0.1,
    ) -> list[tuple[CodeChunk, float]]:
        """根据文件类型提升分数"""
        if not preferred_languages:
            return chunk_scores

        boosted_results = []

        for chunk, score in chunk_scores:
            boost = 0.0

            if chunk.language in preferred_languages:
                boost = boost_factor

            boosted_score = min(score + boost, 1.0)
            boosted_results.append((chunk, boosted_score))

        # 重新排序
        boosted_results.sort(key=lambda x: x[1], reverse=True)

        return boosted_results

    def boost_by_chunk_type(
        self,
        chunk_scores: list[tuple[CodeChunk, float]],
        preferred_types: list[str] = None,
        boost_factor: float = 0.1,
    ) -> list[tuple[CodeChunk, float]]:
        """根据分片类型提升分数"""
        if not preferred_types:
            return chunk_scores

        boosted_results = []

        for chunk, score in chunk_scores:
            boost = 0.0

            if chunk.chunk_type in preferred_types:
                boost = boost_factor

            boosted_score = min(score + boost, 1.0)
            boosted_results.append((chunk, boosted_score))

        # 重新排序
        boosted_results.sort(key=lambda x: x[1], reverse=True)

        return boosted_results

    def diversify_by_file(
        self, chunk_scores: list[tuple[CodeChunk, float]], max_per_file: int = 3
    ) -> list[tuple[CodeChunk, float]]:
        """按文件多样化结果"""
        file_counts = {}
        diversified_results = []

        for chunk, score in chunk_scores:
            file_path = chunk.relative_path
            file_count = file_counts.get(file_path, 0)

            if file_count < max_per_file:
                diversified_results.append((chunk, score))
                file_counts[file_path] = file_count + 1

        return diversified_results

    def format_results(
        self, chunk_scores: list[tuple[CodeChunk, float]], top_k: int = 10
    ) -> list[RetrievalResult]:
        """格式化输出结果"""
        results = []

        for chunk, score in chunk_scores[:top_k]:
            # 截断内容
            content = chunk.content

            result = RetrievalResult(
                file_path=chunk.relative_path,
                text=content,
                score=score,
                chunk_type=chunk.chunk_type,
                symbol_name=chunk.symbol_name or "",
                start_line=chunk.start_line,
                end_line=chunk.end_line,
            )

            results.append(result)

        return results

    def group_by_file(
        self, chunk_scores: list[tuple[CodeChunk, float]]
    ) -> dict[str, list[tuple[CodeChunk, float]]]:
        """按文件分组结果"""
        file_groups = {}

        for chunk, score in chunk_scores:
            file_path = chunk.relative_path
            if file_path not in file_groups:
                file_groups[file_path] = []
            file_groups[file_path].append((chunk, score))

        # 按分数排序每个文件的结果
        for file_path in file_groups:
            file_groups[file_path].sort(key=lambda x: x[1], reverse=True)

        return file_groups

    def get_top_files(
        self, chunk_scores: list[tuple[CodeChunk, float]], top_k: int = 5
    ) -> list[tuple[str, float, int]]:
        """获取得分最高的文件"""
        file_scores = {}
        file_counts = {}

        for chunk, score in chunk_scores:
            file_path = chunk.relative_path

            if file_path not in file_scores:
                file_scores[file_path] = 0.0
                file_counts[file_path] = 0

            file_scores[file_path] += score
            file_counts[file_path] += 1

        # 计算平均分数
        file_avg_scores = []
        for file_path, total_score in file_scores.items():
            count = file_counts[file_path]
            avg_score = total_score / count
            file_avg_scores.append((file_path, avg_score, count))

        # 按平均分数排序
        file_avg_scores.sort(key=lambda x: x[1], reverse=True)

        return file_avg_scores[:top_k]

    def filter_by_score_threshold(
        self, chunk_scores: list[tuple[CodeChunk, float]], threshold: float = 0.5
    ) -> list[tuple[CodeChunk, float]]:
        """根据分数阈值过滤结果"""
        return [(chunk, score) for chunk, score in chunk_scores if score >= threshold]

    def normalize_scores(
        self, chunk_scores: list[tuple[CodeChunk, float]]
    ) -> list[tuple[CodeChunk, float]]:
        """标准化分数到0-1范围"""
        if not chunk_scores:
            return []

        scores = [score for _, score in chunk_scores]
        min_score = min(scores)
        max_score = max(scores)

        if max_score == min_score:
            # 所有分数相同，返回原结果
            return chunk_scores

        normalized_results = []
        for chunk, score in chunk_scores:
            normalized_score = (score - min_score) / (max_score - min_score)
            normalized_results.append((chunk, normalized_score))

        return normalized_results

    def add_ranking_info(self, results: list[RetrievalResult]) -> list[RetrievalResult]:
        """添加排名信息到结果中"""
        for _, _ in enumerate(results):
            # 可以在这里添加排名相关的信息
            # 由于RetrievalResult没有rank字段，这里只是示例
            pass

        return results

    def get_result_summary(self, chunk_scores: list[tuple[CodeChunk, float]]) -> dict:
        """获取结果摘要"""
        if not chunk_scores:
            return {
                "total_results": 0,
                "avg_score": 0.0,
                "unique_files": 0,
                "language_distribution": {},
                "type_distribution": {},
            }

        scores = [score for _, score in chunk_scores]
        chunks = [chunk for chunk, _ in chunk_scores]

        # 语言分布
        language_dist = {}
        for chunk in chunks:
            lang = chunk.language
            language_dist[lang] = language_dist.get(lang, 0) + 1

        # 类型分布
        type_dist = {}
        for chunk in chunks:
            chunk_type = chunk.chunk_type
            type_dist[chunk_type] = type_dist.get(chunk_type, 0) + 1

        return {
            "total_results": len(chunk_scores),
            "avg_score": sum(scores) / len(scores),
            "max_score": max(scores),
            "min_score": min(scores),
            "unique_files": len({chunk.relative_path for chunk in chunks}),
            "language_distribution": language_dist,
            "type_distribution": type_dist,
        }
