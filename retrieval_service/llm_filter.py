"""
LLM筛选器
使用LLM评估代码分片与查询的相关性
"""

import asyncio
import logging

from config import RETRIEVAL_CONFIG, ModelType, model_manager
from utils.interfaces import ILLMFilter
from utils.models import CodeChunk

logger = logging.getLogger(__name__)


class LLMFilter(ILLMFilter):
    """LLM筛选器实现"""

    def __init__(self):
        self.filter_threshold = RETRIEVAL_CONFIG["llm_filter_threshold"]
        self.batch_size = 25  # 批量处理的分片数量

        # 筛选提示词模板
        self.filter_prompt_template = """你是一个专业的代码搜索评估专家。请严格按照评分标准，给出精确的相关性分数。

用户查询：{query}

代码片段信息：
- 文件路径：{file_path}
- 符号名称：{symbol_name}
- 代码类型：{chunk_type}

代码内容：
```{language}
{code_content}
```

评估维度（多维度综合评分）：

1. **功能相关性** (权重40%)
   - 代码是否直接实现查询所需的功能
   - 代码逻辑是否解决查询中的核心问题

2. **概念匹配度** (权重30%)
   - 变量名、函数名、类名、文件名是否包含查询关键词
   - 注释和文档是否与查询概念相关

3. **技术关联度** (权重20%)
   - 使用的技术栈、框架、库是否与查询相关
   - 实现方式是否符合查询的技术要求

4. **上下文重要性** (权重10%)
   - 代码路径在整个代码库中的重要程度
   - 是否为核心实现还是辅助代码

详细评分标准（精确到0.1）：

**0.9-1.0**: 完美匹配
- 直接解决查询问题的核心实现
- 函数名/类名完全对应查询意图
- 代码逻辑完整实现所需功能

**0.7-0.8**: 高度相关
- 实现查询的主要功能，但可能不够完整
- 包含重要的相关逻辑或算法
- 是解决问题的关键组件之一

**0.5-0.6**: 中等相关
- 部分实现相关功能
- 包含查询中的重要概念但不完整
- 对理解问题有一定帮助

**0.3-0.4**: 低度相关
- 仅包含查询的边缘概念
- 相关性较弱

**0.1-0.2**: 微弱相关
- 极少相关性

**0.0**: 完全无关
- 与查询完全无关

请综合以上四个维度进行评估，给出0.0-1.0之间的精确分数（精确到小数点后1位）

重要提示：
1. 要勇于给出极高分(0.9+)或极低分(0.2-)
2. 请直接输出分数并结束，不要输出任何其他内容

分数："""

    async def filter_chunks(
        self, query: str, chunks: list[CodeChunk]
    ) -> list[tuple[CodeChunk, float]]:
        """使用LLM筛选相关分片"""
        if not chunks:
            return []

        logger.info(f"开始LLM筛选，处理{len(chunks)}个分片...")

        filtered_results = []

        # 分批处理
        for i in range(0, len(chunks), self.batch_size):
            batch = chunks[i : i + self.batch_size]

            # 并发评估批次中的分片
            batch_tasks = [
                self._evaluate_chunk_relevance(query, chunk) for chunk in batch
            ]
            batch_scores = await asyncio.gather(*batch_tasks, return_exceptions=True)

            # 处理结果
            for chunk, score in zip(batch, batch_scores):
                if isinstance(score, Exception):
                    logger.warning(f"LLM筛选异常 {chunk.id}: {score}")
                    score = 0.0

                if score >= self.filter_threshold:
                    filtered_results.append((chunk, score))

            # # 避免API调用过于频繁
            # if i + self.batch_size < len(chunks):
            #     await asyncio.sleep(0.1)

        # 按分数排序
        filtered_results.sort(key=lambda x: x[1], reverse=True)

        # print(f"LLM筛选完成，保留{len(filtered_results)}个相关分片")

        return filtered_results

    async def _evaluate_chunk_relevance(self, query: str, chunk: CodeChunk) -> float:
        """评估单个分片的相关性"""
        try:
            # 构建提示词
            prompt = self._build_filter_prompt(query, chunk)

            # 调用LLM
            response = await model_manager.chat_completion(
                prompt, model_type=ModelType.NORMAL, max_tokens=10
            )

            # 解析分数
            score = self._parse_relevance_score(response)
            return score

        except Exception as e:
            logger.warning(f"评估分片相关性失败 {chunk.id}: {e}")
            # 返回中等分数作为降级
            return 0.5

    def _build_filter_prompt(self, query: str, chunk: CodeChunk) -> str:
        """构建筛选提示词"""
        # 截断代码内容
        code_content = chunk.content

        return self.filter_prompt_template.format(
            query=query,
            file_path=chunk.relative_path,
            symbol_name=chunk.symbol_name or "未知",
            chunk_type=chunk.chunk_type,
            insight=chunk.insight or "无描述",
            language=chunk.language,
            code_content=code_content,
        )

    def _parse_relevance_score(self, response: str) -> float:
        """解析相关性分数"""
        if not response:
            return 0.0

        # 清理响应
        cleaned_response = response.strip()

        # 尝试提取数字 - 改进正则表达式以匹配更多格式
        import re

        # 匹配各种数字格式：0.7, .7, 1.0, 1, 0.75等
        number_patterns = [
            r"分数[：:\s]*([01]?\.?\d*)",  # 匹配"分数：0.7"或"分数: 0.7"
            r"([01]?\.?\d+)\s*分",  # 匹配"0.7分"
            r"\b([01]?\.?\d+)\s*$",  # 匹配行末的数字
            r"^([01]?\.?\d+)\b",  # 匹配行首的数字
            r"([01]?\.?\d+)",  # 匹配任何位置的数字
        ]

        for pattern in number_patterns:
            matches = re.findall(pattern, cleaned_response)
            if matches:
                try:
                    score_str = matches[0].strip()
                    if score_str:
                        score = float(score_str)
                        # 确保分数在0-1范围内
                        return max(0.0, min(1.0, score))
                except (ValueError, IndexError):
                    continue

        # 如果无法解析数字，尝试解析评分等级关键词
        response_lower = cleaned_response.lower()

        # 高分关键词
        if any(
            word in response_lower
            for word in ["完美匹配", "完全相关", "直接解决", "0.9", "0.8", "高度相关"]
        ):
            return 0.8
        # 中高分关键词
        elif any(
            word in response_lower for word in ["主要功能", "关键组件", "0.7", "0.6"]
        ):
            return 0.6
        # 中等分关键词
        elif any(
            word in response_lower for word in ["中等相关", "部分实现", "0.5", "0.4"]
        ):
            return 0.4
        # 低分关键词
        elif any(
            word in response_lower
            for word in ["低度相关", "微弱相关", "边缘概念", "0.3", "0.2", "0.1"]
        ):
            return 0.2
        # 无关关键词
        elif any(
            word in response_lower for word in ["完全无关", "无关", "不相关", "0.0"]
        ):
            return 0.0

        # 如果仍无法解析，返回较低的默认分数（避免中等分数的偏向）
        return 0.1

    async def quick_filter(
        self, query: str, chunks: list[CodeChunk], top_k: int = 10
    ) -> list[tuple[CodeChunk, float]]:
        """快速筛选，只处理前面的分片"""
        # 限制处理的分片数量以提高速度
        limited_chunks = chunks[: min(len(chunks), top_k * 2)]

        return await self.filter_chunks(query, limited_chunks)

    def rule_based_filter(
        self, query: str, chunks: list[CodeChunk]
    ) -> list[tuple[CodeChunk, float]]:
        """基于规则的筛选（作为LLM筛选的降级方案）"""
        query_lower = query.lower()
        query_words = set(query_lower.split())

        filtered_results = []

        for chunk in chunks:
            score = self._calculate_rule_based_score(query_words, chunk)

            if score >= self.filter_threshold:
                filtered_results.append((chunk, score))

        # 按分数排序
        filtered_results.sort(key=lambda x: x[1], reverse=True)

        return filtered_results

    def _calculate_rule_based_score(self, query_words: set, chunk: CodeChunk) -> float:
        """计算基于规则的相关性分数"""
        score = 0.0

        # 1. 符号名称匹配
        if chunk.symbol_name:
            symbol_words = set(chunk.symbol_name.lower().split("_"))
            symbol_words.update(chunk.symbol_name.lower().split())

            matched_symbol_words = query_words & symbol_words
            if matched_symbol_words:
                score += len(matched_symbol_words) / len(query_words) * 0.4

        # 2. 关键词匹配
        if chunk.keywords:
            chunk_keywords = {kw.lower() for kw in chunk.keywords}
            matched_keywords = query_words & chunk_keywords

            if matched_keywords:
                score += len(matched_keywords) / len(query_words) * 0.3

        # 3. 洞察描述匹配
        if chunk.insight:
            insight_words = set(chunk.insight.lower().split())
            matched_insight_words = query_words & insight_words

            if matched_insight_words:
                score += len(matched_insight_words) / len(query_words) * 0.2

        # 4. 代码内容匹配
        content_words = set(chunk.content.lower().split())
        matched_content_words = query_words & content_words

        if matched_content_words:
            score += len(matched_content_words) / len(query_words) * 0.1

        return min(score, 1.0)

    async def hybrid_filter(
        self, query: str, chunks: list[CodeChunk], use_llm_ratio: float = 0.5
    ) -> list[tuple[CodeChunk, float]]:
        """混合筛选：结合LLM和规则筛选"""
        if not chunks:
            return []

        # 确定使用LLM筛选的分片数量
        llm_count = int(len(chunks) * use_llm_ratio)

        llm_chunks = chunks[:llm_count]
        rule_chunks = chunks[llm_count:]

        # 并发执行LLM筛选和规则筛选
        llm_task = self.filter_chunks(query, llm_chunks) if llm_chunks else []
        rule_results = self.rule_based_filter(query, rule_chunks) if rule_chunks else []

        if llm_chunks:
            llm_results = await llm_task
        else:
            llm_results = []

        # 合并结果
        all_results = llm_results + rule_results

        # 按分数排序
        all_results.sort(key=lambda x: x[1], reverse=True)

        return all_results

    def get_filter_stats(
        self,
        original_chunks: list[CodeChunk],
        filtered_results: list[tuple[CodeChunk, float]],
    ) -> dict:
        """获取筛选统计信息"""
        if not original_chunks:
            return {
                "original_count": 0,
                "filtered_count": 0,
                "filter_rate": 0.0,
                "avg_score": 0.0,
                "score_distribution": {},
            }

        scores = [score for _, score in filtered_results]

        # 分数分布
        score_ranges = {
            "0.9-1.0": 0,
            "0.8-0.9": 0,
            "0.7-0.8": 0,
            "0.6-0.7": 0,
            "0.5-0.6": 0,
            "0.0-0.5": 0,
        }

        for score in scores:
            if score >= 0.9:
                score_ranges["0.9-1.0"] += 1
            elif score >= 0.8:
                score_ranges["0.8-0.9"] += 1
            elif score >= 0.7:
                score_ranges["0.7-0.8"] += 1
            elif score >= 0.6:
                score_ranges["0.6-0.7"] += 1
            elif score >= 0.5:
                score_ranges["0.5-0.6"] += 1
            else:
                score_ranges["0.0-0.5"] += 1

        return {
            "original_count": len(original_chunks),
            "filtered_count": len(filtered_results),
            "filter_rate": len(filtered_results) / len(original_chunks),
            "avg_score": sum(scores) / len(scores) if scores else 0.0,
            "max_score": max(scores) if scores else 0.0,
            "min_score": min(scores) if scores else 0.0,
            "score_distribution": score_ranges,
        }
