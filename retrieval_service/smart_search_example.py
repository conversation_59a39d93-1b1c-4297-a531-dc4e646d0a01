#!/usr/bin/env python3
"""
智能检索引擎使用示例
展示如何使用重新设计的RetrievalEngine.search接口
"""

import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def demo_smart_search():
    """演示智能搜索功能"""

    # 注意：这里需要根据实际情况初始化RetrievalEngine
    # from index_manager.repo_index import RepoIndex
    # from retrieval_service.retrieval_engine import RetrievalEngine

    # repo_path = "/path/to/your/repo"
    # repo_index = RepoIndex.load(repo_path)
    # engine = RetrievalEngine(repo_index, repo_path)

    print("=== 智能检索引擎演示 ===\n")

    # 测试查询列表
    test_queries = [
        # 有明确符号实体的查询 - 优先使用symbol search
        "RetrievalEngine类的search方法",
        "SymbolRetriever.search_symbols函数",
        "QueryRewriter类",
        # 概念性查询 - 使用embedding search
        "如何实现坐标转换功能",
        "错误处理和异常管理",
        "配置文件解析逻辑",
        # 混合查询 - 智能选择引擎组合
        "search方法中的错误处理",
        "索引更新和刷新机制",
    ]

    for i, query in enumerate(test_queries, 1):
        print(f"查询 {i}: {query}")
        print("-" * 50)

        # 这里演示不同的搜索策略
        strategies = ["auto", "symbol_first", "embedding_first", "hybrid"]

        for strategy in strategies:
            print(f"策略: {strategy}")

            # 实际使用时的代码：
            # try:
            #     results = await engine.smart_search(
            #         query=query,
            #         top_k=5,
            #         strategy=strategy
            #     )
            #
            #     print(f"  结果数量: {len(results)}")
            #     for j, result in enumerate(results[:3], 1):
            #         print(f"    {j}. {result.file_path}:{result.start_line}")
            #         print(f"       {result.content[:100]}...")
            #
            # except Exception as e:
            #     print(f"  搜索失败: {e}")

            # 演示输出
            print(f"  [演示] 策略 {strategy} 会根据查询特点选择最佳引擎组合")

        print()

    # 展示统计信息
    print("=== 搜索统计信息 ===")
    # stats = engine.get_detailed_stats()
    # print(f"总搜索次数: {stats['search_stats']['total_searches']}")
    # print(f"平均响应时间: {stats['search_stats']['avg_response_time']:.2f}ms")
    # print(f"符号搜索使用率: {stats['search_stats']['symbol_search_rate']:.2%}")
    # print(f"引擎使用统计: {stats['search_stats']['engine_usage']}")

    print("[演示] 统计信息会显示各种引擎的使用情况和性能指标")


def explain_search_strategies():
    """解释不同的搜索策略"""

    print("=== 搜索策略说明 ===\n")

    strategies = {
        "auto": {
            "描述": "智能自动策略（推荐）",
            "工作原理": [
                "1. 检测查询中的符号实体（类名、函数名等）",
                "2. 如果有实体，优先使用symbol search",
                "3. 评估symbol search结果质量",
                "4. 如果结果不足或质量低，使用ripgrep兜底",
                "5. 如果仍不满足，使用embedding search补充",
                "6. 智能融合和排序所有结果",
            ],
            "适用场景": "所有类型的查询，系统会自动选择最佳策略",
        },
        "symbol_first": {
            "描述": "符号优先策略",
            "工作原理": [
                "1. 首先尝试符号检索",
                "2. 如果结果足够，直接返回",
                "3. 否则用embedding检索补充",
            ],
            "适用场景": "明确查找特定类、函数、变量的场景",
        },
        "embedding_first": {
            "描述": "语义优先策略",
            "工作原理": ["1. 直接使用embedding检索", "2. 可选择性补充符号检索"],
            "适用场景": "概念性查询、功能描述、问题解决方案查找",
        },
        "hybrid": {
            "描述": "混合并行策略",
            "工作原理": [
                "1. 并行执行所有检索引擎",
                "2. 智能融合所有结果",
                "3. 按相关性排序",
            ],
            "适用场景": "复杂查询、需要全面覆盖的场景",
        },
    }

    for strategy_name, info in strategies.items():
        print(f"策略: {strategy_name}")
        print(f"描述: {info['描述']}")
        print("工作原理:")
        for step in info["工作原理"]:
            print(f"  {step}")
        print(f"适用场景: {info['适用场景']}")
        print()


def show_quality_evaluation():
    """展示结果质量评估机制"""

    print("=== 结果质量评估机制 ===\n")

    print("质量评估指标:")
    print("1. 结果数量评分 (0-0.3)")
    print("   - ≥5个结果: 0.3分")
    print("   - ≥3个结果: 0.2分")
    print("   - ≥1个结果: 0.1分")
    print()

    print("2. 结果多样性评分 (0-0.3)")
    print("   - 基于涉及的不同文件数量")
    print("   - 多样性 = 唯一文件数 / 总结果数")
    print()

    print("3. 内容质量评分 (0-0.4)")
    print("   - 内容长度适中")
    print("   - 包含代码结构关键词(def, class, function等)")
    print()

    print("智能降级策略:")
    print("- 符号检索质量 < 0.6 → 启用ripgrep兜底")
    print("- 总体结果 < top_k/2 → 启用embedding补充")
    print("- 无符号实体 → 直接使用embedding检索")
    print()


if __name__ == "__main__":
    print("智能检索引擎重新设计完成！\n")

    explain_search_strategies()
    show_quality_evaluation()

    # 运行演示
    # asyncio.run(demo_smart_search())

    print("使用方法:")
    print("```python")
    print("# 基本使用")
    print("results = await engine.search(query, top_k=10)")
    print()
    print("# 指定策略")
    print("results = await engine.smart_search(query, top_k=10, strategy='auto')")
    print()
    print("# 带降级的搜索")
    print("results = await engine.search_with_fallback(query, top_k=10)")
    print("```")
