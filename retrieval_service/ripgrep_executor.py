"""
Ripgrep执行器
执行ripgrep搜索命令并解析结果
"""
import os
import logging
import re
import subprocess

# 尝试导入chardet，如果不可用则使用None
try:
    import chardet
except ImportError:
    chardet = None

from config import RETRIEVAL_CONFIG
from utils.interfaces import IRipgrepExecutor
from utils.models import SearchResult
from utils.sys_adapt import get_system_and_arch

logger = logging.getLogger(__name__)


def smart_decode(data: bytes) -> str:
    """智能解码二进制数据到字符串
    
    Args:
        data: 需要解码的二进制数据
        
    Returns:
        解码后的字符串，如果所有编码都失败则返回空字符串
    """
    if not data:
        return ""
    
    # 尝试常见的编码顺序
    encodings = ['utf-8', 'utf-16', 'gbk', 'gb2312', 'big5', 'cp1252', 'latin-1']
    
    # 首先尝试使用chardet自动检测编码（如果可用）
    if chardet:
        try:
            detected = chardet.detect(data)
            if detected and detected['encoding'] and detected['confidence'] > 0.7:
                try:
                    return data.decode(detected['encoding'])
                except (UnicodeDecodeError, LookupError):
                    logger.debug(f"检测到的编码 {detected['encoding']} 解码失败，尝试其他编码")
        except Exception:
            logger.debug("chardet编码检测失败，使用预定义编码列表")
    else:
        logger.debug("chardet库不可用，使用预定义编码列表")
    
    # 如果自动检测失败，逐个尝试预定义的编码
    for encoding in encodings:
        try:
            return data.decode(encoding)
        except (UnicodeDecodeError, LookupError):
            continue
    
    # 最后尝试使用错误处理策略
    try:
        return data.decode('utf-8', errors='replace')
    except Exception:
        try:
            return data.decode('latin-1', errors='replace')
        except Exception:
            logger.warning("所有编码解码都失败，返回空字符串")
            return ""


class RipgrepExecutor(IRipgrepExecutor):
    """Ripgrep执行器实现"""

    def __init__(self):
        self.timeout = RETRIEVAL_CONFIG["ripgrep_timeout"]
        self.max_results = RETRIEVAL_CONFIG["max_ripgrep_results"]

        # 检查ripgrep是否可用
        self.ripgrep_bin_loc = os.path.join("bin", "ripgrep")
        system, arch = get_system_and_arch()
        so_path_map = {
            ("linux", "arm64"): "ripgrep-aarch64-unknown-linux-gnu/rg",
            ("linux", "x64"): "ripgrep-x86_64-unknown-linux-musl/rg",
            ("mac", "arm64"): "ripgrep-aarch64-apple-darwin/rg",
            ("windows", "x64"): "ripgrep-x86_64-pc-windows-gnu\\rg.exe",
        }
        self.ripgrep_exec = os.path.join(self.ripgrep_bin_loc, so_path_map[(system, arch)]) if (system, arch) in so_path_map else "rg"
        self.ripgrep_available = self._check_ripgrep_availability()

    def _check_ripgrep_availability(self) -> bool:
        """检查ripgrep是否可用"""
        try:
            result = subprocess.run(
                [self.ripgrep_exec, "--version"], capture_output=True, text=False, timeout=5
            )
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError):
            logger.warning("ripgrep未安装或不可用，将使用grep降级")
            return False

    def execute_search(
        self,
        commands: list[str],
        repo_path: str,
        include_globs: list[str] | None = None,
        exclude_globs: list[str] | None = None,
    ) -> list[SearchResult]:
        """执行ripgrep搜索"""
        all_results = []

        for command in commands:
            if self.ripgrep_available:
                results = self._execute_ripgrep(
                    command, repo_path, include_globs, exclude_globs
                )
            else:
                results = self._execute_grep_fallback(
                    command, repo_path, include_globs, exclude_globs
                )

            all_results.extend(results)

            # 限制总结果数量
            if len(all_results) >= self.max_results:
                break

        # 去重并限制结果数量
        unique_results = self._deduplicate_results(all_results)
        return unique_results[: self.max_results]

    def _execute_ripgrep(
        self,
        pattern: str,
        repo_path: str,
        include_globs: list[str] | None = None,
        exclude_globs: list[str] | None = None,
    ) -> list[SearchResult]:
        """执行ripgrep命令"""
        try:
            # 构建ripgrep命令
            cmd = [
                self.ripgrep_exec,
                "--fixed-strings",  # 使用字面量匹配，避免简单关键字被当作正则
                "--line-number",  # 显示行号
                "--with-filename",  # 显示文件名
                "--no-heading",  # 不显示文件头
                "--context", "2",  # 显示上下文
                "--max-count", "10",  # 每个文件最多匹配10次
                pattern,
                repo_path,
            ]

            # 应用 include/exclude globs 限定扫描范围
            effective_includes = include_globs or [
                "**/*.py", "**/*.js", "**/*.ts", "**/*.java", "**/*.c", "**/*.cpp", "**/*.rs", "**/*.md", "**/*.ets"
            ]
            for g in effective_includes:
                cmd.extend(["--glob", g])
            if exclude_globs:
                for g in exclude_globs:
                    cmd.extend(["--glob", f"!{g}"])

            # 执行命令
            result = subprocess.run(
                cmd, capture_output=True, text=False, timeout=self.timeout, cwd=repo_path
            )

            if result.returncode == 0:
                stdout_text = smart_decode(result.stdout)
                return self.parse_ripgrep_output(stdout_text)
            else:
                stdout_text = smart_decode(result.stdout)
                stderr_text = smart_decode(result.stderr)
                if not stdout_text and not stderr_text:
                    return []
                logger.warning(f"ripgrep搜索失败，返回码: {result.returncode}")
                logger.warning(f"stderr: {stderr_text}")
                logger.warning(f"命令: {' '.join(cmd)}")
                return []

        except subprocess.TimeoutExpired:
            logger.warning(f"ripgrep搜索超时: {pattern}")
            return []
        except Exception as e:
            logger.error(f"ripgrep执行异常: {e}")
            return []

    def _convert_pattern_for_grep(self, pattern: str) -> str:
        """将ripgrep模式转换为grep兼容的模式"""
        # 转换常见的正则表达式语法
        converted = pattern
        converted = converted.replace(r"\s+", r"[[:space:]]+")  # 空白字符
        converted = converted.replace(r"\s*", r"[[:space:]]*")  # 可选空白字符
        converted = converted.replace(r"\s", r"[[:space:]]")  # 单个空白字符
        converted = converted.replace(r"\b", r"\<\>")  # 单词边界（部分支持）
        converted = converted.replace(r"\d+", r"[0-9]+")  # 数字
        converted = converted.replace(r"\d*", r"[0-9]*")  # 可选数字
        converted = converted.replace(r"\d", r"[0-9]")  # 单个数字
        converted = converted.replace(r"\w+", r"[a-zA-Z0-9_]+")  # 单词字符
        converted = converted.replace(r"\w*", r"[a-zA-Z0-9_]*")  # 可选单词字符
        converted = converted.replace(r"\w", r"[a-zA-Z0-9_]")  # 单个单词字符
        return converted

    def _execute_grep_fallback(
        self,
        pattern: str,
        repo_path: str,
        include_globs: list[str] | None = None,
        exclude_globs: list[str] | None = None,
    ) -> list[SearchResult]:
        """使用grep作为降级方案"""
        try:
            # 转换模式为grep兼容格式
            grep_pattern = self._convert_pattern_for_grep(pattern)
            logger.debug(f"原始模式: {pattern}, 转换后: {grep_pattern}")
            # 构建grep命令
            cmd = [
                "grep",
                "-E",  # 使用扩展正则表达式
                "-r",  # 递归搜索
                "-n",  # 显示行号
                "-H",  # 显示文件名
                "-A",
                "2",  # 显示后续2行
                "-B",
                "2",  # 显示前面2行
            ]

            # 应用 include/exclude globs（grep 使用 --include/--exclude）
            default_includes = [
                "*.py", "*.js", "*.ts", "*.java", "*.c", "*.cpp", "*.rs", "*.md", "*.ets"
            ]
            for inc in (include_globs or default_includes):
                cmd.append(f"--include={inc}")
            for exc in (exclude_globs or []):
                cmd.append(f"--exclude={exc}")

            cmd += [
                grep_pattern,
                repo_path,
            ]

            # 执行命令
            result = subprocess.run(
                cmd, capture_output=True, text=False, timeout=self.timeout, cwd=repo_path
            )

            if result.returncode == 0:
                stdout_text = smart_decode(result.stdout)
                return self._parse_grep_output(stdout_text, repo_path)
            else:
                stderr_text = smart_decode(result.stderr)
                logger.debug(
                    f"grep命令失败，返回码: {result.returncode}, stderr: {stderr_text}"
                )
                return []

        except subprocess.TimeoutExpired:
            logger.warning(f"grep搜索超时: {pattern}")
            return []
        except Exception as e:
            logger.error(f"grep执行异常: {e}")
            return []

    def parse_ripgrep_output(self, output: str) -> list[SearchResult]:
        """解析ripgrep输出"""
        results = []

        if not output or not output.strip():
            return results

        lines = output.strip().split("\n")
        current_context = []

        for line in lines:
            # ripgrep输出格式: file:line:content 或 file-line-content (上下文)
            if ":" in line:
                # 匹配行
                parts = line.split(":", 2)
                if len(parts) >= 3:
                    file_path = parts[0]
                    try:
                        line_number = int(parts[1])
                        matched_text = parts[2]

                        # 收集上下文
                        context = (
                            "\n".join(current_context[-2:]) if current_context else ""
                        )

                        result = SearchResult(
                            file_path=file_path,
                            line_number=line_number,
                            matched_text=matched_text,
                            context=context,
                        )
                        results.append(result)

                    except ValueError:
                        continue
            elif "-" in line:
                # 上下文行
                current_context.append(line)
                # 限制上下文长度
                if len(current_context) > 10:
                    current_context = current_context[-10:]

        return results

    def _parse_grep_output(self, output: str, repo_path: str) -> list[SearchResult]:
        """解析grep输出"""
        results = []

        if not output or not output.strip():
            return results

        lines = output.strip().split("\n")

        for line in lines:
            # grep输出格式: file:line:content
            if ":" in line:
                parts = line.split(":", 2)
                if len(parts) >= 3:
                    file_path = parts[0]
                    try:
                        line_number = int(parts[1])
                        matched_text = parts[2]

                        # 将绝对路径转换为相对路径
                        if file_path.startswith(repo_path):
                            file_path = file_path[len(repo_path) :].lstrip("/")

                        logger.debug(f"转换路径: {parts[0]} -> {file_path}")

                        result = SearchResult(
                            file_path=file_path,
                            line_number=line_number,
                            matched_text=matched_text,
                            context="",  # grep降级方案不提供上下文
                        )
                        results.append(result)

                    except ValueError:
                        continue

        return results

    def _deduplicate_results(self, results: list[SearchResult]) -> list[SearchResult]:
        """去重搜索结果"""
        seen = set()
        unique_results = []

        for result in results:
            # 使用文件路径和行号作为唯一标识
            key = (result.file_path, result.line_number)
            if key not in seen:
                seen.add(key)
                unique_results.append(result)

        return unique_results

    def search_with_patterns(
        self, patterns: list[str], repo_path: str
    ) -> dict[str, list[SearchResult]]:
        """使用多个模式搜索并返回分组结果"""
        pattern_results = {}

        for pattern in patterns:
            if self.ripgrep_available:
                results = self._execute_ripgrep(pattern, repo_path)
            else:
                results = self._execute_grep_fallback(pattern, repo_path)

            pattern_results[pattern] = results

        return pattern_results

    def get_search_stats(self, results: list[SearchResult]) -> dict:
        """获取搜索结果统计信息"""
        if not results:
            return {"total_results": 0, "unique_files": 0, "file_distribution": {}}

        file_counts = {}
        for result in results:
            file_counts[result.file_path] = file_counts.get(result.file_path, 0) + 1

        return {
            "total_results": len(results),
            "unique_files": len(file_counts),
            "file_distribution": file_counts,
            "avg_results_per_file": len(results) / len(file_counts)
            if file_counts
            else 0,
        }

    def validate_pattern(self, pattern: str) -> bool:
        """验证搜索模式是否有效"""
        try:
            # 尝试编译正则表达式
            re.compile(pattern)
            return True
        except re.error:
            return False
