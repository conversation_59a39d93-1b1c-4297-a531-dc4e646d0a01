"""
嵌入计算器
计算代码分片的嵌入向量
"""

import asyncio
import logging

from config import EMBEDDING_CONFIG, ModelType, model_manager
from utils.helpers import truncate_text
from utils.interfaces import IEmbeddingCalculator
from utils.models import CodeChunk

logger = logging.getLogger(__name__)


class EmbeddingCalculator(IEmbeddingCalculator):
    """嵌入计算器实现"""

    def __init__(self):
        self.batch_size = EMBEDDING_CONFIG["batch_size"]
        self.max_text_length = EMBEDDING_CONFIG["max_text_length"]
        self.embedding_dimension = EMBEDDING_CONFIG["embedding_dimension"]

    async def calculate_embedding(self, text: str) -> list[float]:
        """计算文本的嵌入向量（用于索引构建，稳态高重试）"""
        return await self._calculate_embedding_with_policy(text, max_retries=5, backoff_base=0.1)

    async def calculate_query_embedding(self, text: str) -> list[float]:
        """计算查询嵌入（低重试/快速失败，降低尾延迟）"""
        return await self._calculate_embedding_with_policy(text, max_retries=1, backoff_base=0.01)  # 进一步减少重试，加快失败响应

    async def _calculate_embedding_with_policy(self, text: str, max_retries: int, backoff_base: float) -> list[float]:
        once_max_len = self.max_text_length
        for attempt in range(max_retries):
            try:
                truncated_text = truncate_text(text, min(once_max_len, self.max_text_length))
                embedding = await model_manager.embedding(truncated_text)
                return embedding
            except Exception as e:
                if attempt < max_retries - 1:
                    if attempt == 0:
                        logger.warning(f"计算嵌入向量失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                        logger.warning(f"truncated_text: {truncated_text[:100]}")
                    try:
                        if "input must have less than" in str(e):
                            once_max_len = once_max_len // 2
                    except Exception as parse_err:
                        logger.debug(f"重试策略解析报错信息失败: {parse_err}")
                    await asyncio.sleep((2 ** attempt) * backoff_base)
                else:
                    logger.error(f"计算嵌入向量最终失败 (已重试 {max_retries} 次): {e}")
                    return [0.0] * self.embedding_dimension

    async def batch_calculate_embeddings(self, texts: list[str]) -> list[list[float]]:
        """批量计算嵌入向量"""
        embeddings: list[list[float]] = []
        total_texts = len(texts)

        # 分批处理（每批 self.batch_size 条，单次POST发送）
        for i in range(0, total_texts, self.batch_size):
            batch = texts[i : i + self.batch_size]

            progress = min(i + self.batch_size, total_texts)
            logger.debug(
                f"  嵌入计算进度: {progress}/{total_texts} ({progress / total_texts * 100:.1f}%)"
            )

            try:
                batch_embeddings = await model_manager.batch_embedding(batch, ModelType.EMBEDDING)
            except Exception as e:
                logger.warning(f"批量嵌入调用失败（将回退逐条计算）：{e}")
                # 回退策略：逐条计算，尽量避免丢失嵌入
                batch_embeddings = []
                for text in batch:
                    try:
                        emb = await self.calculate_embedding(text)
                    except Exception as ie:
                        logger.warning(f"逐条计算嵌入失败: {ie}")
                        emb = [0.0] * self.embedding_dimension
                    batch_embeddings.append(emb)

            # 安全兜底：若返回条数与输入不一致，做对齐处理
            if len(batch_embeddings) != len(batch):
                logger.warning(
                    f"批量嵌入返回数量异常: 期待 {len(batch)} 实际 {len(batch_embeddings)}，将进行对齐补齐"
                )
                fixed: list[list[float]] = []
                for idx in range(len(batch)):
                    if idx < len(batch_embeddings) and batch_embeddings[idx]:
                        fixed.append(batch_embeddings[idx])
                    else:
                        fixed.append([0.0] * self.embedding_dimension)
                batch_embeddings = fixed

            embeddings.extend(batch_embeddings)

            # 简单的节流，避免过快触发限流
            if i + self.batch_size < total_texts:
                await asyncio.sleep(0.05)

        return embeddings

    def prepare_text_for_embedding(self, chunk: CodeChunk) -> str:
        """为嵌入计算准备文本"""
        # 组合多种信息来构建嵌入文本
        text_parts = []

        # 1. 文件路径信息
        text_parts.append(f"文件: {chunk.relative_path}")

        # 2. 符号信息
        if chunk.symbol_name:
            text_parts.append(f"符号: {chunk.symbol_name}")

        # 3. 类型信息
        text_parts.append(f"类型: {chunk.chunk_type}")

        # 4. 洞察信息（如果有）# For cmp test
        # if chunk.insight:
        #     text_parts.append(f"功能: {chunk.insight}")

        # # 5. 关键词信息（如果有）
        # if chunk.keywords:
        #     keywords_text = ', '.join(chunk.keywords[:10])  # 限制关键词数量
        #     text_parts.append(f"关键词: {keywords_text}")

        # 6. 代码内容
        text_parts.append(f"内容: {chunk.content}")

        # 组合所有部分
        combined_text = "\n".join(text_parts)

        # 截断到最大长度
        return truncate_text(combined_text, self.max_text_length)

    async def calculate_chunk_embedding(self, chunk: CodeChunk) -> list[float]:
        """计算代码分片的嵌入向量"""
        text = self.prepare_text_for_embedding(chunk)
        return await self.calculate_embedding(text)

    async def update_chunk_embeddings(self, chunks: list[CodeChunk]) -> list[CodeChunk]:
        """更新代码分片的嵌入向量"""
        logger.debug(f"开始计算{len(chunks)}个代码分片的嵌入向量...")

        # 准备文本列表
        texts = [self.prepare_text_for_embedding(chunk) for chunk in chunks]

        # 按 batch_size=10 进行批量POST调用
        embeddings = await self.batch_calculate_embeddings(texts)

        # 更新分片的嵌入信息
        for chunk, embedding in zip(chunks, embeddings):
            chunk.embedding = embedding

        logger.debug(f"完成嵌入计算，成功处理{len(chunks)}个分片")
        return chunks

    def get_embedding_stats(self, chunks: list[CodeChunk]) -> dict:
        """获取嵌入向量统计信息"""
        embeddings_with_data = [
            chunk for chunk in chunks if chunk.embedding and any(chunk.embedding)
        ]
        zero_embeddings = len(chunks) - len(embeddings_with_data)

        stats = {
            "total_chunks": len(chunks),
            "embeddings_with_data": len(embeddings_with_data),
            "zero_embeddings": zero_embeddings,
            "embedding_dimension": self.embedding_dimension,
            "success_rate": len(embeddings_with_data) / len(chunks) if chunks else 0,
        }

        if embeddings_with_data:
            # 计算向量的平均长度（L2范数）
            vector_norms = []
            for chunk in embeddings_with_data:
                if chunk.embedding:
                    norm = sum(x * x for x in chunk.embedding) ** 0.5
                    vector_norms.append(norm)

            if vector_norms:
                stats["avg_vector_norm"] = sum(vector_norms) / len(vector_norms)
                stats["min_vector_norm"] = min(vector_norms)
                stats["max_vector_norm"] = max(vector_norms)

        return stats
