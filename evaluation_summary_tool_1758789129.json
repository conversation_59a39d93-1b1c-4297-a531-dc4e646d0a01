{"overall_metrics": {"path_matching": {"total_queries": 1517, "exact_match_count": 1060, "exact_match_rate": 0.6987475280158207, "avg_k": 5.431773236651286, "success_rate_at_1": 0.4126565589980224, "success_rate_at_3": 0.5978905735003296, "success_rate_at_5": 0.6591957811470006, "success_rate_at_k": 0.6591957811470006, "precision_at_1": 0.4126565589980224, "precision_at_3": 0.25038453087233503, "precision_at_5": 0.21349154032080722, "precision_at_k": 0.21349154032080722, "mrr_at_k": 0.515504964895209, "ndcg_at_k": 0.5473804736733242, "f1_score": 0.4126565589980224, "avg_query_time_ms": 2699.814061074958, "max_query_time_ms": 73142.21692085266, "min_query_time_ms": 1166.1510467529297}, "content_matching": {"total_queries": 1517, "exact_match_count": 1015, "exact_match_rate": 0.6690837178642056, "avg_k": 5.431773236651286, "success_rate_at_1": 0.36585365853658536, "success_rate_at_3": 0.5728411338167436, "success_rate_at_5": 0.6315095583388266, "success_rate_at_k": 0.6315095583388266, "precision_at_1": 0.36585365853658536, "precision_at_3": 0.2372006152493954, "precision_at_5": 0.20170292243462865, "precision_at_k": 0.20170292243462865, "mrr_at_k": 0.4765580039970287, "ndcg_at_k": 0.5115840826525564, "f1_score": 0.36585365853658536, "avg_query_time_ms": 2699.814061074958, "max_query_time_ms": 73142.21692085266, "min_query_time_ms": 1166.1510467529297}, "total_queries": 1517, "exact_match_count": 1060, "exact_match_rate": 0.6987475280158207, "avg_k": 5.431773236651286, "success_rate_at_1": 0.4126565589980224, "success_rate_at_3": 0.5978905735003296, "success_rate_at_5": 0.6591957811470006, "success_rate_at_k": 0.6591957811470006, "precision_at_1": 0.4126565589980224, "precision_at_3": 0.25038453087233503, "precision_at_5": 0.21349154032080722, "precision_at_k": 0.21349154032080722, "mrr_at_k": 0.515504964895209, "ndcg_at_k": 0.5473804736733242, "f1_score": 0.4126565589980224, "avg_query_time_ms": 2699.814061074958, "max_query_time_ms": 73142.21692085266, "min_query_time_ms": 1166.1510467529297}, "intent_metrics_overall": {"debugging": {"path_matching": {"total_queries": 287, "exact_match_count": 230, "exact_match_rate": 0.8013937282229965, "avg_k": 6.062717770034843, "success_rate_at_1": 0.4738675958188153, "success_rate_at_3": 0.7177700348432056, "success_rate_at_5": 0.7560975609756098, "success_rate_at_k": 0.7804878048780488, "precision_at_1": 0.4738675958188153, "precision_at_3": 0.28861788617886175, "precision_at_5": 0.23281068524970983, "precision_at_k": 0.00987224157955865, "mrr_at_k": 0.5985191637630661, "ndcg_at_k": 0.052347453011448886, "f1_score": 0.4738675958188153, "avg_query_time_ms": 2942.648660015146, "max_query_time_ms": 73142.21692085266, "min_query_time_ms": 1236.3340854644775}, "content_matching": {"total_queries": 287, "exact_match_count": 228, "exact_match_rate": 0.794425087108014, "avg_k": 6.062717770034843, "success_rate_at_1": 0.43902439024390244, "success_rate_at_3": 0.710801393728223, "success_rate_at_5": 0.7665505226480837, "success_rate_at_k": 0.7700348432055749, "precision_at_1": 0.43902439024390244, "precision_at_3": 0.2833914053426248, "precision_at_5": 0.23054587688734057, "precision_at_k": 0.010452961672473865, "mrr_at_k": 0.5780916431613294, "ndcg_at_k": 0.0504257077810635, "f1_score": 0.43902439024390244, "avg_query_time_ms": 2942.648660015146, "max_query_time_ms": 73142.21692085266, "min_query_time_ms": 1236.3340854644775}, "total_queries": 287, "exact_match_count": 230, "exact_match_rate": 0.8013937282229965, "avg_k": 6.062717770034843, "success_rate_at_1": 0.4738675958188153, "success_rate_at_3": 0.7177700348432056, "success_rate_at_5": 0.7560975609756098, "success_rate_at_k": 0.7804878048780488, "precision_at_1": 0.4738675958188153, "precision_at_3": 0.28861788617886175, "precision_at_5": 0.23281068524970983, "precision_at_k": 0.00987224157955865, "mrr_at_k": 0.5985191637630661, "ndcg_at_k": 0.052347453011448886, "f1_score": 0.4738675958188153, "avg_query_time_ms": 2942.648660015146, "max_query_time_ms": 73142.21692085266, "min_query_time_ms": 1236.3340854644775}, "understanding": {"path_matching": {"total_queries": 285, "exact_match_count": 245, "exact_match_rate": 0.8596491228070176, "avg_k": 6.161403508771929, "success_rate_at_1": 0.5789473684210527, "success_rate_at_3": 0.7719298245614035, "success_rate_at_5": 0.8350877192982457, "success_rate_at_k": 0.8456140350877193, "precision_at_1": 0.5789473684210527, "precision_at_3": 0.3017543859649122, "precision_at_5": 0.2439766081871349, "precision_at_k": 0.014035087719298239, "mrr_at_k": 0.6868170426065163, "ndcg_at_k": 0.06897816752597088, "f1_score": 0.5789473684210527, "avg_query_time_ms": 2461.02032661438, "max_query_time_ms": 34595.508098602295, "min_query_time_ms": 1268.4416770935059}, "content_matching": {"total_queries": 285, "exact_match_count": 242, "exact_match_rate": 0.8491228070175438, "avg_k": 6.161403508771929, "success_rate_at_1": 0.5333333333333333, "success_rate_at_3": 0.7578947368421053, "success_rate_at_5": 0.8245614035087719, "success_rate_at_k": 0.8385964912280702, "precision_at_1": 0.5333333333333333, "precision_at_3": 0.29473684210526313, "precision_at_5": 0.23953216374269046, "precision_at_k": 0.014035087719298239, "mrr_at_k": 0.6553007518796994, "ndcg_at_k": 0.06858115983205698, "f1_score": 0.5333333333333333, "avg_query_time_ms": 2461.02032661438, "max_query_time_ms": 34595.508098602295, "min_query_time_ms": 1268.4416770935059}, "total_queries": 285, "exact_match_count": 245, "exact_match_rate": 0.8596491228070176, "avg_k": 6.161403508771929, "success_rate_at_1": 0.5789473684210527, "success_rate_at_3": 0.7719298245614035, "success_rate_at_5": 0.8350877192982457, "success_rate_at_k": 0.8456140350877193, "precision_at_1": 0.5789473684210527, "precision_at_3": 0.3017543859649122, "precision_at_5": 0.2439766081871349, "precision_at_k": 0.014035087719298239, "mrr_at_k": 0.6868170426065163, "ndcg_at_k": 0.06897816752597088, "f1_score": 0.5789473684210527, "avg_query_time_ms": 2461.02032661438, "max_query_time_ms": 34595.508098602295, "min_query_time_ms": 1268.4416770935059}, "optimization": {"path_matching": {"total_queries": 229, "exact_match_count": 121, "exact_match_rate": 0.5283842794759825, "avg_k": 4.676855895196507, "success_rate_at_1": 0.29694323144104806, "success_rate_at_3": 0.45414847161572053, "success_rate_at_5": 0.5065502183406113, "success_rate_at_k": 0.5065502183406113, "precision_at_1": 0.29694323144104806, "precision_at_3": 0.19650655021834065, "precision_at_5": 0.17612809315866085, "precision_at_k": 0.17612809315866085, "mrr_at_k": 0.3793893394330076, "ndcg_at_k": 0.40878112343235523, "f1_score": 0.29694323144104806, "avg_query_time_ms": 2860.473697362508, "max_query_time_ms": 31215.895175933838, "min_query_time_ms": 1313.8408660888672}, "content_matching": {"total_queries": 229, "exact_match_count": 115, "exact_match_rate": 0.5021834061135371, "avg_k": 4.676855895196507, "success_rate_at_1": 0.26200873362445415, "success_rate_at_3": 0.4279475982532751, "success_rate_at_5": 0.47161572052401746, "success_rate_at_k": 0.47161572052401746, "precision_at_1": 0.26200873362445415, "precision_at_3": 0.18704512372634638, "precision_at_5": 0.16608442503639007, "precision_at_k": 0.16608442503639007, "mrr_at_k": 0.3461287863034589, "ndcg_at_k": 0.37407169496563303, "f1_score": 0.26200873362445415, "avg_query_time_ms": 2860.473697362508, "max_query_time_ms": 31215.895175933838, "min_query_time_ms": 1313.8408660888672}, "total_queries": 229, "exact_match_count": 121, "exact_match_rate": 0.5283842794759825, "avg_k": 4.676855895196507, "success_rate_at_1": 0.29694323144104806, "success_rate_at_3": 0.45414847161572053, "success_rate_at_5": 0.5065502183406113, "success_rate_at_k": 0.5065502183406113, "precision_at_1": 0.29694323144104806, "precision_at_3": 0.19650655021834065, "precision_at_5": 0.17612809315866085, "precision_at_k": 0.17612809315866085, "mrr_at_k": 0.3793893394330076, "ndcg_at_k": 0.40878112343235523, "f1_score": 0.29694323144104806, "avg_query_time_ms": 2860.473697362508, "max_query_time_ms": 31215.895175933838, "min_query_time_ms": 1313.8408660888672}, "implementation": {"path_matching": {"total_queries": 259, "exact_match_count": 174, "exact_match_rate": 0.6718146718146718, "avg_k": 5.718146718146718, "success_rate_at_1": 0.36293436293436293, "success_rate_at_3": 0.5212355212355212, "success_rate_at_5": 0.5984555984555985, "success_rate_at_k": 0.6254826254826255, "precision_at_1": 0.36293436293436293, "precision_at_3": 0.2142857142857144, "precision_at_5": 0.18429858429858445, "precision_at_k": 0.011583011583011579, "mrr_at_k": 0.4622433658147944, "ndcg_at_k": 0.05431091047540668, "f1_score": 0.36293436293436293, "avg_query_time_ms": 2372.915233884539, "max_query_time_ms": 37077.686071395874, "min_query_time_ms": 1227.0898818969727}, "content_matching": {"total_queries": 259, "exact_match_count": 162, "exact_match_rate": 0.6254826254826255, "avg_k": 5.718146718146718, "success_rate_at_1": 0.3127413127413127, "success_rate_at_3": 0.4864864864864865, "success_rate_at_5": 0.555984555984556, "success_rate_at_k": 0.5791505791505791, "precision_at_1": 0.3127413127413127, "precision_at_3": 0.19948519948519955, "precision_at_5": 0.17155727155727163, "precision_at_k": 0.010296010296010294, "mrr_at_k": 0.41295121652264516, "ndcg_at_k": 0.04784468072864873, "f1_score": 0.3127413127413127, "avg_query_time_ms": 2372.915233884539, "max_query_time_ms": 37077.686071395874, "min_query_time_ms": 1227.0898818969727}, "total_queries": 259, "exact_match_count": 174, "exact_match_rate": 0.6718146718146718, "avg_k": 5.718146718146718, "success_rate_at_1": 0.36293436293436293, "success_rate_at_3": 0.5212355212355212, "success_rate_at_5": 0.5984555984555985, "success_rate_at_k": 0.6254826254826255, "precision_at_1": 0.36293436293436293, "precision_at_3": 0.2142857142857144, "precision_at_5": 0.18429858429858445, "precision_at_k": 0.011583011583011579, "mrr_at_k": 0.4622433658147944, "ndcg_at_k": 0.05431091047540668, "f1_score": 0.36293436293436293, "avg_query_time_ms": 2372.915233884539, "max_query_time_ms": 37077.686071395874, "min_query_time_ms": 1227.0898818969727}, "testing": {"path_matching": {"total_queries": 235, "exact_match_count": 151, "exact_match_rate": 0.6425531914893617, "avg_k": 4.208510638297873, "success_rate_at_1": 0.3574468085106383, "success_rate_at_3": 0.5531914893617021, "success_rate_at_5": 0.6085106382978723, "success_rate_at_k": 0.6042553191489362, "precision_at_1": 0.3574468085106383, "precision_at_3": 0.26099290780141865, "precision_at_5": 0.23631205673758873, "precision_at_k": 0.02553191489361702, "mrr_at_k": 0.4614302600472813, "ndcg_at_k": 0.08563069201399874, "f1_score": 0.3574468085106383, "avg_query_time_ms": 2893.6348935391043, "max_query_time_ms": 38877.80404090881, "min_query_time_ms": 1228.945255279541}, "content_matching": {"total_queries": 235, "exact_match_count": 141, "exact_match_rate": 0.6, "avg_k": 4.208510638297873, "success_rate_at_1": 0.3191489361702128, "success_rate_at_3": 0.5276595744680851, "success_rate_at_5": 0.574468085106383, "success_rate_at_k": 0.5659574468085107, "precision_at_1": 0.3191489361702128, "precision_at_3": 0.24539007092198603, "precision_at_5": 0.2209219858156029, "precision_at_k": 0.026595744680851064, "mrr_at_k": 0.42494596420128333, "ndcg_at_k": 0.08592568538389919, "f1_score": 0.3191489361702128, "avg_query_time_ms": 2893.6348935391043, "max_query_time_ms": 38877.80404090881, "min_query_time_ms": 1228.945255279541}, "total_queries": 235, "exact_match_count": 151, "exact_match_rate": 0.6425531914893617, "avg_k": 4.208510638297873, "success_rate_at_1": 0.3574468085106383, "success_rate_at_3": 0.5531914893617021, "success_rate_at_5": 0.6085106382978723, "success_rate_at_k": 0.6042553191489362, "precision_at_1": 0.3574468085106383, "precision_at_3": 0.26099290780141865, "precision_at_5": 0.23631205673758873, "precision_at_k": 0.02553191489361702, "mrr_at_k": 0.4614302600472813, "ndcg_at_k": 0.08563069201399874, "f1_score": 0.3574468085106383, "avg_query_time_ms": 2893.6348935391043, "max_query_time_ms": 38877.80404090881, "min_query_time_ms": 1228.945255279541}, "integration": {"path_matching": {"total_queries": 222, "exact_match_count": 139, "exact_match_rate": 0.6261261261261262, "avg_k": 5.418918918918919, "success_rate_at_1": 0.35585585585585583, "success_rate_at_3": 0.5045045045045045, "success_rate_at_5": 0.5900900900900901, "success_rate_at_k": 0.5900900900900901, "precision_at_1": 0.35585585585585583, "precision_at_3": 0.2214714714714716, "precision_at_5": 0.19782282282282299, "precision_at_k": 0.19782282282282299, "mrr_at_k": 0.44804447304447304, "ndcg_at_k": 0.4796152192900836, "f1_score": 0.35585585585585583, "avg_query_time_ms": 2702.9245490426415, "max_query_time_ms": 38087.64696121216, "min_query_time_ms": 1166.1510467529297}, "content_matching": {"total_queries": 222, "exact_match_count": 127, "exact_match_rate": 0.5720720720720721, "avg_k": 5.418918918918919, "success_rate_at_1": 0.2747747747747748, "success_rate_at_3": 0.45495495495495497, "success_rate_at_5": 0.5225225225225225, "success_rate_at_k": 0.5225225225225225, "precision_at_1": 0.2747747747747748, "precision_at_3": 0.19069069069069072, "precision_at_5": 0.16741741741741736, "precision_at_k": 0.16741741741741736, "mrr_at_k": 0.3792131417131417, "ndcg_at_k": 0.4100810471643191, "f1_score": 0.2747747747747748, "avg_query_time_ms": 2702.9245490426415, "max_query_time_ms": 38087.64696121216, "min_query_time_ms": 1166.1510467529297}, "total_queries": 222, "exact_match_count": 139, "exact_match_rate": 0.6261261261261262, "avg_k": 5.418918918918919, "success_rate_at_1": 0.35585585585585583, "success_rate_at_3": 0.5045045045045045, "success_rate_at_5": 0.5900900900900901, "success_rate_at_k": 0.5900900900900901, "precision_at_1": 0.35585585585585583, "precision_at_3": 0.2214714714714716, "precision_at_5": 0.19782282282282299, "precision_at_k": 0.19782282282282299, "mrr_at_k": 0.44804447304447304, "ndcg_at_k": 0.4796152192900836, "f1_score": 0.35585585585585583, "avg_query_time_ms": 2702.9245490426415, "max_query_time_ms": 38087.64696121216, "min_query_time_ms": 1166.1510467529297}}, "repo_metrics": {"legado-Harmony": {"path_matching": {"total_queries": 581, "exact_match_count": 340, "exact_match_rate": 0.5851979345955249, "avg_k": 6.363166953528399, "success_rate_at_1": 0.2822719449225473, "success_rate_at_3": 0.4629948364888124, "success_rate_at_5": 0.5283993115318416, "success_rate_at_k": 0.5473321858864028, "precision_at_1": 0.2822719449225473, "precision_at_3": 0.1640849110728625, "precision_at_5": 0.12736660929432067, "precision_at_k": 0.00831899024670109, "mrr_at_k": 0.3857757560855666, "ndcg_at_k": 0.04030205046988959, "f1_score": 0.2822719449225473, "avg_query_time_ms": 3022.489234215216, "max_query_time_ms": 18224.411964416504, "min_query_time_ms": 1398.097038269043}, "content_matching": {"total_queries": 581, "exact_match_count": 334, "exact_match_rate": 0.5748709122203098, "avg_k": 6.363166953528399, "success_rate_at_1": 0.28055077452667815, "success_rate_at_3": 0.45611015490533563, "success_rate_at_5": 0.5197934595524957, "success_rate_at_k": 0.5421686746987951, "precision_at_1": 0.28055077452667815, "precision_at_3": 0.160929432013769, "precision_at_5": 0.1247848537005169, "precision_at_k": 0.008032128514056224, "mrr_at_k": 0.3808540283583311, "ndcg_at_k": 0.039441465271954995, "f1_score": 0.28055077452667815, "avg_query_time_ms": 3022.489234215216, "max_query_time_ms": 18224.411964416504, "min_query_time_ms": 1398.097038269043}, "total_queries": 581, "exact_match_count": 340, "exact_match_rate": 0.5851979345955249, "avg_k": 6.363166953528399, "success_rate_at_1": 0.2822719449225473, "success_rate_at_3": 0.4629948364888124, "success_rate_at_5": 0.5283993115318416, "success_rate_at_k": 0.5473321858864028, "precision_at_1": 0.2822719449225473, "precision_at_3": 0.1640849110728625, "precision_at_5": 0.12736660929432067, "precision_at_k": 0.00831899024670109, "mrr_at_k": 0.3857757560855666, "ndcg_at_k": 0.04030205046988959, "f1_score": 0.2822719449225473, "avg_query_time_ms": 3022.489234215216, "max_query_time_ms": 18224.411964416504, "min_query_time_ms": 1398.097038269043}, "HOme_App": {"path_matching": {"total_queries": 167, "exact_match_count": 121, "exact_match_rate": 0.7245508982035929, "avg_k": 5.425149700598802, "success_rate_at_1": 0.40718562874251496, "success_rate_at_3": 0.6047904191616766, "success_rate_at_5": 0.6646706586826348, "success_rate_at_k": 0.6646706586826348, "precision_at_1": 0.40718562874251496, "precision_at_3": 0.25149700598802405, "precision_at_5": 0.21167664670658676, "precision_at_k": 0.21167664670658676, "mrr_at_k": 0.5173391312612868, "ndcg_at_k": 0.5474540069054966, "f1_score": 0.40718562874251496, "avg_query_time_ms": 2011.629087482384, "max_query_time_ms": 6318.711996078491, "min_query_time_ms": 1227.9062271118164}, "content_matching": {"total_queries": 167, "exact_match_count": 126, "exact_match_rate": 0.7544910179640718, "avg_k": 5.425149700598802, "success_rate_at_1": 0.4311377245508982, "success_rate_at_3": 0.6706586826347305, "success_rate_at_5": 0.7245508982035929, "success_rate_at_k": 0.7245508982035929, "precision_at_1": 0.4311377245508982, "precision_at_3": 0.26646706586826363, "precision_at_5": 0.2161676646706586, "precision_at_k": 0.2161676646706586, "mrr_at_k": 0.5526257009789941, "ndcg_at_k": 0.5929213270025769, "f1_score": 0.4311377245508982, "avg_query_time_ms": 2011.629087482384, "max_query_time_ms": 6318.711996078491, "min_query_time_ms": 1227.9062271118164}, "total_queries": 167, "exact_match_count": 121, "exact_match_rate": 0.7245508982035929, "avg_k": 5.425149700598802, "success_rate_at_1": 0.40718562874251496, "success_rate_at_3": 0.6047904191616766, "success_rate_at_5": 0.6646706586826348, "success_rate_at_k": 0.6646706586826348, "precision_at_1": 0.40718562874251496, "precision_at_3": 0.25149700598802405, "precision_at_5": 0.21167664670658676, "precision_at_k": 0.21167664670658676, "mrr_at_k": 0.5173391312612868, "ndcg_at_k": 0.5474540069054966, "f1_score": 0.40718562874251496, "avg_query_time_ms": 2011.629087482384, "max_query_time_ms": 6318.711996078491, "min_query_time_ms": 1227.9062271118164}, "S1-Orange": {"path_matching": {"total_queries": 291, "exact_match_count": 214, "exact_match_rate": 0.7353951890034365, "avg_k": 5.4604810996563575, "success_rate_at_1": 0.5085910652920962, "success_rate_at_3": 0.6597938144329897, "success_rate_at_5": 0.7250859106529209, "success_rate_at_k": 0.7250859106529209, "precision_at_1": 0.5085910652920962, "precision_at_3": 0.26403207331042383, "precision_at_5": 0.21970217640320783, "precision_at_k": 0.21970217640320783, "mrr_at_k": 0.5917907598319969, "ndcg_at_k": 0.6241572816123742, "f1_score": 0.5085910652920962, "avg_query_time_ms": 3720.************, "max_query_time_ms": 73142.21692085266, "min_query_time_ms": 1380.0067901611328}, "content_matching": {"total_queries": 291, "exact_match_count": 193, "exact_match_rate": 0.6632302405498282, "avg_k": 5.4604810996563575, "success_rate_at_1": 0.35738831615120276, "success_rate_at_3": 0.570446735395189, "success_rate_at_5": 0.6288659793814433, "success_rate_at_k": 0.6288659793814433, "precision_at_1": 0.35738831615120276, "precision_at_3": 0.2227949599083623, "precision_at_5": 0.1859106529209624, "precision_at_k": 0.1859106529209624, "mrr_at_k": 0.46950990018000327, "ndcg_at_k": 0.5061709591926897, "f1_score": 0.35738831615120276, "avg_query_time_ms": 3720.************, "max_query_time_ms": 73142.21692085266, "min_query_time_ms": 1380.0067901611328}, "total_queries": 291, "exact_match_count": 214, "exact_match_rate": 0.7353951890034365, "avg_k": 5.4604810996563575, "success_rate_at_1": 0.5085910652920962, "success_rate_at_3": 0.6597938144329897, "success_rate_at_5": 0.7250859106529209, "success_rate_at_k": 0.7250859106529209, "precision_at_1": 0.5085910652920962, "precision_at_3": 0.26403207331042383, "precision_at_5": 0.21970217640320783, "precision_at_k": 0.21970217640320783, "mrr_at_k": 0.5917907598319969, "ndcg_at_k": 0.6241572816123742, "f1_score": 0.5085910652920962, "avg_query_time_ms": 3720.************, "max_query_time_ms": 73142.21692085266, "min_query_time_ms": 1380.0067901611328}, "interview-guide": {"path_matching": {"total_queries": 208, "exact_match_count": 167, "exact_match_rate": 0.8028846153846154, "avg_k": 4.278846153846154, "success_rate_at_1": 0.5096153846153846, "success_rate_at_3": 0.7355769230769231, "success_rate_at_5": 0.7884615384615384, "success_rate_at_k": 0.7740384615384616, "precision_at_1": 0.5096153846153846, "precision_at_3": 0.32772435897435925, "precision_at_5": 0.2895032051282055, "precision_at_k": 0.03485576923076923, "mrr_at_k": 0.6291723901098902, "ndcg_at_k": 0.1118756935618521, "f1_score": 0.5096153846153846, "avg_query_time_ms": 2028.6772136504833, "max_query_time_ms": 6803.************, "min_query_time_ms": 1166.1510467529297}, "content_matching": {"total_queries": 208, "exact_match_count": 162, "exact_match_rate": 0.7788461538461539, "avg_k": 4.278846153846154, "success_rate_at_1": 0.4230769230769231, "success_rate_at_3": 0.6682692307692307, "success_rate_at_5": 0.7548076923076923, "success_rate_at_k": 0.7259615384615384, "precision_at_1": 0.4230769230769231, "precision_at_3": 0.30048076923076955, "precision_at_5": 0.27540064102564127, "precision_at_k": 0.03485576923076923, "mrr_at_k": 0.5591117216117216, "ndcg_at_k": 0.11091293781426526, "f1_score": 0.4230769230769231, "avg_query_time_ms": 2028.6772136504833, "max_query_time_ms": 6803.************, "min_query_time_ms": 1166.1510467529297}, "total_queries": 208, "exact_match_count": 167, "exact_match_rate": 0.8028846153846154, "avg_k": 4.278846153846154, "success_rate_at_1": 0.5096153846153846, "success_rate_at_3": 0.7355769230769231, "success_rate_at_5": 0.7884615384615384, "success_rate_at_k": 0.7740384615384616, "precision_at_1": 0.5096153846153846, "precision_at_3": 0.32772435897435925, "precision_at_5": 0.2895032051282055, "precision_at_k": 0.03485576923076923, "mrr_at_k": 0.6291723901098902, "ndcg_at_k": 0.1118756935618521, "f1_score": 0.5096153846153846, "avg_query_time_ms": 2028.6772136504833, "max_query_time_ms": 6803.************, "min_query_time_ms": 1166.1510467529297}, "Wechat_ArkTs": {"path_matching": {"total_queries": 108, "exact_match_count": 83, "exact_match_rate": 0.7685185185185185, "avg_k": 3.6296296296296298, "success_rate_at_1": 0.5185185185185185, "success_rate_at_3": 0.6944444444444444, "success_rate_at_5": 0.75, "success_rate_at_k": 0.7407407407407407, "precision_at_1": 0.5185185185185185, "precision_at_3": 0.3549382716049382, "precision_at_5": 0.3311728395061729, "precision_at_k": 0.023148148148148147, "mrr_at_k": 0.6199074074074074, "ndcg_at_k": 0.07243949832210894, "f1_score": 0.5185185185185185, "avg_query_time_ms": 1911.29364128466, "max_query_time_ms": 9294.646978378296, "min_query_time_ms": 1236.3340854644775}, "content_matching": {"total_queries": 108, "exact_match_count": 84, "exact_match_rate": 0.7777777777777778, "avg_k": 3.6296296296296298, "success_rate_at_1": 0.5, "success_rate_at_3": 0.7314814814814815, "success_rate_at_5": 0.7685185185185185, "success_rate_at_k": 0.75, "precision_at_1": 0.5, "precision_at_3": 0.36111111111111105, "precision_at_5": 0.3287037037037038, "precision_at_k": 0.023148148148148147, "mrr_at_k": 0.6179012345679014, "ndcg_at_k": 0.07308138204365161, "f1_score": 0.5, "avg_query_time_ms": 1911.29364128466, "max_query_time_ms": 9294.646978378296, "min_query_time_ms": 1236.3340854644775}, "total_queries": 108, "exact_match_count": 83, "exact_match_rate": 0.7685185185185185, "avg_k": 3.6296296296296298, "success_rate_at_1": 0.5185185185185185, "success_rate_at_3": 0.6944444444444444, "success_rate_at_5": 0.75, "success_rate_at_k": 0.7407407407407407, "precision_at_1": 0.5185185185185185, "precision_at_3": 0.3549382716049382, "precision_at_5": 0.3311728395061729, "precision_at_k": 0.023148148148148147, "mrr_at_k": 0.6199074074074074, "ndcg_at_k": 0.07243949832210894, "f1_score": 0.5185185185185185, "avg_query_time_ms": 1911.29364128466, "max_query_time_ms": 9294.646978378296, "min_query_time_ms": 1236.3340854644775}, "Chatime": {"path_matching": {"total_queries": 139, "exact_match_count": 122, "exact_match_rate": 0.8776978417266187, "avg_k": 4.9784172661870505, "success_rate_at_1": 0.5323741007194245, "success_rate_at_3": 0.7553956834532374, "success_rate_at_5": 0.8129496402877698, "success_rate_at_k": 0.8129496402877698, "precision_at_1": 0.5323741007194245, "precision_at_3": 0.3681055155875305, "precision_at_5": 0.33477218225419725, "precision_at_k": 0.33477218225419725, "mrr_at_k": 0.6493576567317575, "ndcg_at_k": 0.6831086296482566, "f1_score": 0.5323741007194245, "avg_query_time_ms": 1822.8275158422457, "max_query_time_ms": 2763.6678218841553, "min_query_time_ms": 1290.4210090637207}, "content_matching": {"total_queries": 139, "exact_match_count": 103, "exact_match_rate": 0.7410071942446043, "avg_k": 4.9784172661870505, "success_rate_at_1": 0.460431654676259, "success_rate_at_3": 0.6906474820143885, "success_rate_at_5": 0.7122302158273381, "success_rate_at_k": 0.7122302158273381, "precision_at_1": 0.460431654676259, "precision_at_3": 0.34172661870503634, "precision_at_5": 0.3052757793764992, "precision_at_k": 0.3052757793764992, "mrr_at_k": 0.5648766700924972, "ndcg_at_k": 0.5992752390624876, "f1_score": 0.460431654676259, "avg_query_time_ms": 1822.8275158422457, "max_query_time_ms": 2763.6678218841553, "min_query_time_ms": 1290.4210090637207}, "total_queries": 139, "exact_match_count": 122, "exact_match_rate": 0.8776978417266187, "avg_k": 4.9784172661870505, "success_rate_at_1": 0.5323741007194245, "success_rate_at_3": 0.7553956834532374, "success_rate_at_5": 0.8129496402877698, "success_rate_at_k": 0.8129496402877698, "precision_at_1": 0.5323741007194245, "precision_at_3": 0.3681055155875305, "precision_at_5": 0.33477218225419725, "precision_at_k": 0.33477218225419725, "mrr_at_k": 0.6493576567317575, "ndcg_at_k": 0.6831086296482566, "f1_score": 0.5323741007194245, "avg_query_time_ms": 1822.8275158422457, "max_query_time_ms": 2763.6678218841553, "min_query_time_ms": 1290.4210090637207}, "oh-bill": {"path_matching": {"total_queries": 13, "exact_match_count": 9, "exact_match_rate": 0.6923076923076923, "avg_k": 2.230769230769231, "success_rate_at_1": 0.5384615384615384, "success_rate_at_3": 0.6923076923076923, "success_rate_at_5": 0.6923076923076923, "success_rate_at_k": 0.6923076923076923, "precision_at_1": 0.5384615384615384, "precision_at_3": 0.48717948717948717, "precision_at_5": 0.48717948717948717, "precision_at_k": 0.0, "mrr_at_k": 0.6153846153846154, "ndcg_at_k": 0.0, "f1_score": 0.5384615384615384, "avg_query_time_ms": 1688.1827574509841, "max_query_time_ms": 2840.304136276245, "min_query_time_ms": 1268.4416770935059}, "content_matching": {"total_queries": 13, "exact_match_count": 9, "exact_match_rate": 0.6923076923076923, "avg_k": 2.230769230769231, "success_rate_at_1": 0.5384615384615384, "success_rate_at_3": 0.6923076923076923, "success_rate_at_5": 0.6923076923076923, "success_rate_at_k": 0.6923076923076923, "precision_at_1": 0.5384615384615384, "precision_at_3": 0.48717948717948717, "precision_at_5": 0.48717948717948717, "precision_at_k": 0.0, "mrr_at_k": 0.6153846153846154, "ndcg_at_k": 0.0, "f1_score": 0.5384615384615384, "avg_query_time_ms": 1688.1827574509841, "max_query_time_ms": 2840.304136276245, "min_query_time_ms": 1268.4416770935059}, "total_queries": 13, "exact_match_count": 9, "exact_match_rate": 0.6923076923076923, "avg_k": 2.230769230769231, "success_rate_at_1": 0.5384615384615384, "success_rate_at_3": 0.6923076923076923, "success_rate_at_5": 0.6923076923076923, "success_rate_at_k": 0.6923076923076923, "precision_at_1": 0.5384615384615384, "precision_at_3": 0.48717948717948717, "precision_at_5": 0.48717948717948717, "precision_at_k": 0.0, "mrr_at_k": 0.6153846153846154, "ndcg_at_k": 0.0, "f1_score": 0.5384615384615384, "avg_query_time_ms": 1688.1827574509841, "max_query_time_ms": 2840.304136276245, "min_query_time_ms": 1268.4416770935059}, "music-gathering": {"path_matching": {"total_queries": 10, "exact_match_count": 4, "exact_match_rate": 0.4, "avg_k": 4.5, "success_rate_at_1": 0.3, "success_rate_at_3": 0.3, "success_rate_at_5": 0.4, "success_rate_at_k": 0.3, "precision_at_1": 0.3, "precision_at_3": 0.16666666666666666, "precision_at_5": 0.1733333333333333, "precision_at_k": 0.0, "mrr_at_k": 0.32, "ndcg_at_k": 0.0, "f1_score": 0.3, "avg_query_time_ms": 1713.3587837219238, "max_query_time_ms": 2183.00199508667, "min_query_time_ms": 1222.419261932373}, "content_matching": {"total_queries": 10, "exact_match_count": 4, "exact_match_rate": 0.4, "avg_k": 4.5, "success_rate_at_1": 0.3, "success_rate_at_3": 0.3, "success_rate_at_5": 0.4, "success_rate_at_k": 0.3, "precision_at_1": 0.3, "precision_at_3": 0.16666666666666666, "precision_at_5": 0.1733333333333333, "precision_at_k": 0.0, "mrr_at_k": 0.32, "ndcg_at_k": 0.0, "f1_score": 0.3, "avg_query_time_ms": 1713.3587837219238, "max_query_time_ms": 2183.00199508667, "min_query_time_ms": 1222.419261932373}, "total_queries": 10, "exact_match_count": 4, "exact_match_rate": 0.4, "avg_k": 4.5, "success_rate_at_1": 0.3, "success_rate_at_3": 0.3, "success_rate_at_5": 0.4, "success_rate_at_k": 0.3, "precision_at_1": 0.3, "precision_at_3": 0.16666666666666666, "precision_at_5": 0.1733333333333333, "precision_at_k": 0.0, "mrr_at_k": 0.32, "ndcg_at_k": 0.0, "f1_score": 0.3, "avg_query_time_ms": 1713.3587837219238, "max_query_time_ms": 2183.00199508667, "min_query_time_ms": 1222.419261932373}}, "intent_metrics_per_repo": {"legado-Harmony": {"debugging": {"path_matching": {"total_queries": 106, "exact_match_count": 75, "exact_match_rate": 0.7075471698113207, "avg_k": 7.084905660377358, "success_rate_at_1": 0.41509433962264153, "success_rate_at_3": 0.6226415094339622, "success_rate_at_5": 0.6792452830188679, "success_rate_at_k": 0.6886792452830188, "precision_at_1": 0.41509433962264153, "precision_at_3": 0.21698113207547157, "precision_at_5": 0.16163522012578602, "precision_at_k": 0.014824797843665763, "mrr_at_k": 0.5242924528301889, "ndcg_at_k": 0.08282681196178221, "f1_score": 0.41509433962264153, "avg_query_time_ms": 3650.9015987504204, "max_query_time_ms": 17685.20188331604, "min_query_time_ms": 1437.8440380096436}, "content_matching": {"total_queries": 106, "exact_match_count": 76, "exact_match_rate": 0.7169811320754716, "avg_k": 7.084905660377358, "success_rate_at_1": 0.42452830188679247, "success_rate_at_3": 0.6226415094339622, "success_rate_at_5": 0.6886792452830188, "success_rate_at_k": 0.6981132075471698, "precision_at_1": 0.42452830188679247, "precision_at_3": 0.21698113207547157, "precision_at_5": 0.16179245283018853, "precision_at_k": 0.013477088948787058, "mrr_at_k": 0.534748427672956, "ndcg_at_k": 0.06802187383039913, "f1_score": 0.42452830188679247, "avg_query_time_ms": 3650.9015987504204, "max_query_time_ms": 17685.20188331604, "min_query_time_ms": 1437.8440380096436}, "total_queries": 106, "exact_match_count": 75, "exact_match_rate": 0.7075471698113207, "avg_k": 7.084905660377358, "success_rate_at_1": 0.41509433962264153, "success_rate_at_3": 0.6226415094339622, "success_rate_at_5": 0.6792452830188679, "success_rate_at_k": 0.6886792452830188, "precision_at_1": 0.41509433962264153, "precision_at_3": 0.21698113207547157, "precision_at_5": 0.16163522012578602, "precision_at_k": 0.014824797843665763, "mrr_at_k": 0.5242924528301889, "ndcg_at_k": 0.08282681196178221, "f1_score": 0.41509433962264153, "avg_query_time_ms": 3650.9015987504204, "max_query_time_ms": 17685.20188331604, "min_query_time_ms": 1437.8440380096436}, "understanding": {"path_matching": {"total_queries": 103, "exact_match_count": 83, "exact_match_rate": 0.8058252427184466, "avg_k": 7.475728155339806, "success_rate_at_1": 0.47572815533980584, "success_rate_at_3": 0.6990291262135923, "success_rate_at_5": 0.7669902912621359, "success_rate_at_k": 0.7864077669902912, "precision_at_1": 0.47572815533980584, "precision_at_3": 0.23624595469255644, "precision_at_5": 0.1677993527508089, "precision_at_k": 0.008321775312066572, "mrr_at_k": 0.6023462783171522, "ndcg_at_k": 0.05825242718446602, "f1_score": 0.47572815533980584, "avg_query_time_ms": 2487.2232918600434, "max_query_time_ms": 10064.17202949524, "min_query_time_ms": 1398.467779159546}, "content_matching": {"total_queries": 103, "exact_match_count": 83, "exact_match_rate": 0.8058252427184466, "avg_k": 7.475728155339806, "success_rate_at_1": 0.47572815533980584, "success_rate_at_3": 0.7087378640776699, "success_rate_at_5": 0.7669902912621359, "success_rate_at_k": 0.8058252427184466, "precision_at_1": 0.47572815533980584, "precision_at_3": 0.23948220064724898, "precision_at_5": 0.16909385113268594, "precision_at_k": 0.008321775312066572, "mrr_at_k": 0.6034905224225613, "ndcg_at_k": 0.054669220908460756, "f1_score": 0.47572815533980584, "avg_query_time_ms": 2487.2232918600434, "max_query_time_ms": 10064.17202949524, "min_query_time_ms": 1398.467779159546}, "total_queries": 103, "exact_match_count": 83, "exact_match_rate": 0.8058252427184466, "avg_k": 7.475728155339806, "success_rate_at_1": 0.47572815533980584, "success_rate_at_3": 0.6990291262135923, "success_rate_at_5": 0.7669902912621359, "success_rate_at_k": 0.7864077669902912, "precision_at_1": 0.47572815533980584, "precision_at_3": 0.23624595469255644, "precision_at_5": 0.1677993527508089, "precision_at_k": 0.008321775312066572, "mrr_at_k": 0.6023462783171522, "ndcg_at_k": 0.05825242718446602, "f1_score": 0.47572815533980584, "avg_query_time_ms": 2487.2232918600434, "max_query_time_ms": 10064.17202949524, "min_query_time_ms": 1398.467779159546}, "optimization": {"path_matching": {"total_queries": 98, "exact_match_count": 35, "exact_match_rate": 0.35714285714285715, "avg_k": 5.244897959183674, "success_rate_at_1": 0.1326530612244898, "success_rate_at_3": 0.2755102040816326, "success_rate_at_5": 0.336734693877551, "success_rate_at_k": 0.336734693877551, "precision_at_1": 0.1326530612244898, "precision_at_3": 0.10034013605442177, "precision_at_5": 0.08656462585034017, "precision_at_k": 0.08656462585034017, "mrr_at_k": 0.21294946550048588, "ndcg_at_k": 0.24157385846898652, "f1_score": 0.1326530612244898, "avg_query_time_ms": 3518.7377808045367, "max_query_time_ms": 18224.411964416504, "min_query_time_ms": 1398.097038269043}, "content_matching": {"total_queries": 98, "exact_match_count": 36, "exact_match_rate": 0.3673469387755102, "avg_k": 5.244897959183674, "success_rate_at_1": 0.1326530612244898, "success_rate_at_3": 0.2857142857142857, "success_rate_at_5": 0.32653061224489793, "success_rate_at_k": 0.32653061224489793, "precision_at_1": 0.1326530612244898, "precision_at_3": 0.10374149659863947, "precision_at_5": 0.08452380952380954, "precision_at_k": 0.08452380952380954, "mrr_at_k": 0.21345966958211854, "ndcg_at_k": 0.23699774501103354, "f1_score": 0.1326530612244898, "avg_query_time_ms": 3518.7377808045367, "max_query_time_ms": 18224.411964416504, "min_query_time_ms": 1398.097038269043}, "total_queries": 98, "exact_match_count": 35, "exact_match_rate": 0.35714285714285715, "avg_k": 5.244897959183674, "success_rate_at_1": 0.1326530612244898, "success_rate_at_3": 0.2755102040816326, "success_rate_at_5": 0.336734693877551, "success_rate_at_k": 0.336734693877551, "precision_at_1": 0.1326530612244898, "precision_at_3": 0.10034013605442177, "precision_at_5": 0.08656462585034017, "precision_at_k": 0.08656462585034017, "mrr_at_k": 0.21294946550048588, "ndcg_at_k": 0.24157385846898652, "f1_score": 0.1326530612244898, "avg_query_time_ms": 3518.7377808045367, "max_query_time_ms": 18224.411964416504, "min_query_time_ms": 1398.097038269043}, "implementation": {"path_matching": {"total_queries": 98, "exact_match_count": 52, "exact_match_rate": 0.5306122448979592, "avg_k": 7.1020408163265305, "success_rate_at_1": 0.22448979591836735, "success_rate_at_3": 0.3469387755102041, "success_rate_at_5": 0.40816326530612246, "success_rate_at_k": 0.47959183673469385, "precision_at_1": 0.22448979591836735, "precision_at_3": 0.11734693877551022, "precision_at_5": 0.08809523809523812, "precision_at_k": 0.008746355685131194, "mrr_at_k": 0.3059321347586654, "ndcg_at_k": 0.03047061212944008, "f1_score": 0.22448979591836735, "avg_query_time_ms": 2545.5694344578956, "max_query_time_ms": 9883.882999420166, "min_query_time_ms": 1479.5398712158203}, "content_matching": {"total_queries": 98, "exact_match_count": 50, "exact_match_rate": 0.5102040816326531, "avg_k": 7.1020408163265305, "success_rate_at_1": 0.21428571428571427, "success_rate_at_3": 0.32653061224489793, "success_rate_at_5": 0.3979591836734694, "success_rate_at_k": 0.45918367346938777, "precision_at_1": 0.21428571428571427, "precision_at_3": 0.11054421768707484, "precision_at_5": 0.0860544217687075, "precision_at_k": 0.008746355685131194, "mrr_at_k": 0.28899821833495304, "ndcg_at_k": 0.03047061212944008, "f1_score": 0.21428571428571427, "avg_query_time_ms": 2545.5694344578956, "max_query_time_ms": 9883.882999420166, "min_query_time_ms": 1479.5398712158203}, "total_queries": 98, "exact_match_count": 52, "exact_match_rate": 0.5306122448979592, "avg_k": 7.1020408163265305, "success_rate_at_1": 0.22448979591836735, "success_rate_at_3": 0.3469387755102041, "success_rate_at_5": 0.40816326530612246, "success_rate_at_k": 0.47959183673469385, "precision_at_1": 0.22448979591836735, "precision_at_3": 0.11734693877551022, "precision_at_5": 0.08809523809523812, "precision_at_k": 0.008746355685131194, "mrr_at_k": 0.3059321347586654, "ndcg_at_k": 0.03047061212944008, "f1_score": 0.22448979591836735, "avg_query_time_ms": 2545.5694344578956, "max_query_time_ms": 9883.882999420166, "min_query_time_ms": 1479.5398712158203}, "testing": {"path_matching": {"total_queries": 92, "exact_match_count": 48, "exact_match_rate": 0.5217391304347826, "avg_k": 4.760869565217392, "success_rate_at_1": 0.20652173913043478, "success_rate_at_3": 0.42391304347826086, "success_rate_at_5": 0.4782608695652174, "success_rate_at_k": 0.4782608695652174, "precision_at_1": 0.20652173913043478, "precision_at_3": 0.16304347826086962, "precision_at_5": 0.13460144927536224, "precision_at_k": 0.13460144927536224, "mrr_at_k": 0.3205012077294686, "ndcg_at_k": 0.35570151992635285, "f1_score": 0.20652173913043478, "avg_query_time_ms": 2953.148315782132, "max_query_time_ms": 10405.457973480225, "min_query_time_ms": 1547.1539497375488}, "content_matching": {"total_queries": 92, "exact_match_count": 45, "exact_match_rate": 0.4891304347826087, "avg_k": 4.760869565217392, "success_rate_at_1": 0.20652173913043478, "success_rate_at_3": 0.391304347826087, "success_rate_at_5": 0.44565217391304346, "success_rate_at_k": 0.44565217391304346, "precision_at_1": 0.20652173913043478, "precision_at_3": 0.15036231884057977, "precision_at_5": 0.12536231884057966, "precision_at_k": 0.12536231884057966, "mrr_at_k": 0.30782004830917875, "ndcg_at_k": 0.337974022604924, "f1_score": 0.20652173913043478, "avg_query_time_ms": 2953.148315782132, "max_query_time_ms": 10405.457973480225, "min_query_time_ms": 1547.1539497375488}, "total_queries": 92, "exact_match_count": 48, "exact_match_rate": 0.5217391304347826, "avg_k": 4.760869565217392, "success_rate_at_1": 0.20652173913043478, "success_rate_at_3": 0.42391304347826086, "success_rate_at_5": 0.4782608695652174, "success_rate_at_k": 0.4782608695652174, "precision_at_1": 0.20652173913043478, "precision_at_3": 0.16304347826086962, "precision_at_5": 0.13460144927536224, "precision_at_k": 0.13460144927536224, "mrr_at_k": 0.3205012077294686, "ndcg_at_k": 0.35570151992635285, "f1_score": 0.20652173913043478, "avg_query_time_ms": 2953.148315782132, "max_query_time_ms": 10405.457973480225, "min_query_time_ms": 1547.1539497375488}, "integration": {"path_matching": {"total_queries": 84, "exact_match_count": 47, "exact_match_rate": 0.5595238095238095, "avg_k": 6.285714285714286, "success_rate_at_1": 0.20238095238095238, "success_rate_at_3": 0.36904761904761907, "success_rate_at_5": 0.4642857142857143, "success_rate_at_k": 0.4880952380952381, "precision_at_1": 0.20238095238095238, "precision_at_3": 0.1388888888888889, "precision_at_5": 0.12003968253968253, "precision_at_k": 0.011904761904761904, "mrr_at_k": 0.3116969009826152, "ndcg_at_k": 0.06264118460884423, "f1_score": 0.20238095238095238, "avg_query_time_ms": 2939.225290502821, "max_query_time_ms": 10328.846216201782, "min_query_time_ms": 1516.390085220337}, "content_matching": {"total_queries": 84, "exact_match_count": 44, "exact_match_rate": 0.5238095238095238, "avg_k": 6.285714285714286, "success_rate_at_1": 0.19047619047619047, "success_rate_at_3": 0.35714285714285715, "success_rate_at_5": 0.4523809523809524, "success_rate_at_k": 0.4642857142857143, "precision_at_1": 0.19047619047619047, "precision_at_3": 0.130952380952381, "precision_at_5": 0.11527777777777776, "precision_at_k": 0.011904761904761904, "mrr_at_k": 0.29610733182161747, "ndcg_at_k": 0.06264118460884423, "f1_score": 0.19047619047619047, "avg_query_time_ms": 2939.225290502821, "max_query_time_ms": 10328.846216201782, "min_query_time_ms": 1516.390085220337}, "total_queries": 84, "exact_match_count": 47, "exact_match_rate": 0.5595238095238095, "avg_k": 6.285714285714286, "success_rate_at_1": 0.20238095238095238, "success_rate_at_3": 0.36904761904761907, "success_rate_at_5": 0.4642857142857143, "success_rate_at_k": 0.4880952380952381, "precision_at_1": 0.20238095238095238, "precision_at_3": 0.1388888888888889, "precision_at_5": 0.12003968253968253, "precision_at_k": 0.011904761904761904, "mrr_at_k": 0.3116969009826152, "ndcg_at_k": 0.06264118460884423, "f1_score": 0.20238095238095238, "avg_query_time_ms": 2939.225290502821, "max_query_time_ms": 10328.846216201782, "min_query_time_ms": 1516.390085220337}}, "HOme_App": {"debugging": {"path_matching": {"total_queries": 36, "exact_match_count": 33, "exact_match_rate": 0.9166666666666666, "avg_k": 6.888888888888889, "success_rate_at_1": 0.4722222222222222, "success_rate_at_3": 0.75, "success_rate_at_5": 0.8055555555555556, "success_rate_at_k": 0.9166666666666666, "precision_at_1": 0.4722222222222222, "precision_at_3": 0.2962962962962963, "precision_at_5": 0.23101851851851857, "precision_at_k": 0.015873015873015872, "mrr_at_k": 0.6250000000000001, "ndcg_at_k": 0.07115573087902363, "f1_score": 0.4722222222222222, "avg_query_time_ms": 1958.914127614763, "max_query_time_ms": 2517.6849365234375, "min_query_time_ms": 1339.3988609313965}, "content_matching": {"total_queries": 36, "exact_match_count": 32, "exact_match_rate": 0.8888888888888888, "avg_k": 6.888888888888889, "success_rate_at_1": 0.5555555555555556, "success_rate_at_3": 0.8333333333333334, "success_rate_at_5": 0.8888888888888888, "success_rate_at_k": 0.8888888888888888, "precision_at_1": 0.5555555555555556, "precision_at_3": 0.3194444444444445, "precision_at_5": 0.23935185185185193, "precision_at_k": 0.015873015873015872, "mrr_at_k": 0.6898148148148147, "ndcg_at_k": 0.07115573087902363, "f1_score": 0.5555555555555556, "avg_query_time_ms": 1958.914127614763, "max_query_time_ms": 2517.6849365234375, "min_query_time_ms": 1339.3988609313965}, "total_queries": 36, "exact_match_count": 33, "exact_match_rate": 0.9166666666666666, "avg_k": 6.888888888888889, "success_rate_at_1": 0.4722222222222222, "success_rate_at_3": 0.75, "success_rate_at_5": 0.8055555555555556, "success_rate_at_k": 0.9166666666666666, "precision_at_1": 0.4722222222222222, "precision_at_3": 0.2962962962962963, "precision_at_5": 0.23101851851851857, "precision_at_k": 0.015873015873015872, "mrr_at_k": 0.6250000000000001, "ndcg_at_k": 0.07115573087902363, "f1_score": 0.4722222222222222, "avg_query_time_ms": 1958.914127614763, "max_query_time_ms": 2517.6849365234375, "min_query_time_ms": 1339.3988609313965}, "understanding": {"path_matching": {"total_queries": 35, "exact_match_count": 28, "exact_match_rate": 0.8, "avg_k": 6.085714285714285, "success_rate_at_1": 0.45714285714285713, "success_rate_at_3": 0.6857142857142857, "success_rate_at_5": 0.7428571428571429, "success_rate_at_k": 0.7714285714285715, "precision_at_1": 0.45714285714285713, "precision_at_3": 0.2571428571428571, "precision_at_5": 0.20428571428571432, "precision_at_k": 0.023809523809523808, "mrr_at_k": 0.583095238095238, "ndcg_at_k": 0.1090722675802267, "f1_score": 0.45714285714285713, "avg_query_time_ms": 2141.1077635628835, "max_query_time_ms": 6318.711996078491, "min_query_time_ms": 1402.2860527038574}, "content_matching": {"total_queries": 35, "exact_match_count": 30, "exact_match_rate": 0.8571428571428571, "avg_k": 6.085714285714285, "success_rate_at_1": 0.42857142857142855, "success_rate_at_3": 0.7142857142857143, "success_rate_at_5": 0.8, "success_rate_at_k": 0.8, "precision_at_1": 0.42857142857142855, "precision_at_3": 0.2666666666666666, "precision_at_5": 0.21571428571428575, "precision_at_k": 0.023809523809523808, "mrr_at_k": 0.5833673469387755, "ndcg_at_k": 0.1090722675802267, "f1_score": 0.42857142857142855, "avg_query_time_ms": 2141.1077635628835, "max_query_time_ms": 6318.711996078491, "min_query_time_ms": 1402.2860527038574}, "total_queries": 35, "exact_match_count": 28, "exact_match_rate": 0.8, "avg_k": 6.085714285714285, "success_rate_at_1": 0.45714285714285713, "success_rate_at_3": 0.6857142857142857, "success_rate_at_5": 0.7428571428571429, "success_rate_at_k": 0.7714285714285715, "precision_at_1": 0.45714285714285713, "precision_at_3": 0.2571428571428571, "precision_at_5": 0.20428571428571432, "precision_at_k": 0.023809523809523808, "mrr_at_k": 0.583095238095238, "ndcg_at_k": 0.1090722675802267, "f1_score": 0.45714285714285713, "avg_query_time_ms": 2141.1077635628835, "max_query_time_ms": 6318.711996078491, "min_query_time_ms": 1402.2860527038574}, "implementation": {"path_matching": {"total_queries": 27, "exact_match_count": 21, "exact_match_rate": 0.7777777777777778, "avg_k": 6.407407407407407, "success_rate_at_1": 0.2962962962962963, "success_rate_at_3": 0.5185185185185185, "success_rate_at_5": 0.6296296296296297, "success_rate_at_k": 0.7037037037037037, "precision_at_1": 0.2962962962962963, "precision_at_3": 0.228395061728395, "precision_at_5": 0.2049382716049383, "precision_at_k": 0.018518518518518517, "mrr_at_k": 0.4347148736037625, "ndcg_at_k": 0.06874841433733415, "f1_score": 0.2962962962962963, "avg_query_time_ms": 1855.1613401483608, "max_query_time_ms": 3405.050039291382, "min_query_time_ms": 1227.9062271118164}, "content_matching": {"total_queries": 27, "exact_match_count": 21, "exact_match_rate": 0.7777777777777778, "avg_k": 6.407407407407407, "success_rate_at_1": 0.37037037037037035, "success_rate_at_3": 0.6296296296296297, "success_rate_at_5": 0.6666666666666666, "success_rate_at_k": 0.7037037037037037, "precision_at_1": 0.37037037037037035, "precision_at_3": 0.24074074074074067, "precision_at_5": 0.1827160493827161, "precision_at_k": 0.018518518518518517, "mrr_at_k": 0.49829512051734276, "ndcg_at_k": 0.06874841433733415, "f1_score": 0.37037037037037035, "avg_query_time_ms": 1855.1613401483608, "max_query_time_ms": 3405.050039291382, "min_query_time_ms": 1227.9062271118164}, "total_queries": 27, "exact_match_count": 21, "exact_match_rate": 0.7777777777777778, "avg_k": 6.407407407407407, "success_rate_at_1": 0.2962962962962963, "success_rate_at_3": 0.5185185185185185, "success_rate_at_5": 0.6296296296296297, "success_rate_at_k": 0.7037037037037037, "precision_at_1": 0.2962962962962963, "precision_at_3": 0.228395061728395, "precision_at_5": 0.2049382716049383, "precision_at_k": 0.018518518518518517, "mrr_at_k": 0.4347148736037625, "ndcg_at_k": 0.06874841433733415, "f1_score": 0.2962962962962963, "avg_query_time_ms": 1855.1613401483608, "max_query_time_ms": 3405.050039291382, "min_query_time_ms": 1227.9062271118164}, "optimization": {"path_matching": {"total_queries": 24, "exact_match_count": 12, "exact_match_rate": 0.5, "avg_k": 3.3333333333333335, "success_rate_at_1": 0.3333333333333333, "success_rate_at_3": 0.4583333333333333, "success_rate_at_5": 0.5, "success_rate_at_k": 0.4583333333333333, "precision_at_1": 0.3333333333333333, "precision_at_3": 0.16666666666666666, "precision_at_5": 0.13819444444444445, "precision_at_k": 0.16666666666666666, "mrr_at_k": 0.3993055555555556, "ndcg_at_k": 0.40674414613095483, "f1_score": 0.3333333333333333, "avg_query_time_ms": 1954.763611157735, "max_query_time_ms": 2671.314239501953, "min_query_time_ms": 1392.7879333496094}, "content_matching": {"total_queries": 24, "exact_match_count": 12, "exact_match_rate": 0.5, "avg_k": 3.3333333333333335, "success_rate_at_1": 0.3333333333333333, "success_rate_at_3": 0.4583333333333333, "success_rate_at_5": 0.5, "success_rate_at_k": 0.4583333333333333, "precision_at_1": 0.3333333333333333, "precision_at_3": 0.16666666666666666, "precision_at_5": 0.13819444444444445, "precision_at_k": 0.16666666666666666, "mrr_at_k": 0.3993055555555556, "ndcg_at_k": 0.40674414613095483, "f1_score": 0.3333333333333333, "avg_query_time_ms": 1954.763611157735, "max_query_time_ms": 2671.314239501953, "min_query_time_ms": 1392.7879333496094}, "total_queries": 24, "exact_match_count": 12, "exact_match_rate": 0.5, "avg_k": 3.3333333333333335, "success_rate_at_1": 0.3333333333333333, "success_rate_at_3": 0.4583333333333333, "success_rate_at_5": 0.5, "success_rate_at_k": 0.4583333333333333, "precision_at_1": 0.3333333333333333, "precision_at_3": 0.16666666666666666, "precision_at_5": 0.13819444444444445, "precision_at_k": 0.16666666666666666, "mrr_at_k": 0.3993055555555556, "ndcg_at_k": 0.40674414613095483, "f1_score": 0.3333333333333333, "avg_query_time_ms": 1954.763611157735, "max_query_time_ms": 2671.314239501953, "min_query_time_ms": 1392.7879333496094}, "integration": {"path_matching": {"total_queries": 22, "exact_match_count": 14, "exact_match_rate": 0.6363636363636364, "avg_k": 4.954545454545454, "success_rate_at_1": 0.45454545454545453, "success_rate_at_3": 0.5909090909090909, "success_rate_at_5": 0.6363636363636364, "success_rate_at_k": 0.6363636363636364, "precision_at_1": 0.45454545454545453, "precision_at_3": 0.3181818181818181, "precision_at_5": 0.28712121212121217, "precision_at_k": 0.28712121212121217, "mrr_at_k": 0.5265151515151515, "ndcg_at_k": 0.5542061847825596, "f1_score": 0.45454545454545453, "avg_query_time_ms": 2183.6417046460238, "max_query_time_ms": 4497.570991516113, "min_query_time_ms": 1281.2771797180176}, "content_matching": {"total_queries": 22, "exact_match_count": 16, "exact_match_rate": 0.7272727272727273, "avg_k": 4.954545454545454, "success_rate_at_1": 0.4090909090909091, "success_rate_at_3": 0.6818181818181818, "success_rate_at_5": 0.7272727272727273, "success_rate_at_k": 0.7272727272727273, "precision_at_1": 0.4090909090909091, "precision_at_3": 0.3257575757575757, "precision_at_5": 0.2825757575757576, "precision_at_k": 0.2825757575757576, "mrr_at_k": 0.5492424242424243, "ndcg_at_k": 0.594787514815031, "f1_score": 0.4090909090909091, "avg_query_time_ms": 2183.6417046460238, "max_query_time_ms": 4497.570991516113, "min_query_time_ms": 1281.2771797180176}, "total_queries": 22, "exact_match_count": 14, "exact_match_rate": 0.6363636363636364, "avg_k": 4.954545454545454, "success_rate_at_1": 0.45454545454545453, "success_rate_at_3": 0.5909090909090909, "success_rate_at_5": 0.6363636363636364, "success_rate_at_k": 0.6363636363636364, "precision_at_1": 0.45454545454545453, "precision_at_3": 0.3181818181818181, "precision_at_5": 0.28712121212121217, "precision_at_k": 0.28712121212121217, "mrr_at_k": 0.5265151515151515, "ndcg_at_k": 0.5542061847825596, "f1_score": 0.45454545454545453, "avg_query_time_ms": 2183.6417046460238, "max_query_time_ms": 4497.570991516113, "min_query_time_ms": 1281.2771797180176}, "testing": {"path_matching": {"total_queries": 23, "exact_match_count": 13, "exact_match_rate": 0.5652173913043478, "avg_k": 3.608695652173913, "success_rate_at_1": 0.391304347826087, "success_rate_at_3": 0.5217391304347826, "success_rate_at_5": 0.5652173913043478, "success_rate_at_k": 0.5652173913043478, "precision_at_1": 0.391304347826087, "precision_at_3": 0.22463768115942026, "precision_at_5": 0.20507246376811594, "precision_at_k": 0.03260869565217391, "mrr_at_k": 0.46014492753623193, "ndcg_at_k": 0.10869565217391304, "f1_score": 0.391304347826087, "avg_query_time_ms": 1975.5903015966, "max_query_time_ms": 4012.1238231658936, "min_query_time_ms": 1319.6110725402832}, "content_matching": {"total_queries": 23, "exact_match_count": 15, "exact_match_rate": 0.6521739130434783, "avg_k": 3.608695652173913, "success_rate_at_1": 0.43478260869565216, "success_rate_at_3": 0.6086956521739131, "success_rate_at_5": 0.6521739130434783, "success_rate_at_k": 0.6521739130434783, "precision_at_1": 0.43478260869565216, "precision_at_3": 0.2608695652173913, "precision_at_5": 0.23768115942028986, "precision_at_k": 0.043478260869565216, "mrr_at_k": 0.5181159420289856, "ndcg_at_k": 0.13043478260869565, "f1_score": 0.43478260869565216, "avg_query_time_ms": 1975.5903015966, "max_query_time_ms": 4012.1238231658936, "min_query_time_ms": 1319.6110725402832}, "total_queries": 23, "exact_match_count": 13, "exact_match_rate": 0.5652173913043478, "avg_k": 3.608695652173913, "success_rate_at_1": 0.391304347826087, "success_rate_at_3": 0.5217391304347826, "success_rate_at_5": 0.5652173913043478, "success_rate_at_k": 0.5652173913043478, "precision_at_1": 0.391304347826087, "precision_at_3": 0.22463768115942026, "precision_at_5": 0.20507246376811594, "precision_at_k": 0.03260869565217391, "mrr_at_k": 0.46014492753623193, "ndcg_at_k": 0.10869565217391304, "f1_score": 0.391304347826087, "avg_query_time_ms": 1975.5903015966, "max_query_time_ms": 4012.1238231658936, "min_query_time_ms": 1319.6110725402832}}, "S1-Orange": {"implementation": {"path_matching": {"total_queries": 50, "exact_match_count": 39, "exact_match_rate": 0.78, "avg_k": 5.12, "success_rate_at_1": 0.6, "success_rate_at_3": 0.7, "success_rate_at_5": 0.78, "success_rate_at_k": 0.78, "precision_at_1": 0.6, "precision_at_3": 0.2733333333333334, "precision_at_5": 0.22566666666666663, "precision_at_k": 0.22566666666666663, "mrr_at_k": 0.6633333333333333, "ndcg_at_k": 0.692309909860159, "f1_score": 0.6, "avg_query_time_ms": 3131.627130508423, "max_query_time_ms": 37077.686071395874, "min_query_time_ms": 1457.442045211792}, "content_matching": {"total_queries": 50, "exact_match_count": 33, "exact_match_rate": 0.66, "avg_k": 5.12, "success_rate_at_1": 0.4, "success_rate_at_3": 0.58, "success_rate_at_5": 0.64, "success_rate_at_k": 0.64, "precision_at_1": 0.4, "precision_at_3": 0.23000000000000004, "precision_at_5": 0.1943333333333333, "precision_at_k": 0.1943333333333333, "mrr_at_k": 0.4978571428571428, "ndcg_at_k": 0.5315521639129785, "f1_score": 0.4, "avg_query_time_ms": 3131.627130508423, "max_query_time_ms": 37077.686071395874, "min_query_time_ms": 1457.442045211792}, "total_queries": 50, "exact_match_count": 39, "exact_match_rate": 0.78, "avg_k": 5.12, "success_rate_at_1": 0.6, "success_rate_at_3": 0.7, "success_rate_at_5": 0.78, "success_rate_at_k": 0.78, "precision_at_1": 0.6, "precision_at_3": 0.2733333333333334, "precision_at_5": 0.22566666666666663, "precision_at_k": 0.22566666666666663, "mrr_at_k": 0.6633333333333333, "ndcg_at_k": 0.692309909860159, "f1_score": 0.6, "avg_query_time_ms": 3131.627130508423, "max_query_time_ms": 37077.686071395874, "min_query_time_ms": 1457.442045211792}, "understanding": {"path_matching": {"total_queries": 52, "exact_match_count": 45, "exact_match_rate": 0.8653846153846154, "avg_k": 6.019230769230769, "success_rate_at_1": 0.6923076923076923, "success_rate_at_3": 0.7884615384615384, "success_rate_at_5": 0.8653846153846154, "success_rate_at_k": 0.8653846153846154, "precision_at_1": 0.6923076923076923, "precision_at_3": 0.28525641025641035, "precision_at_5": 0.22980769230769227, "precision_at_k": 0.016025641025641024, "mrr_at_k": 0.7522435897435897, "ndcg_at_k": 0.08653846153846154, "f1_score": 0.6923076923076923, "avg_query_time_ms": 3579.0986189475425, "max_query_time_ms": 34595.508098602295, "min_query_time_ms": 1391.5951251983643}, "content_matching": {"total_queries": 52, "exact_match_count": 43, "exact_match_rate": 0.8269230769230769, "avg_k": 6.019230769230769, "success_rate_at_1": 0.5576923076923077, "success_rate_at_3": 0.7115384615384616, "success_rate_at_5": 0.8076923076923077, "success_rate_at_k": 0.8269230769230769, "precision_at_1": 0.5576923076923077, "precision_at_3": 0.2596153846153847, "precision_at_5": 0.2182692307692307, "precision_at_k": 0.01282051282051282, "mrr_at_k": 0.653525641025641, "ndcg_at_k": 0.0698255721840665, "f1_score": 0.5576923076923077, "avg_query_time_ms": 3579.0986189475425, "max_query_time_ms": 34595.508098602295, "min_query_time_ms": 1391.5951251983643}, "total_queries": 52, "exact_match_count": 45, "exact_match_rate": 0.8653846153846154, "avg_k": 6.019230769230769, "success_rate_at_1": 0.6923076923076923, "success_rate_at_3": 0.7884615384615384, "success_rate_at_5": 0.8653846153846154, "success_rate_at_k": 0.8653846153846154, "precision_at_1": 0.6923076923076923, "precision_at_3": 0.28525641025641035, "precision_at_5": 0.22980769230769227, "precision_at_k": 0.016025641025641024, "mrr_at_k": 0.7522435897435897, "ndcg_at_k": 0.08653846153846154, "f1_score": 0.6923076923076923, "avg_query_time_ms": 3579.0986189475425, "max_query_time_ms": 34595.508098602295, "min_query_time_ms": 1391.5951251983643}, "debugging": {"path_matching": {"total_queries": 52, "exact_match_count": 40, "exact_match_rate": 0.7692307692307693, "avg_k": 6.288461538461538, "success_rate_at_1": 0.40384615384615385, "success_rate_at_3": 0.7307692307692307, "success_rate_at_5": 0.75, "success_rate_at_k": 0.75, "precision_at_1": 0.40384615384615385, "precision_at_3": 0.2660256410256411, "precision_at_5": 0.19262820512820514, "precision_at_k": 0.009615384615384616, "mrr_at_k": 0.5548076923076923, "ndcg_at_k": 0.04807692307692308, "f1_score": 0.40384615384615385, "avg_query_time_ms": 3893.35828102552, "max_query_time_ms": 73142.21692085266, "min_query_time_ms": 1406.2793254852295}, "content_matching": {"total_queries": 52, "exact_match_count": 39, "exact_match_rate": 0.75, "avg_k": 6.288461538461538, "success_rate_at_1": 0.2692307692307692, "success_rate_at_3": 0.6538461538461539, "success_rate_at_5": 0.6923076923076923, "success_rate_at_k": 0.6923076923076923, "precision_at_1": 0.2692307692307692, "precision_at_3": 0.24038461538461542, "precision_at_5": 0.18012820512820513, "precision_at_k": 0.01282051282051282, "mrr_at_k": 0.45422008547008547, "ndcg_at_k": 0.06021018756868188, "f1_score": 0.2692307692307692, "avg_query_time_ms": 3893.35828102552, "max_query_time_ms": 73142.21692085266, "min_query_time_ms": 1406.2793254852295}, "total_queries": 52, "exact_match_count": 40, "exact_match_rate": 0.7692307692307693, "avg_k": 6.288461538461538, "success_rate_at_1": 0.40384615384615385, "success_rate_at_3": 0.7307692307692307, "success_rate_at_5": 0.75, "success_rate_at_k": 0.75, "precision_at_1": 0.40384615384615385, "precision_at_3": 0.2660256410256411, "precision_at_5": 0.19262820512820514, "precision_at_k": 0.009615384615384616, "mrr_at_k": 0.5548076923076923, "ndcg_at_k": 0.04807692307692308, "f1_score": 0.40384615384615385, "avg_query_time_ms": 3893.35828102552, "max_query_time_ms": 73142.21692085266, "min_query_time_ms": 1406.2793254852295}, "optimization": {"path_matching": {"total_queries": 48, "exact_match_count": 30, "exact_match_rate": 0.625, "avg_k": 5.375, "success_rate_at_1": 0.3958333333333333, "success_rate_at_3": 0.5416666666666666, "success_rate_at_5": 0.5833333333333334, "success_rate_at_k": 0.5833333333333334, "precision_at_1": 0.3958333333333333, "precision_at_3": 0.2083333333333333, "precision_at_5": 0.17465277777777777, "precision_at_k": 0.17465277777777777, "mrr_at_k": 0.4710648148148148, "ndcg_at_k": 0.4948779661846075, "f1_score": 0.3958333333333333, "avg_query_time_ms": 2929.6385844548545, "max_query_time_ms": 31215.895175933838, "min_query_time_ms": 1431.4827919006348}, "content_matching": {"total_queries": 48, "exact_match_count": 27, "exact_match_rate": 0.5625, "avg_k": 5.375, "success_rate_at_1": 0.25, "success_rate_at_3": 0.4791666666666667, "success_rate_at_5": 0.5208333333333334, "success_rate_at_k": 0.5208333333333334, "precision_at_1": 0.25, "precision_at_3": 0.17361111111111108, "precision_at_5": 0.14548611111111112, "precision_at_k": 0.14548611111111112, "mrr_at_k": 0.3634259259259259, "ndcg_at_k": 0.398894409116157, "f1_score": 0.25, "avg_query_time_ms": 2929.6385844548545, "max_query_time_ms": 31215.895175933838, "min_query_time_ms": 1431.4827919006348}, "total_queries": 48, "exact_match_count": 30, "exact_match_rate": 0.625, "avg_k": 5.375, "success_rate_at_1": 0.3958333333333333, "success_rate_at_3": 0.5416666666666666, "success_rate_at_5": 0.5833333333333334, "success_rate_at_k": 0.5833333333333334, "precision_at_1": 0.3958333333333333, "precision_at_3": 0.2083333333333333, "precision_at_5": 0.17465277777777777, "precision_at_k": 0.17465277777777777, "mrr_at_k": 0.4710648148148148, "ndcg_at_k": 0.4948779661846075, "f1_score": 0.3958333333333333, "avg_query_time_ms": 2929.6385844548545, "max_query_time_ms": 31215.895175933838, "min_query_time_ms": 1431.4827919006348}, "testing": {"path_matching": {"total_queries": 46, "exact_match_count": 30, "exact_match_rate": 0.6521739130434783, "avg_k": 4.565217391304348, "success_rate_at_1": 0.45652173913043476, "success_rate_at_3": 0.5869565217391305, "success_rate_at_5": 0.6521739130434783, "success_rate_at_k": 0.6521739130434783, "precision_at_1": 0.45652173913043476, "precision_at_3": 0.29347826086956524, "precision_at_5": 0.26521739130434785, "precision_at_k": 0.26521739130434785, "mrr_at_k": 0.5297101449275362, "ndcg_at_k": 0.560259237775373, "f1_score": 0.45652173913043476, "avg_query_time_ms": 4833.258670309316, "max_query_time_ms": 38877.80404090881, "min_query_time_ms": 1380.0067901611328}, "content_matching": {"total_queries": 46, "exact_match_count": 27, "exact_match_rate": 0.5869565217391305, "avg_k": 4.565217391304348, "success_rate_at_1": 0.34782608695652173, "success_rate_at_3": 0.5, "success_rate_at_5": 0.5652173913043478, "success_rate_at_k": 0.5652173913043478, "precision_at_1": 0.34782608695652173, "precision_at_3": 0.24637681159420288, "precision_at_5": 0.22681159420289856, "precision_at_k": 0.22681159420289856, "mrr_at_k": 0.43027950310559004, "ndcg_at_k": 0.461480460583224, "f1_score": 0.34782608695652173, "avg_query_time_ms": 4833.258670309316, "max_query_time_ms": 38877.80404090881, "min_query_time_ms": 1380.0067901611328}, "total_queries": 46, "exact_match_count": 30, "exact_match_rate": 0.6521739130434783, "avg_k": 4.565217391304348, "success_rate_at_1": 0.45652173913043476, "success_rate_at_3": 0.5869565217391305, "success_rate_at_5": 0.6521739130434783, "success_rate_at_k": 0.6521739130434783, "precision_at_1": 0.45652173913043476, "precision_at_3": 0.29347826086956524, "precision_at_5": 0.26521739130434785, "precision_at_k": 0.26521739130434785, "mrr_at_k": 0.5297101449275362, "ndcg_at_k": 0.560259237775373, "f1_score": 0.45652173913043476, "avg_query_time_ms": 4833.258670309316, "max_query_time_ms": 38877.80404090881, "min_query_time_ms": 1380.0067901611328}, "integration": {"path_matching": {"total_queries": 43, "exact_match_count": 30, "exact_match_rate": 0.6976744186046512, "avg_k": 5.232558139534884, "success_rate_at_1": 0.4883720930232558, "success_rate_at_3": 0.5813953488372093, "success_rate_at_5": 0.6976744186046512, "success_rate_at_k": 0.6976744186046512, "precision_at_1": 0.4883720930232558, "precision_at_3": 0.25581395348837216, "precision_at_5": 0.2348837209302326, "precision_at_k": 0.2348837209302326, "mrr_at_k": 0.5604651162790697, "ndcg_at_k": 0.594084431445029, "f1_score": 0.4883720930232558, "avg_query_time_ms": 4062.1023178100586, "max_query_time_ms": 38087.64696121216, "min_query_time_ms": 1422.3041534423828}, "content_matching": {"total_queries": 43, "exact_match_count": 24, "exact_match_rate": 0.5581395348837209, "avg_k": 5.232558139534884, "success_rate_at_1": 0.3023255813953488, "success_rate_at_3": 0.46511627906976744, "success_rate_at_5": 0.5116279069767442, "success_rate_at_k": 0.5116279069767442, "precision_at_1": 0.3023255813953488, "precision_at_3": 0.1782945736434108, "precision_at_5": 0.14534883720930236, "precision_at_k": 0.14534883720930236, "mrr_at_k": 0.39289405684754525, "ndcg_at_k": 0.41795763100384237, "f1_score": 0.3023255813953488, "avg_query_time_ms": 4062.1023178100586, "max_query_time_ms": 38087.64696121216, "min_query_time_ms": 1422.3041534423828}, "total_queries": 43, "exact_match_count": 30, "exact_match_rate": 0.6976744186046512, "avg_k": 5.232558139534884, "success_rate_at_1": 0.4883720930232558, "success_rate_at_3": 0.5813953488372093, "success_rate_at_5": 0.6976744186046512, "success_rate_at_k": 0.6976744186046512, "precision_at_1": 0.4883720930232558, "precision_at_3": 0.25581395348837216, "precision_at_5": 0.2348837209302326, "precision_at_k": 0.2348837209302326, "mrr_at_k": 0.5604651162790697, "ndcg_at_k": 0.594084431445029, "f1_score": 0.4883720930232558, "avg_query_time_ms": 4062.1023178100586, "max_query_time_ms": 38087.64696121216, "min_query_time_ms": 1422.3041534423828}}, "interview-guide": {"understanding": {"path_matching": {"total_queries": 38, "exact_match_count": 36, "exact_match_rate": 0.9473684210526315, "avg_k": 4.973684210526316, "success_rate_at_1": 0.6578947368421053, "success_rate_at_3": 0.8421052631578947, "success_rate_at_5": 0.9473684210526315, "success_rate_at_k": 0.9473684210526315, "precision_at_1": 0.6578947368421053, "precision_at_3": 0.3552631578947369, "precision_at_5": 0.312719298245614, "precision_at_k": 0.312719298245614, "mrr_at_k": 0.7675438596491229, "ndcg_at_k": 0.8125619736881806, "f1_score": 0.6578947368421053, "avg_query_time_ms": 2097.96835246839, "max_query_time_ms": 6803.************, "min_query_time_ms": 1366.49489402771}, "content_matching": {"total_queries": 38, "exact_match_count": 36, "exact_match_rate": 0.9473684210526315, "avg_k": 4.973684210526316, "success_rate_at_1": 0.6052631578947368, "success_rate_at_3": 0.8157894736842105, "success_rate_at_5": 0.9473684210526315, "success_rate_at_k": 0.9473684210526315, "precision_at_1": 0.6052631578947368, "precision_at_3": 0.34649122807017546, "precision_at_5": 0.312719298245614, "precision_at_k": 0.312719298245614, "mrr_at_k": 0.7333333333333334, "ndcg_at_k": 0.78671414634097, "f1_score": 0.6052631578947368, "avg_query_time_ms": 2097.96835246839, "max_query_time_ms": 6803.************, "min_query_time_ms": 1366.49489402771}, "total_queries": 38, "exact_match_count": 36, "exact_match_rate": 0.9473684210526315, "avg_k": 4.973684210526316, "success_rate_at_1": 0.6578947368421053, "success_rate_at_3": 0.8421052631578947, "success_rate_at_5": 0.9473684210526315, "success_rate_at_k": 0.9473684210526315, "precision_at_1": 0.6578947368421053, "precision_at_3": 0.3552631578947369, "precision_at_5": 0.312719298245614, "precision_at_k": 0.312719298245614, "mrr_at_k": 0.7675438596491229, "ndcg_at_k": 0.8125619736881806, "f1_score": 0.6578947368421053, "avg_query_time_ms": 2097.96835246839, "max_query_time_ms": 6803.************, "min_query_time_ms": 1366.49489402771}, "implementation": {"path_matching": {"total_queries": 36, "exact_match_count": 24, "exact_match_rate": 0.6666666666666666, "avg_k": 4.138888888888889, "success_rate_at_1": 0.3611111111111111, "success_rate_at_3": 0.5833333333333334, "success_rate_at_5": 0.6388888888888888, "success_rate_at_k": 0.6111111111111112, "precision_at_1": 0.3611111111111111, "precision_at_3": 0.27314814814814814, "precision_at_5": 0.24166666666666664, "precision_at_k": 0.027777777777777776, "mrr_at_k": 0.4886904761904762, "ndcg_at_k": 0.1111111111111111, "f1_score": 0.3611111111111111, "avg_query_time_ms": 2014.8373908466763, "max_query_time_ms": 3219.2299365997314, "min_query_time_ms": 1281.1269760131836}, "content_matching": {"total_queries": 36, "exact_match_count": 24, "exact_match_rate": 0.6666666666666666, "avg_k": 4.138888888888889, "success_rate_at_1": 0.3611111111111111, "success_rate_at_3": 0.5555555555555556, "success_rate_at_5": 0.6388888888888888, "success_rate_at_k": 0.6111111111111112, "precision_at_1": 0.3611111111111111, "precision_at_3": 0.2638888888888889, "precision_at_5": 0.24166666666666664, "precision_at_k": 0.027777777777777776, "mrr_at_k": 0.47711640211640205, "ndcg_at_k": 0.10085915982142939, "f1_score": 0.3611111111111111, "avg_query_time_ms": 2014.8373908466763, "max_query_time_ms": 3219.2299365997314, "min_query_time_ms": 1281.1269760131836}, "total_queries": 36, "exact_match_count": 24, "exact_match_rate": 0.6666666666666666, "avg_k": 4.138888888888889, "success_rate_at_1": 0.3611111111111111, "success_rate_at_3": 0.5833333333333334, "success_rate_at_5": 0.6388888888888888, "success_rate_at_k": 0.6111111111111112, "precision_at_1": 0.3611111111111111, "precision_at_3": 0.27314814814814814, "precision_at_5": 0.24166666666666664, "precision_at_k": 0.027777777777777776, "mrr_at_k": 0.4886904761904762, "ndcg_at_k": 0.1111111111111111, "f1_score": 0.3611111111111111, "avg_query_time_ms": 2014.8373908466763, "max_query_time_ms": 3219.2299365997314, "min_query_time_ms": 1281.1269760131836}, "debugging": {"path_matching": {"total_queries": 40, "exact_match_count": 36, "exact_match_rate": 0.9, "avg_k": 4.425, "success_rate_at_1": 0.625, "success_rate_at_3": 0.85, "success_rate_at_5": 0.875, "success_rate_at_k": 0.875, "precision_at_1": 0.625, "precision_at_3": 0.3750000000000001, "precision_at_5": 0.31875, "precision_at_k": 0.04375, "mrr_at_k": 0.7385416666666667, "ndcg_at_k": 0.12981340163040772, "f1_score": 0.625, "avg_query_time_ms": 2184.3022167682648, "max_query_time_ms": 6798.121929168701, "min_query_time_ms": 1270.982027053833}, "content_matching": {"total_queries": 40, "exact_match_count": 36, "exact_match_rate": 0.9, "avg_k": 4.425, "success_rate_at_1": 0.525, "success_rate_at_3": 0.775, "success_rate_at_5": 0.875, "success_rate_at_k": 0.85, "precision_at_1": 0.525, "precision_at_3": 0.3500000000000001, "precision_at_5": 0.31875, "precision_at_k": 0.04375, "mrr_at_k": 0.6648214285714287, "ndcg_at_k": 0.12480707174295609, "f1_score": 0.525, "avg_query_time_ms": 2184.3022167682648, "max_query_time_ms": 6798.121929168701, "min_query_time_ms": 1270.982027053833}, "total_queries": 40, "exact_match_count": 36, "exact_match_rate": 0.9, "avg_k": 4.425, "success_rate_at_1": 0.625, "success_rate_at_3": 0.85, "success_rate_at_5": 0.875, "success_rate_at_k": 0.875, "precision_at_1": 0.625, "precision_at_3": 0.3750000000000001, "precision_at_5": 0.31875, "precision_at_k": 0.04375, "mrr_at_k": 0.7385416666666667, "ndcg_at_k": 0.12981340163040772, "f1_score": 0.625, "avg_query_time_ms": 2184.3022167682648, "max_query_time_ms": 6798.121929168701, "min_query_time_ms": 1270.982027053833}, "testing": {"path_matching": {"total_queries": 29, "exact_match_count": 25, "exact_match_rate": 0.8620689655172413, "avg_k": 3.0, "success_rate_at_1": 0.5172413793103449, "success_rate_at_3": 0.8275862068965517, "success_rate_at_5": 0.8620689655172413, "success_rate_at_k": 0.8275862068965517, "precision_at_1": 0.5172413793103449, "precision_at_3": 0.3793103448275862, "precision_at_5": 0.35229885057471266, "precision_at_k": 0.3793103448275862, "mrr_at_k": 0.675287356321839, "ndcg_at_k": 0.7085323458128159, "f1_score": 0.5172413793103449, "avg_query_time_ms": 2011.6451773150213, "max_query_time_ms": 3734.2798709869385, "min_query_time_ms": 1228.945255279541}, "content_matching": {"total_queries": 29, "exact_match_count": 24, "exact_match_rate": 0.8275862068965517, "avg_k": 3.0, "success_rate_at_1": 0.41379310344827586, "success_rate_at_3": 0.7931034482758621, "success_rate_at_5": 0.8275862068965517, "success_rate_at_k": 0.7931034482758621, "precision_at_1": 0.41379310344827586, "precision_at_3": 0.3620689655172413, "precision_at_5": 0.33505747126436775, "precision_at_k": 0.3620689655172413, "mrr_at_k": 0.6005747126436781, "ndcg_at_k": 0.6440816476601076, "f1_score": 0.41379310344827586, "avg_query_time_ms": 2011.6451773150213, "max_query_time_ms": 3734.2798709869385, "min_query_time_ms": 1228.945255279541}, "total_queries": 29, "exact_match_count": 25, "exact_match_rate": 0.8620689655172413, "avg_k": 3.0, "success_rate_at_1": 0.5172413793103449, "success_rate_at_3": 0.8275862068965517, "success_rate_at_5": 0.8620689655172413, "success_rate_at_k": 0.8275862068965517, "precision_at_1": 0.5172413793103449, "precision_at_3": 0.3793103448275862, "precision_at_5": 0.35229885057471266, "precision_at_k": 0.3793103448275862, "mrr_at_k": 0.675287356321839, "ndcg_at_k": 0.7085323458128159, "f1_score": 0.5172413793103449, "avg_query_time_ms": 2011.6451773150213, "max_query_time_ms": 3734.2798709869385, "min_query_time_ms": 1228.945255279541}, "integration": {"path_matching": {"total_queries": 35, "exact_match_count": 25, "exact_match_rate": 0.7142857142857143, "avg_k": 4.771428571428571, "success_rate_at_1": 0.5142857142857142, "success_rate_at_3": 0.6857142857142857, "success_rate_at_5": 0.7142857142857143, "success_rate_at_k": 0.7142857142857143, "precision_at_1": 0.5142857142857142, "precision_at_3": 0.30952380952380953, "precision_at_5": 0.25952380952380955, "precision_at_k": 0.25952380952380955, "mrr_at_k": 0.6009523809523809, "ndcg_at_k": 0.6297571878597665, "f1_score": 0.5142857142857142, "avg_query_time_ms": 1738.8015815189906, "max_query_time_ms": 2833.540916442871, "min_query_time_ms": 1166.1510467529297}, "content_matching": {"total_queries": 35, "exact_match_count": 23, "exact_match_rate": 0.6571428571428571, "avg_k": 4.771428571428571, "success_rate_at_1": 0.2571428571428571, "success_rate_at_3": 0.5142857142857142, "success_rate_at_5": 0.6, "success_rate_at_k": 0.6, "precision_at_1": 0.2571428571428571, "precision_at_3": 0.2333333333333333, "precision_at_5": 0.21380952380952387, "precision_at_k": 0.21380952380952387, "mrr_at_k": 0.4083673469387755, "ndcg_at_k": 0.45005200574611826, "f1_score": 0.2571428571428571, "avg_query_time_ms": 1738.8015815189906, "max_query_time_ms": 2833.540916442871, "min_query_time_ms": 1166.1510467529297}, "total_queries": 35, "exact_match_count": 25, "exact_match_rate": 0.7142857142857143, "avg_k": 4.771428571428571, "success_rate_at_1": 0.5142857142857142, "success_rate_at_3": 0.6857142857142857, "success_rate_at_5": 0.7142857142857143, "success_rate_at_k": 0.7142857142857143, "precision_at_1": 0.5142857142857142, "precision_at_3": 0.30952380952380953, "precision_at_5": 0.25952380952380955, "precision_at_k": 0.25952380952380955, "mrr_at_k": 0.6009523809523809, "ndcg_at_k": 0.6297571878597665, "f1_score": 0.5142857142857142, "avg_query_time_ms": 1738.8015815189906, "max_query_time_ms": 2833.540916442871, "min_query_time_ms": 1166.1510467529297}, "optimization": {"path_matching": {"total_queries": 30, "exact_match_count": 21, "exact_match_rate": 0.7, "avg_k": 4.033333333333333, "success_rate_at_1": 0.3333333333333333, "success_rate_at_3": 0.6, "success_rate_at_5": 0.6666666666666666, "success_rate_at_k": 0.6333333333333333, "precision_at_1": 0.3333333333333333, "precision_at_3": 0.26666666666666666, "precision_at_5": 0.25277777777777777, "precision_at_k": 0.041666666666666664, "mrr_at_k": 0.46499999999999997, "ndcg_at_k": 0.12308453550721028, "f1_score": 0.3333333333333333, "avg_query_time_ms": 2104.6687602996826, "max_query_time_ms": 3732.746124267578, "min_query_time_ms": 1336.8582725524902}, "content_matching": {"total_queries": 30, "exact_match_count": 19, "exact_match_rate": 0.6333333333333333, "avg_k": 4.033333333333333, "success_rate_at_1": 0.3333333333333333, "success_rate_at_3": 0.5333333333333333, "success_rate_at_5": 0.6, "success_rate_at_k": 0.5666666666666667, "precision_at_1": 0.3333333333333333, "precision_at_3": 0.23888888888888885, "precision_at_5": 0.225, "precision_at_k": 0.041666666666666664, "mrr_at_k": 0.43166666666666664, "ndcg_at_k": 0.13538687705482835, "f1_score": 0.3333333333333333, "avg_query_time_ms": 2104.6687602996826, "max_query_time_ms": 3732.746124267578, "min_query_time_ms": 1336.8582725524902}, "total_queries": 30, "exact_match_count": 21, "exact_match_rate": 0.7, "avg_k": 4.033333333333333, "success_rate_at_1": 0.3333333333333333, "success_rate_at_3": 0.6, "success_rate_at_5": 0.6666666666666666, "success_rate_at_k": 0.6333333333333333, "precision_at_1": 0.3333333333333333, "precision_at_3": 0.26666666666666666, "precision_at_5": 0.25277777777777777, "precision_at_k": 0.041666666666666664, "mrr_at_k": 0.46499999999999997, "ndcg_at_k": 0.12308453550721028, "f1_score": 0.3333333333333333, "avg_query_time_ms": 2104.6687602996826, "max_query_time_ms": 3732.746124267578, "min_query_time_ms": 1336.8582725524902}}, "Wechat_ArkTs": {"implementation": {"path_matching": {"total_queries": 18, "exact_match_count": 13, "exact_match_rate": 0.7222222222222222, "avg_k": 3.5555555555555554, "success_rate_at_1": 0.3333333333333333, "success_rate_at_3": 0.5555555555555556, "success_rate_at_5": 0.6666666666666666, "success_rate_at_k": 0.6111111111111112, "precision_at_1": 0.3333333333333333, "precision_at_3": 0.2685185185185185, "precision_at_5": 0.2592592592592593, "precision_at_k": 0.027777777777777776, "mrr_at_k": 0.47870370370370374, "ndcg_at_k": 0.09060720853174764, "f1_score": 0.3333333333333333, "avg_query_time_ms": 1798.8383505079482, "max_query_time_ms": 2551.9332885742188, "min_query_time_ms": 1326.4620304107666}, "content_matching": {"total_queries": 18, "exact_match_count": 13, "exact_match_rate": 0.7222222222222222, "avg_k": 3.5555555555555554, "success_rate_at_1": 0.3333333333333333, "success_rate_at_3": 0.5555555555555556, "success_rate_at_5": 0.6666666666666666, "success_rate_at_k": 0.5555555555555556, "precision_at_1": 0.3333333333333333, "precision_at_3": 0.2685185185185185, "precision_at_5": 0.2592592592592593, "precision_at_k": 0.027777777777777776, "mrr_at_k": 0.4759259259259259, "ndcg_at_k": 0.09060720853174764, "f1_score": 0.3333333333333333, "avg_query_time_ms": 1798.8383505079482, "max_query_time_ms": 2551.9332885742188, "min_query_time_ms": 1326.4620304107666}, "total_queries": 18, "exact_match_count": 13, "exact_match_rate": 0.7222222222222222, "avg_k": 3.5555555555555554, "success_rate_at_1": 0.3333333333333333, "success_rate_at_3": 0.5555555555555556, "success_rate_at_5": 0.6666666666666666, "success_rate_at_k": 0.6111111111111112, "precision_at_1": 0.3333333333333333, "precision_at_3": 0.2685185185185185, "precision_at_5": 0.2592592592592593, "precision_at_k": 0.027777777777777776, "mrr_at_k": 0.47870370370370374, "ndcg_at_k": 0.09060720853174764, "f1_score": 0.3333333333333333, "avg_query_time_ms": 1798.8383505079482, "max_query_time_ms": 2551.9332885742188, "min_query_time_ms": 1326.4620304107666}, "understanding": {"path_matching": {"total_queries": 25, "exact_match_count": 24, "exact_match_rate": 0.96, "avg_k": 4.44, "success_rate_at_1": 0.72, "success_rate_at_3": 0.96, "success_rate_at_5": 0.96, "success_rate_at_k": 0.96, "precision_at_1": 0.72, "precision_at_3": 0.4533333333333334, "precision_at_5": 0.3913333333333333, "precision_at_k": 0.01, "mrr_at_k": 0.84, "ndcg_at_k": 0.0252371901428583, "f1_score": 0.72, "avg_query_time_ms": 1899.0170192718506, "max_query_time_ms": 3229.4819355010986, "min_query_time_ms": 1299.5121479034424}, "content_matching": {"total_queries": 25, "exact_match_count": 25, "exact_match_rate": 1.0, "avg_k": 4.44, "success_rate_at_1": 0.72, "success_rate_at_3": 1.0, "success_rate_at_5": 1.0, "success_rate_at_k": 1.0, "precision_at_1": 0.72, "precision_at_3": 0.4666666666666668, "precision_at_5": 0.3993333333333333, "precision_at_k": 0.01, "mrr_at_k": 0.8533333333333333, "ndcg_at_k": 0.0252371901428583, "f1_score": 0.72, "avg_query_time_ms": 1899.0170192718506, "max_query_time_ms": 3229.4819355010986, "min_query_time_ms": 1299.5121479034424}, "total_queries": 25, "exact_match_count": 24, "exact_match_rate": 0.96, "avg_k": 4.44, "success_rate_at_1": 0.72, "success_rate_at_3": 0.96, "success_rate_at_5": 0.96, "success_rate_at_k": 0.96, "precision_at_1": 0.72, "precision_at_3": 0.4533333333333334, "precision_at_5": 0.3913333333333333, "precision_at_k": 0.01, "mrr_at_k": 0.84, "ndcg_at_k": 0.0252371901428583, "f1_score": 0.72, "avg_query_time_ms": 1899.0170192718506, "max_query_time_ms": 3229.4819355010986, "min_query_time_ms": 1299.5121479034424}, "debugging": {"path_matching": {"total_queries": 24, "exact_match_count": 19, "exact_match_rate": 0.7916666666666666, "avg_k": 4.291666666666667, "success_rate_at_1": 0.5, "success_rate_at_3": 0.7083333333333334, "success_rate_at_5": 0.75, "success_rate_at_k": 0.75, "precision_at_1": 0.5, "precision_at_3": 0.33333333333333326, "precision_at_5": 0.31041666666666673, "precision_at_k": 0.010416666666666666, "mrr_at_k": 0.6215277777777778, "ndcg_at_k": 0.041666666666666664, "f1_score": 0.5, "avg_query_time_ms": 1763.1975015004475, "max_query_time_ms": 2501.7600059509277, "min_query_time_ms": 1236.3340854644775}, "content_matching": {"total_queries": 24, "exact_match_count": 20, "exact_match_rate": 0.8333333333333334, "avg_k": 4.291666666666667, "success_rate_at_1": 0.4583333333333333, "success_rate_at_3": 0.7916666666666666, "success_rate_at_5": 0.8333333333333334, "success_rate_at_k": 0.8333333333333334, "precision_at_1": 0.4583333333333333, "precision_at_3": 0.3611111111111111, "precision_at_5": 0.33263888888888893, "precision_at_k": 0.010416666666666666, "mrr_at_k": 0.6284722222222222, "ndcg_at_k": 0.041666666666666664, "f1_score": 0.4583333333333333, "avg_query_time_ms": 1763.1975015004475, "max_query_time_ms": 2501.7600059509277, "min_query_time_ms": 1236.3340854644775}, "total_queries": 24, "exact_match_count": 19, "exact_match_rate": 0.7916666666666666, "avg_k": 4.291666666666667, "success_rate_at_1": 0.5, "success_rate_at_3": 0.7083333333333334, "success_rate_at_5": 0.75, "success_rate_at_k": 0.75, "precision_at_1": 0.5, "precision_at_3": 0.33333333333333326, "precision_at_5": 0.31041666666666673, "precision_at_k": 0.010416666666666666, "mrr_at_k": 0.6215277777777778, "ndcg_at_k": 0.041666666666666664, "f1_score": 0.5, "avg_query_time_ms": 1763.1975015004475, "max_query_time_ms": 2501.7600059509277, "min_query_time_ms": 1236.3340854644775}, "testing": {"path_matching": {"total_queries": 19, "exact_match_count": 14, "exact_match_rate": 0.7368421052631579, "avg_k": 2.789473684210526, "success_rate_at_1": 0.42105263157894735, "success_rate_at_3": 0.5789473684210527, "success_rate_at_5": 0.7368421052631579, "success_rate_at_k": 0.5789473684210527, "precision_at_1": 0.42105263157894735, "precision_at_3": 0.3508771929824561, "precision_at_5": 0.3649122807017544, "precision_at_k": 0.3508771929824561, "mrr_at_k": 0.5219298245614035, "ndcg_at_k": 0.5068910396616557, "f1_score": 0.42105263157894735, "avg_query_time_ms": 1836.0539988467567, "max_query_time_ms": 2736.298084259033, "min_query_time_ms": 1292.3619747161865}, "content_matching": {"total_queries": 19, "exact_match_count": 13, "exact_match_rate": 0.6842105263157895, "avg_k": 2.789473684210526, "success_rate_at_1": 0.3684210526315789, "success_rate_at_3": 0.631578947368421, "success_rate_at_5": 0.6842105263157895, "success_rate_at_k": 0.631578947368421, "precision_at_1": 0.3684210526315789, "precision_at_3": 0.3333333333333333, "precision_at_5": 0.312280701754386, "precision_at_k": 0.3333333333333333, "mrr_at_k": 0.4868421052631579, "ndcg_at_k": 0.5137820793233113, "f1_score": 0.3684210526315789, "avg_query_time_ms": 1836.0539988467567, "max_query_time_ms": 2736.298084259033, "min_query_time_ms": 1292.3619747161865}, "total_queries": 19, "exact_match_count": 14, "exact_match_rate": 0.7368421052631579, "avg_k": 2.789473684210526, "success_rate_at_1": 0.42105263157894735, "success_rate_at_3": 0.5789473684210527, "success_rate_at_5": 0.7368421052631579, "success_rate_at_k": 0.5789473684210527, "precision_at_1": 0.42105263157894735, "precision_at_3": 0.3508771929824561, "precision_at_5": 0.3649122807017544, "precision_at_k": 0.3508771929824561, "mrr_at_k": 0.5219298245614035, "ndcg_at_k": 0.5068910396616557, "f1_score": 0.42105263157894735, "avg_query_time_ms": 1836.0539988467567, "max_query_time_ms": 2736.298084259033, "min_query_time_ms": 1292.3619747161865}, "optimization": {"path_matching": {"total_queries": 10, "exact_match_count": 6, "exact_match_rate": 0.6, "avg_k": 2.8, "success_rate_at_1": 0.6, "success_rate_at_3": 0.6, "success_rate_at_5": 0.6, "success_rate_at_k": 0.6, "precision_at_1": 0.6, "precision_at_3": 0.36666666666666664, "precision_at_5": 0.35333333333333333, "precision_at_k": 0.36666666666666664, "mrr_at_k": 0.6, "ndcg_at_k": 0.6, "f1_score": 0.6, "avg_query_time_ms": 2590.5890941619873, "max_query_time_ms": 9294.646978378296, "min_query_time_ms": 1384.2041492462158}, "content_matching": {"total_queries": 10, "exact_match_count": 6, "exact_match_rate": 0.6, "avg_k": 2.8, "success_rate_at_1": 0.6, "success_rate_at_3": 0.6, "success_rate_at_5": 0.6, "success_rate_at_k": 0.6, "precision_at_1": 0.6, "precision_at_3": 0.36666666666666664, "precision_at_5": 0.35333333333333333, "precision_at_k": 0.36666666666666664, "mrr_at_k": 0.6, "ndcg_at_k": 0.6, "f1_score": 0.6, "avg_query_time_ms": 2590.5890941619873, "max_query_time_ms": 9294.646978378296, "min_query_time_ms": 1384.2041492462158}, "total_queries": 10, "exact_match_count": 6, "exact_match_rate": 0.6, "avg_k": 2.8, "success_rate_at_1": 0.6, "success_rate_at_3": 0.6, "success_rate_at_5": 0.6, "success_rate_at_k": 0.6, "precision_at_1": 0.6, "precision_at_3": 0.36666666666666664, "precision_at_5": 0.35333333333333333, "precision_at_k": 0.36666666666666664, "mrr_at_k": 0.6, "ndcg_at_k": 0.6, "f1_score": 0.6, "avg_query_time_ms": 2590.5890941619873, "max_query_time_ms": 9294.646978378296, "min_query_time_ms": 1384.2041492462158}, "integration": {"path_matching": {"total_queries": 12, "exact_match_count": 7, "exact_match_rate": 0.5833333333333334, "avg_k": 2.75, "success_rate_at_1": 0.5, "success_rate_at_3": 0.5833333333333334, "success_rate_at_5": 0.5833333333333334, "success_rate_at_k": 0.5833333333333334, "precision_at_1": 0.5, "precision_at_3": 0.3194444444444445, "precision_at_5": 0.2833333333333333, "precision_at_k": 0.3194444444444445, "mrr_at_k": 0.5416666666666666, "ndcg_at_k": 0.5525774794642881, "f1_score": 0.5, "avg_query_time_ms": 1954.7950426737468, "max_query_time_ms": 2733.753204345703, "min_query_time_ms": 1313.8880729675293}, "content_matching": {"total_queries": 12, "exact_match_count": 7, "exact_match_rate": 0.5833333333333334, "avg_k": 2.75, "success_rate_at_1": 0.5, "success_rate_at_3": 0.5833333333333334, "success_rate_at_5": 0.5833333333333334, "success_rate_at_k": 0.5833333333333334, "precision_at_1": 0.5, "precision_at_3": 0.3194444444444445, "precision_at_5": 0.2833333333333333, "precision_at_k": 0.3194444444444445, "mrr_at_k": 0.5416666666666666, "ndcg_at_k": 0.5525774794642881, "f1_score": 0.5, "avg_query_time_ms": 1954.7950426737468, "max_query_time_ms": 2733.753204345703, "min_query_time_ms": 1313.8880729675293}, "total_queries": 12, "exact_match_count": 7, "exact_match_rate": 0.5833333333333334, "avg_k": 2.75, "success_rate_at_1": 0.5, "success_rate_at_3": 0.5833333333333334, "success_rate_at_5": 0.5833333333333334, "success_rate_at_k": 0.5833333333333334, "precision_at_1": 0.5, "precision_at_3": 0.3194444444444445, "precision_at_5": 0.2833333333333333, "precision_at_k": 0.3194444444444445, "mrr_at_k": 0.5416666666666666, "ndcg_at_k": 0.5525774794642881, "f1_score": 0.5, "avg_query_time_ms": 1954.7950426737468, "max_query_time_ms": 2733.753204345703, "min_query_time_ms": 1313.8880729675293}}, "Chatime": {"implementation": {"path_matching": {"total_queries": 24, "exact_match_count": 23, "exact_match_rate": 0.9583333333333334, "avg_k": 4.958333333333333, "success_rate_at_1": 0.5833333333333334, "success_rate_at_3": 0.8333333333333334, "success_rate_at_5": 0.9166666666666666, "success_rate_at_k": 0.9166666666666666, "precision_at_1": 0.5833333333333334, "precision_at_3": 0.3541666666666667, "precision_at_5": 0.3215277777777778, "precision_at_k": 0.3215277777777778, "mrr_at_k": 0.7260912698412697, "ndcg_at_k": 0.7696740888818843, "f1_score": 0.5833333333333334, "avg_query_time_ms": 1828.8082977135975, "max_query_time_ms": 2401.7019271850586, "min_query_time_ms": 1310.9331130981445}, "content_matching": {"total_queries": 24, "exact_match_count": 19, "exact_match_rate": 0.7916666666666666, "avg_k": 4.958333333333333, "success_rate_at_1": 0.4166666666666667, "success_rate_at_3": 0.7083333333333334, "success_rate_at_5": 0.75, "success_rate_at_k": 0.75, "precision_at_1": 0.4166666666666667, "precision_at_3": 0.31249999999999994, "precision_at_5": 0.28263888888888894, "precision_at_k": 0.28263888888888894, "mrr_at_k": 0.555952380952381, "ndcg_at_k": 0.6004404925633489, "f1_score": 0.4166666666666667, "avg_query_time_ms": 1828.8082977135975, "max_query_time_ms": 2401.7019271850586, "min_query_time_ms": 1310.9331130981445}, "total_queries": 24, "exact_match_count": 23, "exact_match_rate": 0.9583333333333334, "avg_k": 4.958333333333333, "success_rate_at_1": 0.5833333333333334, "success_rate_at_3": 0.8333333333333334, "success_rate_at_5": 0.9166666666666666, "success_rate_at_k": 0.9166666666666666, "precision_at_1": 0.5833333333333334, "precision_at_3": 0.3541666666666667, "precision_at_5": 0.3215277777777778, "precision_at_k": 0.3215277777777778, "mrr_at_k": 0.7260912698412697, "ndcg_at_k": 0.7696740888818843, "f1_score": 0.5833333333333334, "avg_query_time_ms": 1828.8082977135975, "max_query_time_ms": 2401.7019271850586, "min_query_time_ms": 1310.9331130981445}, "understanding": {"path_matching": {"total_queries": 26, "exact_match_count": 25, "exact_match_rate": 0.9615384615384616, "avg_k": 5.346153846153846, "success_rate_at_1": 0.6538461538461539, "success_rate_at_3": 0.8846153846153846, "success_rate_at_5": 0.9230769230769231, "success_rate_at_k": 0.9230769230769231, "precision_at_1": 0.6538461538461539, "precision_at_3": 0.42307692307692313, "precision_at_5": 0.36987179487179483, "precision_at_k": 0.36987179487179483, "mrr_at_k": 0.7695970695970694, "ndcg_at_k": 0.8042527623661682, "f1_score": 0.6538461538461539, "avg_query_time_ms": 1787.9323317454412, "max_query_time_ms": 2329.2572498321533, "min_query_time_ms": 1359.2100143432617}, "content_matching": {"total_queries": 26, "exact_match_count": 21, "exact_match_rate": 0.8076923076923077, "avg_k": 5.346153846153846, "success_rate_at_1": 0.5384615384615384, "success_rate_at_3": 0.8076923076923077, "success_rate_at_5": 0.8076923076923077, "success_rate_at_k": 0.8076923076923077, "precision_at_1": 0.5384615384615384, "precision_at_3": 0.37179487179487186, "precision_at_5": 0.316025641025641, "precision_at_k": 0.316025641025641, "mrr_at_k": 0.6538461538461537, "ndcg_at_k": 0.6932199620879166, "f1_score": 0.5384615384615384, "avg_query_time_ms": 1787.9323317454412, "max_query_time_ms": 2329.2572498321533, "min_query_time_ms": 1359.2100143432617}, "total_queries": 26, "exact_match_count": 25, "exact_match_rate": 0.9615384615384616, "avg_k": 5.346153846153846, "success_rate_at_1": 0.6538461538461539, "success_rate_at_3": 0.8846153846153846, "success_rate_at_5": 0.9230769230769231, "success_rate_at_k": 0.9230769230769231, "precision_at_1": 0.6538461538461539, "precision_at_3": 0.42307692307692313, "precision_at_5": 0.36987179487179483, "precision_at_k": 0.36987179487179483, "mrr_at_k": 0.7695970695970694, "ndcg_at_k": 0.8042527623661682, "f1_score": 0.6538461538461539, "avg_query_time_ms": 1787.9323317454412, "max_query_time_ms": 2329.2572498321533, "min_query_time_ms": 1359.2100143432617}, "debugging": {"path_matching": {"total_queries": 26, "exact_match_count": 24, "exact_match_rate": 0.9230769230769231, "avg_k": 4.884615384615385, "success_rate_at_1": 0.5769230769230769, "success_rate_at_3": 0.8076923076923077, "success_rate_at_5": 0.8076923076923077, "success_rate_at_k": 0.8076923076923077, "precision_at_1": 0.5769230769230769, "precision_at_3": 0.4102564102564103, "precision_at_5": 0.3647435897435897, "precision_at_k": 0.3647435897435897, "mrr_at_k": 0.6881410256410256, "ndcg_at_k": 0.7074149715659375, "f1_score": 0.5769230769230769, "avg_query_time_ms": 1930.5903819891123, "max_query_time_ms": 2763.6678218841553, "min_query_time_ms": 1290.4210090637207}, "content_matching": {"total_queries": 26, "exact_match_count": 22, "exact_match_rate": 0.8461538461538461, "avg_k": 4.884615384615385, "success_rate_at_1": 0.5, "success_rate_at_3": 0.8076923076923077, "success_rate_at_5": 0.8076923076923077, "success_rate_at_k": 0.8076923076923077, "precision_at_1": 0.5, "precision_at_3": 0.3846153846153847, "precision_at_5": 0.3320512820512821, "precision_at_k": 0.3320512820512821, "mrr_at_k": 0.6384615384615385, "ndcg_at_k": 0.6790249526098957, "f1_score": 0.5, "avg_query_time_ms": 1930.5903819891123, "max_query_time_ms": 2763.6678218841553, "min_query_time_ms": 1290.4210090637207}, "total_queries": 26, "exact_match_count": 24, "exact_match_rate": 0.9230769230769231, "avg_k": 4.884615384615385, "success_rate_at_1": 0.5769230769230769, "success_rate_at_3": 0.8076923076923077, "success_rate_at_5": 0.8076923076923077, "success_rate_at_k": 0.8076923076923077, "precision_at_1": 0.5769230769230769, "precision_at_3": 0.4102564102564103, "precision_at_5": 0.3647435897435897, "precision_at_k": 0.3647435897435897, "mrr_at_k": 0.6881410256410256, "ndcg_at_k": 0.7074149715659375, "f1_score": 0.5769230769230769, "avg_query_time_ms": 1930.5903819891123, "max_query_time_ms": 2763.6678218841553, "min_query_time_ms": 1290.4210090637207}, "optimization": {"path_matching": {"total_queries": 16, "exact_match_count": 14, "exact_match_rate": 0.875, "avg_k": 4.0625, "success_rate_at_1": 0.625, "success_rate_at_3": 0.8125, "success_rate_at_5": 0.875, "success_rate_at_k": 0.8125, "precision_at_1": 0.625, "precision_at_3": 0.44791666666666663, "precision_at_5": 0.41875, "precision_at_k": 0.0, "mrr_at_k": 0.7104166666666667, "ndcg_at_k": 0.0, "f1_score": 0.625, "avg_query_time_ms": 1819.649949669838, "max_query_time_ms": 2380.2731037139893, "min_query_time_ms": 1313.8408660888672}, "content_matching": {"total_queries": 16, "exact_match_count": 12, "exact_match_rate": 0.75, "avg_k": 4.0625, "success_rate_at_1": 0.5625, "success_rate_at_3": 0.6875, "success_rate_at_5": 0.75, "success_rate_at_k": 0.6875, "precision_at_1": 0.5625, "precision_at_3": 0.4479166666666667, "precision_at_5": 0.42708333333333337, "precision_at_k": 0.0, "mrr_at_k": 0.6166666666666667, "ndcg_at_k": 0.0, "f1_score": 0.5625, "avg_query_time_ms": 1819.649949669838, "max_query_time_ms": 2380.2731037139893, "min_query_time_ms": 1313.8408660888672}, "total_queries": 16, "exact_match_count": 14, "exact_match_rate": 0.875, "avg_k": 4.0625, "success_rate_at_1": 0.625, "success_rate_at_3": 0.8125, "success_rate_at_5": 0.875, "success_rate_at_k": 0.8125, "precision_at_1": 0.625, "precision_at_3": 0.44791666666666663, "precision_at_5": 0.41875, "precision_at_k": 0.0, "mrr_at_k": 0.7104166666666667, "ndcg_at_k": 0.0, "f1_score": 0.625, "avg_query_time_ms": 1819.649949669838, "max_query_time_ms": 2380.2731037139893, "min_query_time_ms": 1313.8408660888672}, "testing": {"path_matching": {"total_queries": 24, "exact_match_count": 20, "exact_match_rate": 0.8333333333333334, "avg_k": 4.666666666666667, "success_rate_at_1": 0.4583333333333333, "success_rate_at_3": 0.6666666666666666, "success_rate_at_5": 0.6666666666666666, "success_rate_at_k": 0.6666666666666666, "precision_at_1": 0.4583333333333333, "precision_at_3": 0.375, "precision_at_5": 0.3368055555555556, "precision_at_k": 0.3368055555555556, "mrr_at_k": 0.5624999999999999, "ndcg_at_k": 0.5679554063988107, "f1_score": 0.4583333333333333, "avg_query_time_ms": 1771.091878414154, "max_query_time_ms": 2756.549835205078, "min_query_time_ms": 1412.5990867614746}, "content_matching": {"total_queries": 24, "exact_match_count": 16, "exact_match_rate": 0.6666666666666666, "avg_k": 4.666666666666667, "success_rate_at_1": 0.4166666666666667, "success_rate_at_3": 0.625, "success_rate_at_5": 0.625, "success_rate_at_k": 0.625, "precision_at_1": 0.4166666666666667, "precision_at_3": 0.3611111111111111, "precision_at_5": 0.3263888888888889, "precision_at_k": 0.3263888888888889, "mrr_at_k": 0.5069444444444444, "ndcg_at_k": 0.5317441461309548, "f1_score": 0.4166666666666667, "avg_query_time_ms": 1771.091878414154, "max_query_time_ms": 2756.549835205078, "min_query_time_ms": 1412.5990867614746}, "total_queries": 24, "exact_match_count": 20, "exact_match_rate": 0.8333333333333334, "avg_k": 4.666666666666667, "success_rate_at_1": 0.4583333333333333, "success_rate_at_3": 0.6666666666666666, "success_rate_at_5": 0.6666666666666666, "success_rate_at_k": 0.6666666666666666, "precision_at_1": 0.4583333333333333, "precision_at_3": 0.375, "precision_at_5": 0.3368055555555556, "precision_at_k": 0.3368055555555556, "mrr_at_k": 0.5624999999999999, "ndcg_at_k": 0.5679554063988107, "f1_score": 0.4583333333333333, "avg_query_time_ms": 1771.091878414154, "max_query_time_ms": 2756.549835205078, "min_query_time_ms": 1412.5990867614746}, "integration": {"path_matching": {"total_queries": 23, "exact_match_count": 16, "exact_match_rate": 0.6956521739130435, "avg_k": 5.6521739130434785, "success_rate_at_1": 0.30434782608695654, "success_rate_at_3": 0.5217391304347826, "success_rate_at_5": 0.6956521739130435, "success_rate_at_k": 0.6956521739130435, "precision_at_1": 0.30434782608695654, "precision_at_3": 0.21014492753623187, "precision_at_5": 0.21449275362318845, "precision_at_k": 0.007246376811594202, "mrr_at_k": 0.43768115942028984, "ndcg_at_k": 0.021739130434782608, "f1_score": 0.30434782608695654, "avg_query_time_ms": 1790.4100314430568, "max_query_time_ms": 2411.9110107421875, "min_query_time_ms": 1368.8528537750244}, "content_matching": {"total_queries": 23, "exact_match_count": 13, "exact_match_rate": 0.5652173913043478, "avg_k": 5.6521739130434785, "success_rate_at_1": 0.34782608695652173, "success_rate_at_3": 0.4782608695652174, "success_rate_at_5": 0.5217391304347826, "success_rate_at_k": 0.5217391304347826, "precision_at_1": 0.34782608695652173, "precision_at_3": 0.1956521739130435, "precision_at_5": 0.17971014492753626, "precision_at_k": 0.0, "mrr_at_k": 0.41485507246376807, "ndcg_at_k": 0.0, "f1_score": 0.34782608695652173, "avg_query_time_ms": 1790.4100314430568, "max_query_time_ms": 2411.9110107421875, "min_query_time_ms": 1368.8528537750244}, "total_queries": 23, "exact_match_count": 16, "exact_match_rate": 0.6956521739130435, "avg_k": 5.6521739130434785, "success_rate_at_1": 0.30434782608695654, "success_rate_at_3": 0.5217391304347826, "success_rate_at_5": 0.6956521739130435, "success_rate_at_k": 0.6956521739130435, "precision_at_1": 0.30434782608695654, "precision_at_3": 0.21014492753623187, "precision_at_5": 0.21449275362318845, "precision_at_k": 0.007246376811594202, "mrr_at_k": 0.43768115942028984, "ndcg_at_k": 0.021739130434782608, "f1_score": 0.30434782608695654, "avg_query_time_ms": 1790.4100314430568, "max_query_time_ms": 2411.9110107421875, "min_query_time_ms": 1368.8528537750244}}, "oh-bill": {"understanding": {"path_matching": {"total_queries": 3, "exact_match_count": 2, "exact_match_rate": 0.6666666666666666, "avg_k": 2.6666666666666665, "success_rate_at_1": 0.6666666666666666, "success_rate_at_3": 0.6666666666666666, "success_rate_at_5": 0.6666666666666666, "success_rate_at_k": 0.6666666666666666, "precision_at_1": 0.6666666666666666, "precision_at_3": 0.4444444444444444, "precision_at_5": 0.4444444444444444, "precision_at_k": 0.4444444444444444, "mrr_at_k": 0.6666666666666666, "ndcg_at_k": 0.6666666666666666, "f1_score": 0.6666666666666666, "avg_query_time_ms": 1522.8925546010335, "max_query_time_ms": 1767.807960510254, "min_query_time_ms": 1268.4416770935059}, "content_matching": {"total_queries": 3, "exact_match_count": 2, "exact_match_rate": 0.6666666666666666, "avg_k": 2.6666666666666665, "success_rate_at_1": 0.6666666666666666, "success_rate_at_3": 0.6666666666666666, "success_rate_at_5": 0.6666666666666666, "success_rate_at_k": 0.6666666666666666, "precision_at_1": 0.6666666666666666, "precision_at_3": 0.4444444444444444, "precision_at_5": 0.4444444444444444, "precision_at_k": 0.4444444444444444, "mrr_at_k": 0.6666666666666666, "ndcg_at_k": 0.6666666666666666, "f1_score": 0.6666666666666666, "avg_query_time_ms": 1522.8925546010335, "max_query_time_ms": 1767.807960510254, "min_query_time_ms": 1268.4416770935059}, "total_queries": 3, "exact_match_count": 2, "exact_match_rate": 0.6666666666666666, "avg_k": 2.6666666666666665, "success_rate_at_1": 0.6666666666666666, "success_rate_at_3": 0.6666666666666666, "success_rate_at_5": 0.6666666666666666, "success_rate_at_k": 0.6666666666666666, "precision_at_1": 0.6666666666666666, "precision_at_3": 0.4444444444444444, "precision_at_5": 0.4444444444444444, "precision_at_k": 0.4444444444444444, "mrr_at_k": 0.6666666666666666, "ndcg_at_k": 0.6666666666666666, "f1_score": 0.6666666666666666, "avg_query_time_ms": 1522.8925546010335, "max_query_time_ms": 1767.807960510254, "min_query_time_ms": 1268.4416770935059}, "implementation": {"path_matching": {"total_queries": 3, "exact_match_count": 1, "exact_match_rate": 0.3333333333333333, "avg_k": 2.6666666666666665, "success_rate_at_1": 0.3333333333333333, "success_rate_at_3": 0.3333333333333333, "success_rate_at_5": 0.3333333333333333, "success_rate_at_k": 0.3333333333333333, "precision_at_1": 0.3333333333333333, "precision_at_3": 0.3333333333333333, "precision_at_5": 0.3333333333333333, "precision_at_k": 0.3333333333333333, "mrr_at_k": 0.3333333333333333, "ndcg_at_k": 0.3333333333333333, "f1_score": 0.3333333333333333, "avg_query_time_ms": 1642.104148864746, "max_query_time_ms": 2118.407964706421, "min_query_time_ms": 1295.5322265625}, "content_matching": {"total_queries": 3, "exact_match_count": 1, "exact_match_rate": 0.3333333333333333, "avg_k": 2.6666666666666665, "success_rate_at_1": 0.3333333333333333, "success_rate_at_3": 0.3333333333333333, "success_rate_at_5": 0.3333333333333333, "success_rate_at_k": 0.3333333333333333, "precision_at_1": 0.3333333333333333, "precision_at_3": 0.3333333333333333, "precision_at_5": 0.3333333333333333, "precision_at_k": 0.3333333333333333, "mrr_at_k": 0.3333333333333333, "ndcg_at_k": 0.3333333333333333, "f1_score": 0.3333333333333333, "avg_query_time_ms": 1642.104148864746, "max_query_time_ms": 2118.407964706421, "min_query_time_ms": 1295.5322265625}, "total_queries": 3, "exact_match_count": 1, "exact_match_rate": 0.3333333333333333, "avg_k": 2.6666666666666665, "success_rate_at_1": 0.3333333333333333, "success_rate_at_3": 0.3333333333333333, "success_rate_at_5": 0.3333333333333333, "success_rate_at_k": 0.3333333333333333, "precision_at_1": 0.3333333333333333, "precision_at_3": 0.3333333333333333, "precision_at_5": 0.3333333333333333, "precision_at_k": 0.3333333333333333, "mrr_at_k": 0.3333333333333333, "ndcg_at_k": 0.3333333333333333, "f1_score": 0.3333333333333333, "avg_query_time_ms": 1642.104148864746, "max_query_time_ms": 2118.407964706421, "min_query_time_ms": 1295.5322265625}, "debugging": {"path_matching": {"total_queries": 3, "exact_match_count": 3, "exact_match_rate": 1.0, "avg_k": 2.3333333333333335, "success_rate_at_1": 0.6666666666666666, "success_rate_at_3": 1.0, "success_rate_at_5": 1.0, "success_rate_at_k": 1.0, "precision_at_1": 0.6666666666666666, "precision_at_3": 0.5555555555555555, "precision_at_5": 0.5555555555555555, "precision_at_k": 0.0, "mrr_at_k": 0.8333333333333334, "ndcg_at_k": 0.0, "f1_score": 0.6666666666666666, "avg_query_time_ms": 1561.6260369618733, "max_query_time_ms": 1753.5090446472168, "min_query_time_ms": 1316.3669109344482}, "content_matching": {"total_queries": 3, "exact_match_count": 3, "exact_match_rate": 1.0, "avg_k": 2.3333333333333335, "success_rate_at_1": 0.6666666666666666, "success_rate_at_3": 1.0, "success_rate_at_5": 1.0, "success_rate_at_k": 1.0, "precision_at_1": 0.6666666666666666, "precision_at_3": 0.5555555555555555, "precision_at_5": 0.5555555555555555, "precision_at_k": 0.0, "mrr_at_k": 0.8333333333333334, "ndcg_at_k": 0.0, "f1_score": 0.6666666666666666, "avg_query_time_ms": 1561.6260369618733, "max_query_time_ms": 1753.5090446472168, "min_query_time_ms": 1316.3669109344482}, "total_queries": 3, "exact_match_count": 3, "exact_match_rate": 1.0, "avg_k": 2.3333333333333335, "success_rate_at_1": 0.6666666666666666, "success_rate_at_3": 1.0, "success_rate_at_5": 1.0, "success_rate_at_k": 1.0, "precision_at_1": 0.6666666666666666, "precision_at_3": 0.5555555555555555, "precision_at_5": 0.5555555555555555, "precision_at_k": 0.0, "mrr_at_k": 0.8333333333333334, "ndcg_at_k": 0.0, "f1_score": 0.6666666666666666, "avg_query_time_ms": 1561.6260369618733, "max_query_time_ms": 1753.5090446472168, "min_query_time_ms": 1316.3669109344482}, "testing": {"path_matching": {"total_queries": 1, "exact_match_count": 1, "exact_match_rate": 1.0, "avg_k": 1.0, "success_rate_at_1": 1.0, "success_rate_at_3": 1.0, "success_rate_at_5": 1.0, "success_rate_at_k": 1.0, "precision_at_1": 1.0, "precision_at_3": 1.0, "precision_at_5": 1.0, "precision_at_k": 1.0, "mrr_at_k": 1.0, "ndcg_at_k": 1.0, "f1_score": 1.0, "avg_query_time_ms": 2840.304136276245, "max_query_time_ms": 2840.304136276245, "min_query_time_ms": 2840.304136276245}, "content_matching": {"total_queries": 1, "exact_match_count": 1, "exact_match_rate": 1.0, "avg_k": 1.0, "success_rate_at_1": 1.0, "success_rate_at_3": 1.0, "success_rate_at_5": 1.0, "success_rate_at_k": 1.0, "precision_at_1": 1.0, "precision_at_3": 1.0, "precision_at_5": 1.0, "precision_at_k": 1.0, "mrr_at_k": 1.0, "ndcg_at_k": 1.0, "f1_score": 1.0, "avg_query_time_ms": 2840.304136276245, "max_query_time_ms": 2840.304136276245, "min_query_time_ms": 2840.304136276245}, "total_queries": 1, "exact_match_count": 1, "exact_match_rate": 1.0, "avg_k": 1.0, "success_rate_at_1": 1.0, "success_rate_at_3": 1.0, "success_rate_at_5": 1.0, "success_rate_at_k": 1.0, "precision_at_1": 1.0, "precision_at_3": 1.0, "precision_at_5": 1.0, "precision_at_k": 1.0, "mrr_at_k": 1.0, "ndcg_at_k": 1.0, "f1_score": 1.0, "avg_query_time_ms": 2840.304136276245, "max_query_time_ms": 2840.304136276245, "min_query_time_ms": 2840.304136276245}, "integration": {"path_matching": {"total_queries": 1, "exact_match_count": 0, "exact_match_rate": 0.0, "avg_k": 1.0, "success_rate_at_1": 0.0, "success_rate_at_3": 0.0, "success_rate_at_5": 0.0, "success_rate_at_k": 0.0, "precision_at_1": 0.0, "precision_at_3": 0.0, "precision_at_5": 0.0, "precision_at_k": 0.0, "mrr_at_k": 0.0, "ndcg_at_k": 0.0, "f1_score": 0.0, "avg_query_time_ms": 1764.3611431121826, "max_query_time_ms": 1764.3611431121826, "min_query_time_ms": 1764.3611431121826}, "content_matching": {"total_queries": 1, "exact_match_count": 0, "exact_match_rate": 0.0, "avg_k": 1.0, "success_rate_at_1": 0.0, "success_rate_at_3": 0.0, "success_rate_at_5": 0.0, "success_rate_at_k": 0.0, "precision_at_1": 0.0, "precision_at_3": 0.0, "precision_at_5": 0.0, "precision_at_k": 0.0, "mrr_at_k": 0.0, "ndcg_at_k": 0.0, "f1_score": 0.0, "avg_query_time_ms": 1764.3611431121826, "max_query_time_ms": 1764.3611431121826, "min_query_time_ms": 1764.3611431121826}, "total_queries": 1, "exact_match_count": 0, "exact_match_rate": 0.0, "avg_k": 1.0, "success_rate_at_1": 0.0, "success_rate_at_3": 0.0, "success_rate_at_5": 0.0, "success_rate_at_k": 0.0, "precision_at_1": 0.0, "precision_at_3": 0.0, "precision_at_5": 0.0, "precision_at_k": 0.0, "mrr_at_k": 0.0, "ndcg_at_k": 0.0, "f1_score": 0.0, "avg_query_time_ms": 1764.3611431121826, "max_query_time_ms": 1764.3611431121826, "min_query_time_ms": 1764.3611431121826}, "optimization": {"path_matching": {"total_queries": 2, "exact_match_count": 2, "exact_match_rate": 1.0, "avg_k": 2.0, "success_rate_at_1": 0.5, "success_rate_at_3": 1.0, "success_rate_at_5": 1.0, "success_rate_at_k": 1.0, "precision_at_1": 0.5, "precision_at_3": 0.6666666666666666, "precision_at_5": 0.6666666666666666, "precision_at_k": 0.0, "mrr_at_k": 0.75, "ndcg_at_k": 0.0, "f1_score": 0.5, "avg_query_time_ms": 1580.9211730957031, "max_query_time_ms": 1733.2401275634766, "min_query_time_ms": 1428.6022186279297}, "content_matching": {"total_queries": 2, "exact_match_count": 2, "exact_match_rate": 1.0, "avg_k": 2.0, "success_rate_at_1": 0.5, "success_rate_at_3": 1.0, "success_rate_at_5": 1.0, "success_rate_at_k": 1.0, "precision_at_1": 0.5, "precision_at_3": 0.6666666666666666, "precision_at_5": 0.6666666666666666, "precision_at_k": 0.0, "mrr_at_k": 0.75, "ndcg_at_k": 0.0, "f1_score": 0.5, "avg_query_time_ms": 1580.9211730957031, "max_query_time_ms": 1733.2401275634766, "min_query_time_ms": 1428.6022186279297}, "total_queries": 2, "exact_match_count": 2, "exact_match_rate": 1.0, "avg_k": 2.0, "success_rate_at_1": 0.5, "success_rate_at_3": 1.0, "success_rate_at_5": 1.0, "success_rate_at_k": 1.0, "precision_at_1": 0.5, "precision_at_3": 0.6666666666666666, "precision_at_5": 0.6666666666666666, "precision_at_k": 0.0, "mrr_at_k": 0.75, "ndcg_at_k": 0.0, "f1_score": 0.5, "avg_query_time_ms": 1580.9211730957031, "max_query_time_ms": 1733.2401275634766, "min_query_time_ms": 1428.6022186279297}}, "music-gathering": {"implementation": {"path_matching": {"total_queries": 3, "exact_match_count": 1, "exact_match_rate": 0.3333333333333333, "avg_k": 5.333333333333333, "success_rate_at_1": 0.0, "success_rate_at_3": 0.0, "success_rate_at_5": 0.3333333333333333, "success_rate_at_k": 0.3333333333333333, "precision_at_1": 0.0, "precision_at_3": 0.0, "precision_at_5": 0.06666666666666667, "precision_at_k": 0.06666666666666667, "mrr_at_k": 0.06666666666666667, "ndcg_at_k": 0.1289509357448472, "f1_score": 0.0, "avg_query_time_ms": 1572.5267728169758, "max_query_time_ms": 2103.1241416931152, "min_query_time_ms": 1227.0898818969727}, "content_matching": {"total_queries": 3, "exact_match_count": 1, "exact_match_rate": 0.3333333333333333, "avg_k": 5.333333333333333, "success_rate_at_1": 0.0, "success_rate_at_3": 0.0, "success_rate_at_5": 0.3333333333333333, "success_rate_at_k": 0.3333333333333333, "precision_at_1": 0.0, "precision_at_3": 0.0, "precision_at_5": 0.06666666666666667, "precision_at_k": 0.06666666666666667, "mrr_at_k": 0.06666666666666667, "ndcg_at_k": 0.1289509357448472, "f1_score": 0.0, "avg_query_time_ms": 1572.5267728169758, "max_query_time_ms": 2103.1241416931152, "min_query_time_ms": 1227.0898818969727}, "total_queries": 3, "exact_match_count": 1, "exact_match_rate": 0.3333333333333333, "avg_k": 5.333333333333333, "success_rate_at_1": 0.0, "success_rate_at_3": 0.0, "success_rate_at_5": 0.3333333333333333, "success_rate_at_k": 0.3333333333333333, "precision_at_1": 0.0, "precision_at_3": 0.0, "precision_at_5": 0.06666666666666667, "precision_at_k": 0.06666666666666667, "mrr_at_k": 0.06666666666666667, "ndcg_at_k": 0.1289509357448472, "f1_score": 0.0, "avg_query_time_ms": 1572.5267728169758, "max_query_time_ms": 2103.1241416931152, "min_query_time_ms": 1227.0898818969727}, "optimization": {"path_matching": {"total_queries": 1, "exact_match_count": 1, "exact_match_rate": 1.0, "avg_k": 1.0, "success_rate_at_1": 1.0, "success_rate_at_3": 1.0, "success_rate_at_5": 1.0, "success_rate_at_k": 1.0, "precision_at_1": 1.0, "precision_at_3": 1.0, "precision_at_5": 1.0, "precision_at_k": 1.0, "mrr_at_k": 1.0, "ndcg_at_k": 1.0, "f1_score": 1.0, "avg_query_time_ms": 1353.0001640319824, "max_query_time_ms": 1353.0001640319824, "min_query_time_ms": 1353.0001640319824}, "content_matching": {"total_queries": 1, "exact_match_count": 1, "exact_match_rate": 1.0, "avg_k": 1.0, "success_rate_at_1": 1.0, "success_rate_at_3": 1.0, "success_rate_at_5": 1.0, "success_rate_at_k": 1.0, "precision_at_1": 1.0, "precision_at_3": 1.0, "precision_at_5": 1.0, "precision_at_k": 1.0, "mrr_at_k": 1.0, "ndcg_at_k": 1.0, "f1_score": 1.0, "avg_query_time_ms": 1353.0001640319824, "max_query_time_ms": 1353.0001640319824, "min_query_time_ms": 1353.0001640319824}, "total_queries": 1, "exact_match_count": 1, "exact_match_rate": 1.0, "avg_k": 1.0, "success_rate_at_1": 1.0, "success_rate_at_3": 1.0, "success_rate_at_5": 1.0, "success_rate_at_k": 1.0, "precision_at_1": 1.0, "precision_at_3": 1.0, "precision_at_5": 1.0, "precision_at_k": 1.0, "mrr_at_k": 1.0, "ndcg_at_k": 1.0, "f1_score": 1.0, "avg_query_time_ms": 1353.0001640319824, "max_query_time_ms": 1353.0001640319824, "min_query_time_ms": 1353.0001640319824}, "understanding": {"path_matching": {"total_queries": 3, "exact_match_count": 2, "exact_match_rate": 0.6666666666666666, "avg_k": 4.333333333333333, "success_rate_at_1": 0.6666666666666666, "success_rate_at_3": 0.6666666666666666, "success_rate_at_5": 0.6666666666666666, "success_rate_at_k": 0.6666666666666666, "precision_at_1": 0.6666666666666666, "precision_at_3": 0.2222222222222222, "precision_at_5": 0.17777777777777778, "precision_at_k": 0.0, "mrr_at_k": 0.6666666666666666, "ndcg_at_k": 0.0, "f1_score": 0.6666666666666666, "avg_query_time_ms": 1967.2509829203289, "max_query_time_ms": 2183.00199508667, "min_query_time_ms": 1615.9229278564453}, "content_matching": {"total_queries": 3, "exact_match_count": 2, "exact_match_rate": 0.6666666666666666, "avg_k": 4.333333333333333, "success_rate_at_1": 0.6666666666666666, "success_rate_at_3": 0.6666666666666666, "success_rate_at_5": 0.6666666666666666, "success_rate_at_k": 0.6666666666666666, "precision_at_1": 0.6666666666666666, "precision_at_3": 0.2222222222222222, "precision_at_5": 0.17777777777777778, "precision_at_k": 0.0, "mrr_at_k": 0.6666666666666666, "ndcg_at_k": 0.0, "f1_score": 0.6666666666666666, "avg_query_time_ms": 1967.2509829203289, "max_query_time_ms": 2183.00199508667, "min_query_time_ms": 1615.9229278564453}, "total_queries": 3, "exact_match_count": 2, "exact_match_rate": 0.6666666666666666, "avg_k": 4.333333333333333, "success_rate_at_1": 0.6666666666666666, "success_rate_at_3": 0.6666666666666666, "success_rate_at_5": 0.6666666666666666, "success_rate_at_k": 0.6666666666666666, "precision_at_1": 0.6666666666666666, "precision_at_3": 0.2222222222222222, "precision_at_5": 0.17777777777777778, "precision_at_k": 0.0, "mrr_at_k": 0.6666666666666666, "ndcg_at_k": 0.0, "f1_score": 0.6666666666666666, "avg_query_time_ms": 1967.2509829203289, "max_query_time_ms": 2183.00199508667, "min_query_time_ms": 1615.9229278564453}, "testing": {"path_matching": {"total_queries": 1, "exact_match_count": 0, "exact_match_rate": 0.0, "avg_k": 5.0, "success_rate_at_1": 0.0, "success_rate_at_3": 0.0, "success_rate_at_5": 0.0, "success_rate_at_k": 0.0, "precision_at_1": 0.0, "precision_at_3": 0.0, "precision_at_5": 0.0, "precision_at_k": 0.0, "mrr_at_k": 0.0, "ndcg_at_k": 0.0, "f1_score": 0.0, "avg_query_time_ms": 1976.8338203430176, "max_query_time_ms": 1976.8338203430176, "min_query_time_ms": 1976.8338203430176}, "content_matching": {"total_queries": 1, "exact_match_count": 0, "exact_match_rate": 0.0, "avg_k": 5.0, "success_rate_at_1": 0.0, "success_rate_at_3": 0.0, "success_rate_at_5": 0.0, "success_rate_at_k": 0.0, "precision_at_1": 0.0, "precision_at_3": 0.0, "precision_at_5": 0.0, "precision_at_k": 0.0, "mrr_at_k": 0.0, "ndcg_at_k": 0.0, "f1_score": 0.0, "avg_query_time_ms": 1976.8338203430176, "max_query_time_ms": 1976.8338203430176, "min_query_time_ms": 1976.8338203430176}, "total_queries": 1, "exact_match_count": 0, "exact_match_rate": 0.0, "avg_k": 5.0, "success_rate_at_1": 0.0, "success_rate_at_3": 0.0, "success_rate_at_5": 0.0, "success_rate_at_k": 0.0, "precision_at_1": 0.0, "precision_at_3": 0.0, "precision_at_5": 0.0, "precision_at_k": 0.0, "mrr_at_k": 0.0, "ndcg_at_k": 0.0, "f1_score": 0.0, "avg_query_time_ms": 1976.8338203430176, "max_query_time_ms": 1976.8338203430176, "min_query_time_ms": 1976.8338203430176}, "integration": {"path_matching": {"total_queries": 2, "exact_match_count": 0, "exact_match_rate": 0.0, "avg_k": 5.0, "success_rate_at_1": 0.0, "success_rate_at_3": 0.0, "success_rate_at_5": 0.0, "success_rate_at_k": 0.0, "precision_at_1": 0.0, "precision_at_3": 0.0, "precision_at_5": 0.0, "precision_at_k": 0.0, "mrr_at_k": 0.0, "ndcg_at_k": 0.0, "f1_score": 0.0, "avg_query_time_ms": 1592.210292816162, "max_query_time_ms": 1962.0013236999512, "min_query_time_ms": 1222.419261932373}, "content_matching": {"total_queries": 2, "exact_match_count": 0, "exact_match_rate": 0.0, "avg_k": 5.0, "success_rate_at_1": 0.0, "success_rate_at_3": 0.0, "success_rate_at_5": 0.0, "success_rate_at_k": 0.0, "precision_at_1": 0.0, "precision_at_3": 0.0, "precision_at_5": 0.0, "precision_at_k": 0.0, "mrr_at_k": 0.0, "ndcg_at_k": 0.0, "f1_score": 0.0, "avg_query_time_ms": 1592.210292816162, "max_query_time_ms": 1962.0013236999512, "min_query_time_ms": 1222.419261932373}, "total_queries": 2, "exact_match_count": 0, "exact_match_rate": 0.0, "avg_k": 5.0, "success_rate_at_1": 0.0, "success_rate_at_3": 0.0, "success_rate_at_5": 0.0, "success_rate_at_k": 0.0, "precision_at_1": 0.0, "precision_at_3": 0.0, "precision_at_5": 0.0, "precision_at_k": 0.0, "mrr_at_k": 0.0, "ndcg_at_k": 0.0, "f1_score": 0.0, "avg_query_time_ms": 1592.210292816162, "max_query_time_ms": 1962.0013236999512, "min_query_time_ms": 1222.419261932373}}}, "source": "tool", "total_time_seconds": 2202.0655360221863}