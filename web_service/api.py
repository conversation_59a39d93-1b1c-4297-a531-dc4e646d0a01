"""
Web API服务
提供HTTP接口用于代码检索
"""

from __future__ import annotations

import logging
import os
from pathlib import Path, PurePosixPath

from fastapi import FastAPI, HTTPException
from contextlib import asynccontextmanager
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field

from config import SERVICE_CONFIG
from config import HTT<PERSON>lient<PERSON>ool
from config import model_manager, ModelType
from symbol_processor import SymbolRetriever
from utils.models import (
    HealthResponse,
    QueryRequest,
    RetrievalResult,
    SymbolQuery,
    SymbolType,
)

logger = logging.getLogger(__name__)


# 仓库洞察请求模型（放在路由定义之前，避免前向引用问题）
class CodebaseInsightRequest(BaseModel):
    """仓库洞察请求参数"""

    include: list[str] | None = Field(
        default=None, description="包含路径模式列表，支持glob通配符。为空时分析全部文件"
    )
    exclude: list[str] | None = Field(
        default=None, description="排除路径模式列表，支持glob通配符。为空时全仓分析"
    )


class CodebaseRetrievalAPI:
    """代码库检索API"""

    def __init__(self, retrieval_engine=None, symbol_index=None):
        # 使用 lifespan 取代 on_event
        @asynccontextmanager
        async def lifespan(app: FastAPI):
            """在应用启动与关闭时执行预热与清理。"""
            try:
                # 降噪：屏蔽底层 HTTP/2/HPACK 的 DEBUG 日志
                logging.getLogger("httpcore.http2").setLevel(logging.WARNING)
                logging.getLogger("hpack.hpack").setLevel(logging.WARNING)

                # 预先实例化客户端（按当前事件循环隔离的连接池）
                model_manager.get_client(ModelType.EMBEDDING)
                model_manager.get_client(ModelType.FLASH)
                model_manager.get_client(ModelType.NORMAL)

                # 预热一次极小请求，建立 HTTP/2/TLS 连接，触发远端连接复用
                try:
                    await model_manager.embedding("warmup", ModelType.EMBEDDING)
                except Exception:
                    pass

                try:
                    await model_manager.chat_completion("ping", ModelType.FLASH, max_tokens=5)
                except Exception:
                    pass

                try:
                    await model_manager.chat_completion("ping", ModelType.NORMAL, max_tokens=5)
                except Exception:
                    pass
            except Exception as e:
                logger.debug(f"模型客户端预热失败（忽略）: {e}")
            # startup 完成
            yield
            # shutdown：释放HTTP连接池资源
            try:
                await HTTPClientPool.aclose_all()
            except Exception:
                pass

        self.app = FastAPI(
            title="Codebase Retriever API",
            description="智能代码库检索服务",
            version="1.0.0",
            lifespan=lifespan,
        )

        self.retrieval_engine = retrieval_engine
        self.symbol_index = symbol_index
        self.symbol_retriever = SymbolRetriever(symbol_index) if symbol_index else None
        self.setup_middleware()
        self.setup_routes()

    def setup_middleware(self):
        """设置中间件"""
        # CORS支持
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )

    def setup_routes(self):
        """设置路由"""

        # startup/on_event 迁移到 lifespan，上面已实现

        @self.app.get("/health")
        async def health_check() -> HealthResponse:
            """健康检查端点"""
            try:
                if not self.retrieval_engine:
                    return HealthResponse(
                        status="unhealthy",
                        message="检索引擎未初始化",
                        index_status="not_ready",
                        total_chunks=0,
                        total_files=0,
                    )

                # 获取索引状态
                index_stats = self.retrieval_engine.get_index_stats()

                return HealthResponse(
                    status="healthy",
                    message="服务运行正常",
                    index_status="ready",
                    total_chunks=index_stats.get("total_chunks", 0),
                    total_files=index_stats.get("total_files", 0),
                )

            except Exception as e:
                return HealthResponse(
                    status="unhealthy",
                    message=f"健康检查失败: {str(e)}",
                    index_status="error",
                    total_chunks=0,
                    total_files=0,
                )

        @self.app.post("/query")
        async def query_endpoint(request: QueryRequest):
            """查询端点"""
            try:
                # 验证请求参数
                if not request.query or not request.query.strip():
                    raise HTTPException(status_code=400, detail="查询内容不能为空")

                if request.top_k <= 0 or request.top_k > 100:
                    raise HTTPException(status_code=400, detail="top_k必须在1-100之间")

                if not self.retrieval_engine:
                    raise HTTPException(status_code=503, detail="检索引擎未初始化")

                # 执行检索
                results = await self.retrieval_engine.search(
                    query=request.query, top_k=request.top_k
                )

                # 转换为详细格式
                detailed_results = [
                    self._convert_retrieval_result_to_dict(r) for r in results
                ]

                # 准备响应，包含agent模式信息
                response = {
                    "query": request.query, 
                    "results": detailed_results,
                    "agent_mode": getattr(self.retrieval_engine, 'agent_mode', False),
                    "search_engine": "agent" if getattr(self.retrieval_engine, 'agent_mode', False) else "traditional"
                }

                return response

            except HTTPException:
                raise
            except Exception as e:
                logger.exception(f"查询处理异常: {e}")
                raise HTTPException(
                    status_code=500, detail=f"查询处理失败: {str(e)}"
                ) from None

        @self.app.get("/stats")
        async def get_stats():
            """获取系统统计信息"""
            try:
                if not self.retrieval_engine:
                    raise HTTPException(status_code=503, detail="检索引擎未初始化")

                stats = self.retrieval_engine.get_detailed_stats()
                
                # 添加agent模式信息
                stats["agent_mode"] = {
                    "enabled": getattr(self.retrieval_engine, 'agent_mode', False),
                    "processor_available": hasattr(self.retrieval_engine, 'agent_query_processor') and 
                                         self.retrieval_engine.agent_query_processor is not None
                }
                
                return stats

            except HTTPException:
                raise
            except Exception as e:
                raise HTTPException(
                    status_code=500, detail=f"获取统计信息失败: {str(e)}"
                ) from None

        @self.app.post("/symbol_query")
        async def symbol_query_endpoint(request: dict):
            """符号检索端点"""
            try:
                if not self.symbol_retriever or not self.symbol_index:
                    raise HTTPException(status_code=503, detail="符号检索未初始化")

                # 解析请求
                q = request or {}
                name = q.get("query", "")
                exact = bool(q.get("exact_match", False))
                types_in: list[str] = q.get("symbol_types") or []
                langs: list[str] = q.get("languages") or None
                ctx = q.get("context_path")
                include_refs = bool(q.get("include_references", False))
                max_results = int(q.get("max_results", 50))

                symbol_types = None
                if types_in:
                    try:
                        symbol_types = [SymbolType(t) for t in types_in]
                    except Exception:
                        # 忽略非法输入
                        symbol_types = None

                query = SymbolQuery(
                    query=name,
                    exact_match=exact,
                    symbol_types=symbol_types,
                    languages=langs,
                    context_path=ctx,
                    include_references=include_refs,
                    max_results=max_results,
                )

                results = await self.symbol_retriever.search_symbols(query)

                def to_dict(r):
                    s = r.symbol
                    return {
                        "symbol": {
                            "id": s.id,
                            "name": s.name,
                            "type": s.symbol_type.value,
                            "file_path": s.file_path,
                            "module_path": s.module_path,
                            "namespace": s.namespace,
                            "parent_symbol": s.parent_symbol,
                            "signature": s.signature,
                            "start_line": s.start_line,
                            "end_line": s.end_line,
                            "language": s.language,
                        },
                        "score": r.score,
                        "match_reason": r.match_reason,
                        "context_snippet": r.context_snippet,
                    }

                return {
                    "query": {
                        "query": query.query,
                        "exact_match": query.exact_match,
                        "symbol_types": [t.value for t in (query.symbol_types or [])],
                        "languages": query.languages,
                        "context_path": query.context_path,
                        "include_references": query.include_references,
                        "max_results": query.max_results,
                    },
                    "results": [to_dict(r) for r in results],
                }
            except HTTPException:
                raise
            except Exception as e:
                raise HTTPException(
                    status_code=500, detail=f"符号查询失败: {e}"
                ) from None

        @self.app.get("/symbol/{symbol_id}")
        async def get_symbol_by_id(symbol_id: str):
            """通过symbol_id获取符号"""
            try:
                if not self.symbol_index:
                    raise HTTPException(status_code=503, detail="符号索引未初始化")

                symbol = self.symbol_index.get_symbol(symbol_id)
                if not symbol:
                    raise HTTPException(status_code=404, detail="符号未找到")

                return {
                    "symbol": {
                        "id": symbol.id,
                        "name": symbol.name,
                        "type": symbol.symbol_type.value,
                        "file_path": symbol.file_path,
                        "module_path": symbol.module_path,
                        "namespace": symbol.namespace,
                        "parent_symbol": symbol.parent_symbol,
                        "signature": symbol.signature,
                        "start_line": symbol.start_line,
                        "end_line": symbol.end_line,
                        "language": symbol.language,
                        "dependencies": symbol.dependencies,
                        "references": [
                            {
                                "file_path": ref.file_path,
                                "line": ref.line,
                                "column": ref.column,
                                "context": ref.context,
                            }
                            for ref in (symbol.references or [])
                        ],
                    }
                }
            except HTTPException:
                raise
            except Exception as e:
                raise HTTPException(
                    status_code=500, detail=f"查询符号失败: {e}"
                ) from None

        @self.app.post("/indexes/update")
        async def refresh_endpoint():
            """刷新索引端点 - 扫描代码库差异并增量更新"""
            try:
                if not self.retrieval_engine:
                    raise HTTPException(status_code=503, detail="检索引擎未初始化")

                # 执行增量刷新
                result = await self.retrieval_engine.refresh_index()

                return {"status": "success", "message": "索引刷新完成", "stats": result}

            except HTTPException:
                raise
            except Exception as e:
                raise HTTPException(
                    status_code=500, detail=f"索引刷新失败: {str(e)}"
                ) from None

        @self.app.post("/reindex")
        async def reindex_endpoint():
            """重新索引端点 - 完全重建索引"""
            try:
                if not self.retrieval_engine:
                    raise HTTPException(status_code=503, detail="检索引擎未初始化")

                # 执行完全重建
                result = await self.retrieval_engine.reindex()

                return {"status": "success", "message": "重新索引完成", "stats": result}

            except HTTPException:
                raise
            except Exception as e:
                raise HTTPException(
                    status_code=500, detail=f"重新索引失败: {str(e)}"
                ) from None

        @self.app.post("/codebase_insight")
        async def codebase_insight_endpoint(request: CodebaseInsightRequest):
            """获取代码库文件树及每个文件的code insight"""
            try:
                if not self.retrieval_engine:
                    raise HTTPException(status_code=503, detail="检索引擎未初始化")

                # 获取文件树和insights（支持 include/exclude 过滤）
                tree_data = self._generate_codebase_tree_with_insights(
                    include=request.include or [], exclude=request.exclude or []
                )

                return {
                    "tree": tree_data["tree"],
                    "file_count": tree_data["file_count"],
                    "total_nodes": tree_data["total_nodes"],
                }

            except HTTPException:
                raise
            except Exception as e:
                raise HTTPException(
                    status_code=500, detail=f"获取代码库洞察失败: {str(e)}"
                ) from None

        @self.app.get("/")
        async def root():
            """根端点"""
            agent_mode_enabled = (self.retrieval_engine and 
                                getattr(self.retrieval_engine, 'agent_mode', False))
            
            return {
                "service": "Codebase Retriever API",
                "version": "1.0.0",
                "status": "running",
                "agent_mode": agent_mode_enabled,
                "description": f"智能代码库检索服务 ({'Agent查询模式' if agent_mode_enabled else '传统模式'})",
                "endpoints": {
                    "health": "/health",
                    "query": "/query",
                    "stats": "/stats",
                    "refresh": "/indexes/update",
                    "reindex": "/reindex",
                    "codebase_insight": "/codebase_insight",
                    "symbol_query": "/symbol_query",
                    "get_symbol": "/symbol/{symbol_id}",
                },
            }

    def set_retrieval_engine(self, engine):
        """设置检索引擎"""
        self.retrieval_engine = engine

    def set_symbol_index(self, symbol_index):
        """设置符号索引并初始化检索器"""
        self.symbol_index = symbol_index
        self.symbol_retriever = SymbolRetriever(symbol_index) if symbol_index else None

    def _convert_retrieval_result_to_dict(self, result: RetrievalResult) -> dict:
        """将RetrievalResult转换为字典格式，匹配期望的输出格式"""
        # 从检索引擎获取更详细的chunk信息
        chunk = None
        if hasattr(self.retrieval_engine, "repo_index"):
            # 尝试通过文件路径和行号找到对应的chunk
            chunks = self.retrieval_engine.repo_index.get_chunks_by_file(
                result.file_path
            )
            for c in chunks:
                if c.start_line <= result.start_line <= c.end_line:
                    chunk = c
                    break

        # 计算文件大小和其他信息

        file_path = result.file_path
        file_name = Path(file_path).name
        file_type = self._get_mime_type(file_path)

        # 尝试获取文件信息
        file_size = 0
        creation_date = "2025-08-02"  # 默认日期
        last_modified_date = "2025-08-02"

        try:
            if hasattr(self.retrieval_engine, "repo_path"):
                full_path = os.path.join(self.retrieval_engine.repo_path, file_path)
                if os.path.exists(full_path):
                    stat = os.stat(full_path)
                    file_size = stat.st_size
                    import datetime

                    creation_date = datetime.datetime.fromtimestamp(
                        stat.st_ctime
                    ).strftime("%Y-%m-%d")
                    last_modified_date = datetime.datetime.fromtimestamp(
                        stat.st_mtime
                    ).strftime("%Y-%m-%d")
        except Exception:
            pass

        # 构建metadata
        metadata = {
            "file_path": file_path,
            "file_name": file_name,
            "file_type": file_type,
            "file_size": file_size,
            "creation_date": creation_date,
            "last_modified_date": last_modified_date,
            "start_line": result.start_line,
            "end_line": result.end_line,
            "span_ids": [result.symbol_name] if result.symbol_name else [],
            "tokens": len(result.text.split()),  # 简单的token计数
            "has_code_insight": chunk and chunk.insight is not None,
            "code_insight": chunk.insight if chunk and chunk.insight else "",
            "tags": self._generate_tags(chunk, result),
            "category": result.chunk_type,
        }

        return {
            "metadata": metadata,
            "file_path": file_path,
            "text": result.text,
            "score": result.score,
        }

    def _get_mime_type(self, file_path: str) -> str:
        """根据文件扩展名获取MIME类型"""
        ext = Path(file_path).suffix.lower()
        mime_types = {
            ".py": "text/x-python",
            ".js": "text/javascript",
            ".ts": "text/typescript",
            ".java": "text/x-java",
            ".c": "text/x-c",
            ".cpp": "text/x-c++",
            ".h": "text/x-c",
            ".hpp": "text/x-c++",
            ".rs": "text/x-rust",
            ".ets": "text/x-arkts",
            ".md": "text/markdown",
            ".json": "application/json",
            ".xml": "text/xml",
            ".html": "text/html",
            ".css": "text/css",
        }
        return mime_types.get(ext, "text/plain")

    def _generate_tags(self, chunk, result) -> str:
        """生成标签字符串"""
        tags = []

        # 添加符号名称
        if result.symbol_name:
            tags.append(result.symbol_name)

        # 添加分片类型
        if result.chunk_type:
            tags.append(result.chunk_type)

        # 添加关键词
        if chunk and chunk.keywords:
            tags.extend(chunk.keywords[:10])  # 限制关键词数量

        # 添加文件类型
        file_ext = Path(result.file_path).suffix.lower()
        if file_ext:
            tags.append(file_ext.replace(".", "") + " file")

        # 添加一些通用标签
        tags.extend(["代码片段", "code snippet", "源代码", "source code"])

        return ", ".join(tags)

    def _generate_codebase_tree_with_insights(
        self, include: list[str] | None = None, exclude: list[str] | None = None
    ) -> dict:
        """生成包含code insight的文件树（文件粒度摘要）"""
        if (
            not hasattr(self.retrieval_engine, "repo_index")
            or not self.retrieval_engine.repo_index
        ):
            raise Exception("索引未初始化")

        repo_index = self.retrieval_engine.repo_index

        # 归一化过滤模式
        include_patterns = [
            p.strip() for p in (include or []) if isinstance(p, str) and p.strip()
        ]
        exclude_patterns = [
            p.strip() for p in (exclude or []) if isinstance(p, str) and p.strip()
        ]

        def matches_any(path_str: str, patterns: list[str]) -> bool:
            if not patterns:
                return False
            normalized = path_str.replace("\\", "/")
            for pattern in patterns:
                # 使用PurePosixPath以支持 ** 递归匹配
                if PurePosixPath(normalized).match(pattern):
                    return True
            return False

        # 先确定需要展示的文件集合（按 include/exclude 过滤）
        file_level_insights: dict[str, str] = {}
        file_count_with_insight = 0
        all_file_paths = list(repo_index.file_chunks.keys())
        filtered_file_paths: list[str] = []
        for rel_path in all_file_paths:
            if include_patterns and not matches_any(rel_path, include_patterns):
                continue
            if exclude_patterns and matches_any(rel_path, exclude_patterns):
                continue
            filtered_file_paths.append(rel_path)

        # 从索引读取已生成好的文件级洞察；若缺失则退化为本地快速构造
        for rel_path in filtered_file_paths:
            fin = repo_index.get_file_insight(rel_path)
            if not fin:
                # 本地兜底：基于该文件的chunks构造
                chunks = repo_index.get_chunks_by_file(rel_path)
                fin = self._build_file_insight(rel_path, chunks)
            file_level_insights[rel_path] = fin

        # 构建文件树结构
        tree_structure = {}
        total_nodes = 0

        for file_path in filtered_file_paths:
            parts = file_path.split("/")
            current = tree_structure

            # 构建目录结构
            for i, part in enumerate(parts):
                if part not in current:
                    current[part] = {} if i < len(parts) - 1 else None
                    total_nodes += 1

                if i < len(parts) - 1:  # 不是最后一个部分（即目录）
                    current = current[part]

        # 生成树形字符串
        def build_tree_string(structure, prefix="", current_path=""):
            lines = []
            if not structure:
                return lines

            # 分离文件夹和文件，并分别排序
            folders = []
            files = []

            for name, children in structure.items():
                if children is None:  # 文件
                    files.append(name)
                else:  # 文件夹
                    folders.append(name)

            # 按名称排序
            folders.sort()
            files.sort()

            # 合并：先文件夹，后文件
            sorted_items = [(name, structure[name]) for name in folders] + [
                (name, structure[name]) for name in files
            ]

            for i, (name, children) in enumerate(sorted_items):
                is_last_item = i == len(sorted_items) - 1
                current_prefix = "└── " if is_last_item else "├── "

                # 构建当前路径
                new_path = f"{current_path}/{name}" if current_path else name

                # 检查是否是文件（children为None）
                if children is None:
                    # 这是一个文件，添加文件粒度 insight 信息
                    fin = file_level_insights.get(new_path, "").strip()
                    if fin:
                        # 展示完整文件级洞察（不截断）
                        lines.append(f"{prefix}{current_prefix}{name}  # {fin}")
                    else:
                        lines.append(f"{prefix}{current_prefix}{name}")
                else:
                    # 这是一个目录
                    lines.append(f"{prefix}{current_prefix}{name}/")

                    # 递归处理子目录
                    next_prefix = prefix + ("    " if is_last_item else "│   ")
                    child_lines = build_tree_string(children, next_prefix, new_path)
                    lines.extend(child_lines)

            return lines

        # 计算有insight的文件数量
        for _, fin in file_level_insights.items():
            if fin and fin.strip():
                file_count_with_insight += 1

        tree_lines = build_tree_string(tree_structure)
        tree_string = self.retrieval_engine.repo_path + "\n" + "\n".join(tree_lines)

        logger.info(f"生成仓库洞察")

        return {
            "tree": tree_string,
            "file_count": file_count_with_insight,
            "total_nodes": total_nodes,
        }

    def _build_file_insight(self, file_path: str, chunks: list) -> str:
        """基于该文件的 chunk 元数据构造简明的文件级洞察描述。
        - 统计 chunk 类型数量（函数/类/方法/文档片段等）
        - 提取代表性符号名称（前若干个）
        - 指示语言与可能的文件角色（如测试/配置）
        """
        try:
            if not chunks:
                return ""

            # 语言与基本信息
            language = getattr(chunks[0], "language", "") or ""

            # 统计类型
            type_counts: dict[str, int] = {}
            symbol_names: list[str] = []
            for c in chunks:
                ctype = getattr(c, "chunk_type", "") or "other"
                type_counts[ctype] = type_counts.get(ctype, 0) + 1
                sname = getattr(c, "symbol_name", "")
                if sname:
                    symbol_names.append(sname)

            # 精选符号名（去重，保持顺序）
            seen = set()
            unique_symbols: list[str] = []
            for s in symbol_names:
                if s not in seen:
                    seen.add(s)
                    unique_symbols.append(s)
                if len(unique_symbols) >= 4:
                    break

            # 文件角色猜测
            fname = os.path.basename(file_path).lower()
            role_hints: list[str] = []
            if any(k in fname for k in ["test", "spec", "tests"]):
                role_hints.append("测试")
            if any(k in fname for k in ["config", "cfg", "conf", "settings", "rc"]):
                role_hints.append("配置")
            if any(k in fname for k in ["util", "helper", "common"]):
                role_hints.append("工具")

            # 组织类型摘要
            type_parts = []
            mapping = {
                "function": "函数",
                "class": "类",
                "method": "方法",
                "markdown_section": "文档",
                "interface": "接口",
                "other": "片段",
            }
            for k in sorted(type_counts.keys()):
                zh = mapping.get(k, k)
                type_parts.append(f"{type_counts[k]}个{zh}")

            parts: list[str] = []
            if language:
                parts.append(f"{language} 文件")
            if type_parts:
                parts.append("，包含" + "、".join(type_parts))
            if unique_symbols:
                parts.append("；符号示例：" + ", ".join(unique_symbols))
            if role_hints:
                parts.append("；角色：" + "/".join(role_hints))

            return "".join(parts)
        except Exception:
            return ""

    def _format_codebase_results(self, result_data: dict) -> str:
        """格式化代码库查询结果为友好的文本格式
        Args:
            result_data: 查询结果数据
        Returns:
            str: 格式化后的结果字符串
        """
        query = result_data.get("query", "")
        results = result_data.get("results", [])

        if not results:
            return f"🔍 查询: {query}\n\n❌ 未找到相关的代码片段"

        formatted_output = [f"🔍 查询: {query}"]
        formatted_output.append(f"📊 找到 {len(results)} 个相关结果\n")

        for i, result in enumerate(results, 1):
            # 提取结果信息
            file_path = result.get("file_path", "未知文件")
            text = result.get("text", "无内容")
            score = result.get("score", 0)
            metadata = result.get("metadata", {})

            # 提取元数据
            start_line = metadata.get("start_line", 0)
            end_line = metadata.get("end_line", start_line)
            category = metadata.get("category", "")
            symbol_name = metadata.get("symbol_name", "")
            file_type = (
                metadata.get("file_type", "").split("/")[-1]
                if metadata.get("file_type")
                else ""
            )

            # 格式化单个结果
            result_section = [f"结果 {i}:"]
            result_section.append(f"文件: {file_path}")

            if start_line and end_line:
                if start_line == end_line:
                    result_section.append(f"位置: 第{start_line}行")
                else:
                    result_section.append(f"位置: 第{start_line}-{end_line}行")

            if category:
                result_section.append(f"索引类型: {category}")

            if symbol_name:
                result_section.append(f"符号名称: {symbol_name}")

            if file_type:
                result_section.append(f"文件类型: {file_type}")

            result_section.append(f"相关度: {score:.3f}")
            result_section.append("代码内容:")
            result_section.append("```")

            # 处理代码内容
            text = text.replace("\r", "")
            code_lines = text.split("\n")
            for line in code_lines:
                result_section.append(f"{line}")

            result_section.append("```")
            result_section.append("")  # 空行分隔

            formatted_output.extend(result_section)

        return "\n".join(formatted_output)

    def _format_results_as_json_string(self, query: str, results: list) -> str:
        """将结果格式化为JSON字符串，匹配期望的输出格式"""
        detailed_results = [self._convert_retrieval_result_to_dict(r) for r in results]

        response = {"query": query, "results": detailed_results}

        import json

        return json.dumps(response, ensure_ascii=False, indent=2)

    def run(self, host: str = None, port: int = None):
        """运行服务"""
        import uvicorn

        host = host or SERVICE_CONFIG["host"]
        port = port or SERVICE_CONFIG["port"]

        logger.info(f"启动代码库检索服务: http://{host}:{port}")

        uvicorn.run(self.app, host=host, port=port, workers=SERVICE_CONFIG["workers"])


# 请求和响应模型的扩展
class DetailedQueryRequest(BaseModel):
    """详细查询请求"""

    query: str = Field(..., description="查询内容")
    top_k: int = Field(default=10, description="返回结果数量")
    languages: list | None = Field(default=None, description="限制编程语言")
    chunk_types: list | None = Field(default=None, description="限制分片类型")
    file_pattern: str | None = Field(default=None, description="文件路径模式")
    use_llm_filter: bool = Field(default=True, description="是否使用LLM筛选")
    embedding_weight: float = Field(default=0.7, description="嵌入检索权重")


class SearchStats(BaseModel):
    """搜索统计"""

    ripgrep_results: int
    embedding_results: int
    llm_filtered_results: int
    final_results: int
    execution_stages: dict


class DetailedQueryResponse(BaseModel):
    """详细查询响应"""

    results: list[RetrievalResult]
    total_time_ms: float
    query: str
    total_chunks: int
    search_stats: SearchStats


# 创建全局API实例（符号索引由 main.py 在启动时注入）
api = CodebaseRetrievalAPI()
app = api.app  # 导出FastAPI应用实例
